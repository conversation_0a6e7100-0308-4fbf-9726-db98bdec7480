@extends('layouts.app')

@section('title', $project->meta_title ?: $project->title)
@section('meta_description', $project->meta_description ?: $project->description)
@section('meta_keywords', $project->meta_keywords)

@section('body')

    <!-- Project Details Hero Section -->
    <section class="project-details-hero-section">
        <div class="hero-background">
            <div class="hero-overlay"></div>
            <img src="{{ asset('storage/' . $project->first_image) }}" alt="{{ $project->title }}" class="hero-image">
        </div>
        <div class="container">
            <div class="hero-content">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('projects') }}">Projects</a></li>
                        <li class="breadcrumb-item active">{{ $project->title }}</li>
                    </ol>
                </nav>
                
                <div class="project-badges">
                    <span class="project-badge">{{ $project->location }}</span>
                    <span class="project-status">{{ $project->location }}</span>
                </div>
                
                <h1 class="project-title text-white">{{ $project->title }}</h1>
                <div class="project-location">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ $project->location }}
                </div>
                
                <div class="project-price">
                    <span class="price-label">Starting from</span>
                    <span class="price-value">{{ $project->location }}</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Overview Section -->
    <section class="project-overview-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="project-content">
                        <h2>Project Overview</h2>
                        <p class="project-description">{{ $project->description }}</p>
                        
                        <div class="project-highlights">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="highlight-item">
                                        <div class="highlight-icon">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="highlight-content">
                                            <h4>Configurations</h4>
                                            <p><?php echo implode(', ', $project->configurations); ?></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="highlight-item">
                                        <div class="highlight-icon">
                                            <i class="fas fa-ruler-combined"></i>
                                        </div>
                                        <div class="highlight-content">
                                            <h4>Area Range</h4>
                                            <p>{{ $project->area_range }}</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="highlight-item">
                                        <div class="highlight-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="highlight-content">
                                            <h4>Possession</h4>
                                            <p> {{ $project->possession }}</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="highlight-item">
                                        <div class="highlight-icon">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                        <div class="highlight-content">
                                            <h4>RERA Number</h4>
                                            <p> {{ $project->rera_number }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Amenities Section -->
                        <div class="amenities-section">
                            <h3>Amenities & Features</h3>
                            <div class="amenities-grid">
                                @foreach($project->amenities as $amenity)
                                    <div class="amenity-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>{{ $amenity }}</span>
                                    </div>
                               @endforeach
                            </div>
                        </div>
                        
                        <!-- Gallery Section -->
                        <div class="gallery-section">
                            <h3>Project Gallery</h3>
                            <div class="project-gallery">
                                @foreach($project->images as $image)
                                    <div class="gallery-item">
                                        <img src="{{ asset('storage/' . $image) }}" alt="{{ $project['title']; }} Image" class="img-fluid">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="project-sidebar">
                        <!-- Enquiry Form -->
                        <div class="sidebar-widget enquiry-widget">
                            <h4>Interested in this project?</h4>

                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            @endif

                            @if(session('error'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session('error') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            @endif

                            <form class="enquiry-form" action="{{ route('project-enquiry.store') }}" method="POST">
                                @csrf
                                <input type="hidden" name="project_id" value="{{ $project->id }}">
                                <input type="hidden" name="project_name" value="{{ $project->title }}">

                                <div class="form-group">
                                    <input type="text" name="name" class="form-control @error('name') is-invalid @enderror"
                                           placeholder="Your Name *" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <input type="email" name="email" class="form-control @error('email') is-invalid @enderror"
                                           placeholder="Email Address *" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <input type="tel" name="phone" class="form-control @error('phone') is-invalid @enderror"
                                           placeholder="Phone Number *" value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <select name="interest" class="form-control @error('interest') is-invalid @enderror" required>
                                        <option value="">I'm interested in...</option>
                                        <option value="site_visit" {{ old('interest') == 'site_visit' ? 'selected' : '' }}>Site Visit</option>
                                        <option value="price_details" {{ old('interest') == 'price_details' ? 'selected' : '' }}>Price Details</option>
                                        <option value="floor_plans" {{ old('interest') == 'floor_plans' ? 'selected' : '' }}>Floor Plans</option>
                                        <option value="loan_assistance" {{ old('interest') == 'loan_assistance' ? 'selected' : '' }}>Loan Assistance</option>
                                    </select>
                                    @error('interest')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <textarea name="message" class="form-control @error('message') is-invalid @enderror"
                                              rows="3" placeholder="Your Message">{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <button type="submit" class="btn btn-primary btn-block">
                                    Send Enquiry
                                </button>
                            </form>
                        </div>
                        
                        <!-- Quick Contact -->
                        <div class="sidebar-widget contact-widget">
                            <h4>Quick Contact</h4>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <div>
                                        <span>Call Us</span>
                                        <a href="tel:+919067881848">+91 ************</a>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <div>
                                        <span>Email Us</span>
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div>
                                        <span>Visit Us</span>
                                        <span>Pune, Maharashtra</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Related Projects -->
                         @if($relatedProjects->count() > 0)
                            <div class="sidebar-widget related-projects-widget">
                            <h4>Similar Projects</h4>
                            <div class="related-projects">
                              
                                @foreach($relatedProjects as $relatedProject)
                                 
                                        <div class="related-project">
                                            <div class="related-project-image">
                                            @if($relatedProject->first_image)
                                                <img src="{{ asset('storage/' . $relatedProject->first_image) }}" alt="{{ $relatedProject->title }}">
                                            @endif
                                        </div>
                                        <div class="related-project-content">
                                        <h5><a href="{{ route('project-details', $relatedProject->slug) }}">{{ $relatedProject->title }}</a></h5>
                                        <div class="related-project-location">
                                        <i class="fas fa-map-marker-alt"></i> {{ $relatedProject->location }}
                                        </div>
                                        <div class="related-project-price"> {{ $relatedProject->price_range }} </div>
                                        
                                    
                                @endforeach
                                
                            </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="project-cta-section">
        <div class="container">
            <div class="cta-content text-center">
                <h2>Ready to Make This Your Home?</h2>
                <p>Schedule a site visit or get detailed information about {{  $project->title }}</p>
                <div class="cta-buttons">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        Schedule Site Visit
                    </a>
                    <a href="tel:+919067881848" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone"></i> Call Now
                    </a>
                </div>
            </div>
        </div>
    </section>

  

@endsection
