@extends('layouts.app')

@section('title', 'Careers - Join Our Team | Hestia Abodes')

@section('body')

<!-- Careers Hero Section -->
@include('partials.page-header', [
    'sectionClass' => 'careers-hero-section',
    'fallbackTitle' => 'Join Our Team',
    'fallbackSubtitle' => 'Build Your Career in Real Estate Excellence'
])

<!-- Career Overview Section -->
<section class="career-overview-section">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">WHY JOIN US</div>
            <h2 class="section-title">Build Your Career With Excellence</h2>
            <p class="section-subtitle">At Hestia Abodes, we believe in nurturing talent and providing opportunities for growth. Join our dynamic team and be part of Pune's leading real estate consultancy.</p>
        </div>

        <div class="career-benefits-grid">
            <div class="benefit-item" data-aos="fade-up" data-aos-delay="100">
                <div class="benefit-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="benefit-content">
                    <h4>Growth Opportunities</h4>
                    <p>Advance your career with continuous learning and development programs tailored to your aspirations.</p>
                </div>
            </div>

            <div class="benefit-item" data-aos="fade-up" data-aos-delay="200">
                <div class="benefit-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="benefit-content">
                    <h4>Great Team Culture</h4>
                    <p>Work with passionate professionals in a collaborative environment that values innovation and excellence.</p>
                </div>
            </div>

            <div class="benefit-item" data-aos="fade-up" data-aos-delay="300">
                <div class="benefit-icon">
                    <i class="fas fa-award"></i>
                </div>
                <div class="benefit-content">
                    <h4>Competitive Benefits</h4>
                    <p>Enjoy competitive salary packages, comprehensive benefits, and performance-based incentives.</p>
                </div>
            </div>

            <div class="benefit-item" data-aos="fade-up" data-aos-delay="400">
                <div class="benefit-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="benefit-content">
                    <h4>Learning & Development</h4>
                    <p>Access to industry training, certifications, and skill development programs to enhance your expertise.</p>
                </div>
            </div>

            <div class="benefit-item" data-aos="fade-up" data-aos-delay="500">
                <div class="benefit-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="benefit-content">
                    <h4>Work-Life Balance</h4>
                    <p>Flexible working arrangements and supportive policies that prioritize your well-being and productivity.</p>
                </div>
            </div>

            <div class="benefit-item" data-aos="fade-up" data-aos-delay="600">
                <div class="benefit-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="benefit-content">
                    <h4>Innovation Focus</h4>
                    <p>Be part of cutting-edge real estate solutions and contribute to industry-leading practices and technologies.</p>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Career Application Form Section -->
<section class="career-application-section">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">APPLY NOW</div>
            <h2 class="section-title">Start Your Journey With Us</h2>
            <p class="section-subtitle">Fill out the form below to submit your application. We'll review your profile and get back to you soon.</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="application-form-wrapper"  data-aos="fade-up" data-aos-delay="200">
                    
                    <form id="careerApplicationForm" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="mobile" class="form-label">Mobile Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="mobile" name="mobile" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position Applied For <span class="text-danger">*</span></label>
                                <select class="form-select" id="position" name="position" required>
                                    <option value="">Select Position</option>
                                    <option value="Sales Executive">Sales Executive</option>
                                    <option value="Sales Manager">Sales Manager</option>
                                    <option value="Marketing Executive">Marketing Executive</option>
                                    <option value="Business Development Manager">Business Development Manager</option>
                                    <option value="Customer Relationship Manager">Customer Relationship Manager</option>
                                    <option value="Property Consultant">Property Consultant</option>
                                    <option value="Digital Marketing Specialist">Digital Marketing Specialist</option>
                                    <option value="Administrative Assistant">Administrative Assistant</option>
                                    <option value="Finance Executive">Finance Executive</option>
                                    <option value="HR Executive">HR Executive</option>
                                    <option value="Other">Other</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-12 mb-3">
                                <label for="resume" class="form-label">Resume/CV <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="resume" name="resume" accept=".pdf,.doc,.docx" required>
                                <div class="form-text">Upload your resume in PDF, DOC, or DOCX format (Max: 5MB)</div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-12 mb-4">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control" id="message" name="message" rows="4" maxlength="500" placeholder="Tell us why you're interested in this position and what makes you a great fit..."></textarea>
                                <div class="form-text">
                                    <span class="character-count"><span id="messageCount">0</span>/500 characters</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="col-12 text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <span class="btn-text">Submit Application</span>
                                    <span class="btn-spinner d-none">
                                        <i class="fas fa-spinner fa-spin"></i> Submitting...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Success/Error Messages -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>
                <span id="successMessage"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    </div>
    
    <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span id="errorMessage"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* ===== CAREER PAGE STYLES ===== */

/* Career Overview Section */
.career-overview-section {
    padding: 120px 0;
    background: var(--white);
    position: relative;
}

.career-overview-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.02) 0%, rgba(0,0,0,0.05) 100%);
    z-index: 1;
}

.career-overview-section .container {
    position: relative;
    z-index: 2;
}

.career-benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 80px;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 25px;
    padding: 40px;
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(30px);
    border: 1px solid var(--border-color);
}

.benefit-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.benefit-item.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.8s ease-out forwards;
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-dark) 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.benefit-item:hover .benefit-icon {
    transform: scale(1.1) rotate(5deg);
}

.benefit-content h4 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
    line-height: 1.3;
}

.benefit-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    font-size: 1rem;
}

/* Career Stats Section */
.career-stats-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-dark) 100%);
    position: relative;
    overflow: hidden;
}

.career-stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.career-stats-section .container {
    position: relative;
    z-index: 2;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-card {
    text-align: center;
    padding: 40px 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(50px);
}

.stat-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.stat-card.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: statCardSlideIn 0.8s ease-out forwards;
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: var(--white);
    font-size: 2rem;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(10deg);
    background: rgba(255, 255, 255, 0.3);
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 10px;
    line-height: 1;
}

.stat-text {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Career Application Section */
.career-application-section {
    padding: 120px 0;
    background: var(--light-gray);
    position: relative;
}

.career-application-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.02) 0%, transparent 50%, rgba(0,0,0,0.02) 100%);
    z-index: 1;
}

.career-application-section .container {
    position: relative;
    z-index: 2;
}

.application-form-wrapper {
    background: var(--white);
    padding: 60px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    margin-top: 60px;
    position: relative;
    overflow: hidden;
}

.application-form-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--text-dark) 100%);
    z-index: 1;
}

.form-section-title {
    color: var(--text-dark);
    font-size: 1.3rem;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 30px;
    position: relative;
}

.form-section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--text-dark);
}

/* Career Form Controls */
.career-application-section .form-control,
.career-application-section .form-select {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
    width: 100%;
    position: relative;
}

.career-application-section .form-control:focus,
.career-application-section .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    background: var(--white);
    outline: none;
}

.career-application-section .form-text {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-top: 5px;
}

/* Career Form Specific Styles - Override global form-label styles */
.career-application-section .form-label {
    position: static !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    font-weight: 600;
    color: var(--text-dark) !important;
    margin-bottom: 8px;
    font-size: 0.95rem !important;
    display: block;
    transition: none !important;
}

.career-application-section .form-label .text-danger {
    color: #dc3545 !important;
}

/* Form Group Spacing */
.career-application-section .mb-3,
.career-application-section .mb-4 {
    margin-bottom: 1.5rem !important;
}

/* Invalid Feedback */
.career-application-section .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-dark) 100%);
    border: none;
    padding: 18px 50px;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.4s ease;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.toast {
    min-width: 350px;
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.character-count {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .career-overview-section,
    .career-application-section {
        padding: 80px 0;
    }

    .career-stats-section {
        padding: 80px 0;
    }

    .career-benefits-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        margin-top: 60px;
    }

    .benefit-item {
        padding: 30px;
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .stat-card {
        padding: 30px 20px;
    }

    .stat-number {
        font-size: 2.8rem;
    }

    .application-form-wrapper {
        padding: 40px 30px;
        margin-top: 40px;
    }

    .btn-primary {
        padding: 15px 40px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .career-benefits-grid {
        grid-template-columns: 1fr;
    }

    .benefit-item {
        padding: 25px 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .stat-card {
        padding: 25px 15px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .application-form-wrapper {
        padding: 30px 20px;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe benefit items
    document.querySelectorAll('.benefit-item').forEach(item => {
        observer.observe(item);
    });

    // Observe stat cards
    document.querySelectorAll('.stat-card').forEach(item => {
        observer.observe(item);
    });

    // Observe form wrapper
    const formWrapper = document.querySelector('.application-form-wrapper');
    if (formWrapper) {
        observer.observe(formWrapper);
    }

    // Counter animation for stats
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);

        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        }
        updateCounter();
    }

    // Trigger counter animation when stats section is visible
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.querySelector('.stat-number');
                const targetValue = parseInt(statNumber.getAttribute('data-count'));
                animateCounter(statNumber, targetValue);
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    document.querySelectorAll('.stat-card').forEach(card => {
        statsObserver.observe(card);
    });

    // Form handling
    const form = document.getElementById('careerApplicationForm');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnSpinner = submitBtn.querySelector('.btn-spinner');
    const successToast = new bootstrap.Toast(document.getElementById('successToast'));
    const errorToast = new bootstrap.Toast(document.getElementById('errorToast'));

    // Character counter for message
    const messageTextarea = document.getElementById('message');
    const messageCount = document.getElementById('messageCount');

    messageTextarea.addEventListener('input', function() {
        messageCount.textContent = this.value.length;
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Clear previous errors
        clearErrors();

        // Show loading state
        submitBtn.disabled = true;
        btnText.classList.add('d-none');
        btnSpinner.classList.remove('d-none');

        // Create FormData object
        const formData = new FormData(form);

        // Submit form
        fetch('{{ route("careers.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                document.getElementById('successMessage').textContent = data.message;
                successToast.show();

                // Reset form
                form.reset();
                messageCount.textContent = '0';
            } else {
                // Show errors
                if (data.errors) {
                    showErrors(data.errors);
                } else {
                    document.getElementById('errorMessage').textContent = data.message || 'An error occurred. Please try again.';
                    errorToast.show();
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('errorMessage').textContent = 'An error occurred. Please try again.';
            errorToast.show();
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            btnText.classList.remove('d-none');
            btnSpinner.classList.add('d-none');
        });
    });

    function clearErrors() {
        const errorElements = form.querySelectorAll('.is-invalid');
        errorElements.forEach(element => {
            element.classList.remove('is-invalid');
        });

        const feedbackElements = form.querySelectorAll('.invalid-feedback');
        feedbackElements.forEach(element => {
            element.textContent = '';
        });
    }

    function showErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');
                const feedback = input.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.textContent = messages[0];
                }
            }
        }
    }
});
</script>
@endpush
