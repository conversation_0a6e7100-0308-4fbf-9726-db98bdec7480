@extends('layouts.app')

@section('title', 'Sitemap - Hestia Abodes Real Estate Consultancy')
@section('meta_description', 'Complete sitemap of Hestia Abodes website. Find all pages, projects, and blog articles in one organized location.')
@section('meta_keywords', 'sitemap, website map, Hestia Abodes, real estate pages, projects, blog articles')
@section('canonical_url', 'https://hestiaabodes.in/sitemap')

@section('body')
    <!-- Page Header -->
    <section class="page-header" style="background-image: url('{{ asset('images/default-header.jpg') }}');">
        <div class="page-header-overlay"></div>
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="page-header-content text-center">
                        <h1 class="page-title">Sitemap</h1>
                        <p class="page-subtitle">Navigate through all pages of our website</p>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb justify-content-center">
                                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Sitemap</li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sitemap Content -->
    <section class="sitemap-section py-5">
        <div class="container">
            <div class="row">
                <!-- Main Pages -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="sitemap-category">
                        <h3 class="sitemap-title">
                            <i class="fas fa-home"></i>
                            Main Pages
                        </h3>
                        <ul class="sitemap-list">
                            @foreach($staticPages as $page)
                                <li>
                                    <a href="{{ $page['url'] }}">
                                        <i class="fas fa-chevron-right"></i>
                                        {{ $page['name'] }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>

                <!-- Projects -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="sitemap-category">
                        <h3 class="sitemap-title">
                            <i class="fas fa-building"></i>
                            Projects ({{ $projects->count() }})
                        </h3>
                        <ul class="sitemap-list">
                            @foreach($projects->take(10) as $project)
                                <li>
                                    <a href="{{ route('project-details', $project->slug) }}">
                                        <i class="fas fa-chevron-right"></i>
                                        {{ $project->title }}
                                    </a>
                                </li>
                            @endforeach
                            @if($projects->count() > 10)
                                <li class="view-all">
                                    <a href="{{ route('projects') }}">
                                        <i class="fas fa-plus"></i>
                                        View All {{ $projects->count() }} Projects
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>

                <!-- Blog Articles -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="sitemap-category">
                        <h3 class="sitemap-title">
                            <i class="fas fa-blog"></i>
                            Blog Articles ({{ $blogs->count() }})
                        </h3>
                        <ul class="sitemap-list">
                            @foreach($blogs->take(10) as $blog)
                                <li>
                                    <a href="{{ route('blog-details', $blog->slug) }}">
                                        <i class="fas fa-chevron-right"></i>
                                        {{ $blog->title }}
                                    </a>
                                </li>
                            @endforeach
                            @if($blogs->count() > 10)
                                <li class="view-all">
                                    <a href="{{ route('blog') }}">
                                        <i class="fas fa-plus"></i>
                                        View All {{ $blogs->count() }} Articles
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>

            <!-- XML Sitemap Link -->
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <div class="xml-sitemap-info">
                        <h4>For Search Engines</h4>
                        <p>Access our XML sitemap for search engine crawlers:</p>
                        <a href="{{ route('sitemap.xml') }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i>
                            Download XML Sitemap
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
.sitemap-section {
    background: #f8f9fa;
    min-height: 60vh;
}

.sitemap-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    height: 100%;
    transition: all 0.3s ease;
}

.sitemap-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.sitemap-title {
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #000;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sitemap-title i {
    color: #000;
    font-size: 1.1rem;
}

.sitemap-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sitemap-list li {
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.sitemap-list li:last-child {
    border-bottom: none;
}

.sitemap-list a {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.sitemap-list a:hover {
    color: #000;
    padding-left: 10px;
}

.sitemap-list a i {
    font-size: 0.8rem;
    color: #000;
    transition: all 0.3s ease;
}

.sitemap-list a:hover i {
    transform: translateX(3px);
}

.view-all a {
    color: #000 !important;
    font-weight: 600;
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.view-all a:hover {
    background: #000;
    color: white !important;
    padding-left: 20px;
}

.xml-sitemap-info {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.xml-sitemap-info h4 {
    color: #333;
    margin-bottom: 1rem;
}

.xml-sitemap-info p {
    color: #666;
    margin-bottom: 1.5rem;
}

.page-header {
    background-color: #000;
    color: white;
    padding: 6rem 0 4rem;
    position: relative;
    z-index: 1;
}

.page-header-overlay {
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.page-header-content {
    position: relative;
    z-index: 3;
}

.page-title {
    color: white;
    font-weight: 700;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.9);
}

.breadcrumb {
    background: transparent;
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

@media (max-width: 768px) {
    .sitemap-category {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .sitemap-title {
        font-size: 1.1rem;
    }
    
    .xml-sitemap-info {
        padding: 1.5rem;
    }
}
</style>
@endpush
