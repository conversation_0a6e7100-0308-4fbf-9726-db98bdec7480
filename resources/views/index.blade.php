@extends('layouts.app')

@section('title', 'Hestia Abodes - Premium Real Estate Consultancy in Pune')
@section('meta_description', 'Hestia Abodes is Pune\'s trusted real estate consultancy with 21+ years of experience. Find your dream home or investment property with expert guidance.')
@section('meta_keywords', 'Pune real estate, property consultancy, real estate agents Pune, buy property Pune, investment properties')
@section('canonical_url', 'https://hestiaabodes.in/')

@section('body')
    <!-- Hero Slider Section -->
    <section id="home" class="hero-section">
        @if($heroSliders->count() > 0)
            <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
                @if($heroSliders->count() > 1)
                    <div class="carousel-indicators">
                        @foreach($heroSliders as $index => $slider)
                            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ $index }}"
                                    class="{{ $index === 0 ? 'active' : '' }}"></button>
                        @endforeach
                    </div>
                @endif

                <div class="carousel-inner">
                    @foreach($heroSliders as $index => $slider)
                        <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                            <div class="hero-slide" style="background-image: url('{{ $slider->background_image_url }}');">
                                <div class="hero-overlay"></div>
                                <div class="container">
                                    <div class="hero-content">
                                        <h1 class="hero-title">{!! nl2br(e($slider->title)) !!}</h1>
                                        @if($slider->subtitle)
                                            <p class="hero-subtitle">{{ $slider->subtitle }}</p>
                                        @endif
                                        @if($slider->description)
                                            <div class="hero-description">
                                                {!! $slider->description !!}
                                            </div>
                                        @endif
                                        @if($slider->button_text && $slider->button_link)
                                            <div class="hero-buttons">
                                                <a href="{{ $slider->button_link }}" class="btn btn-primary">
                                                    {{ $slider->button_text }}
                                                </a>
                                                <a href="{{ route('contact') }}" class="btn btn-outline-light">Contact Us</a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                @if($heroSliders->count() > 1)
                    <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                @endif
            </div>
        @else
            <!-- Fallback content when no sliders are available -->
            <div class="hero-slide" style="background-image: url('images/real.jpg');">
                <div class="hero-overlay"></div>
                <div class="container">
                    <div class="hero-content">
                        <h1 class="hero-title">Premium Real Estate<br>Consultancy in Pune</h1>
                        <p class="hero-subtitle">Discover exceptional properties with our expert guidance and personalized service</p>
                        <div class="hero-buttons">
                            <a href="#properties" class="btn btn-primary">View Properties</a>
                            <a href="#contact" class="btn btn-outline-light">Contact Us</a>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </section>

    <!-- About Us Section -->
    <section id="about" class="index-about-section">
        <div class="container">
            <div class="index-about-split">
                <!-- Left Side - About Content -->
                <div class="index-about-content-side">
                    <div class="index-content-wrapper">
                        <div class="index-section-badge">ABOUT US</div>
                        <h2 class="index-about-title">Building Dreams,<br>Creating Homes</h2>
                        <div class="index-about-description">
                            <p>
                                Hestia Abodes is a premium real estate consultancy firm based in Pune, committed to helping homebuyers and investors make confident, informed decisions in an ever-evolving property market.
                            </p>
                            <p>
                                As trusted Channel Partners (CPs), we bridge the gap between buyers and top-tier developers by offering personalized, transparent, and end-to-end real estate advisory services.
                            </p>
                        </div>

                        <div class="index-about-features">
                            <div class="index-feature-item">
                                <div class="index-feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="index-feature-content">
                                    <h4>Trusted Expertise</h4>
                                    <p>15+ years of proven track record in Pune's real estate market</p>
                                </div>
                            </div>

                            <div class="index-feature-item">
                                <div class="index-feature-icon">
                                    <i class="fas fa-handshake"></i>
                                </div>
                                <div class="index-feature-content">
                                    <h4>Personalized Service</h4>
                                    <p>Tailored solutions that match your unique requirements</p>
                                </div>
                            </div>

                            <div class="index-feature-item">
                                <div class="index-feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="index-feature-content">
                                    <h4>Trusted Advisory</h4>
                                    <p>Expert guidance backed by 15+ years of market expertise</p>
                                </div>
                            </div>
                        </div>

                        <div class="index-about-cta">
                            <a href="#contact" class="btn btn-primary">Get Free Consultation</a>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Statistics Cards -->
                <div class="index-about-stats-side">
                    <div class="index-stats-container">
                        <div class="index-stats-grid">
                            <!-- Card 1: Years in Business -->
                            <div class="index-stat-card index-card-dark" data-count="15">
                                <div class="index-card-background">
                                    <div class="index-card-pattern"></div>
                                </div>
                                <div class="index-card-content">
                                    <div class="index-stat-number">15</div>
                                    <div class="index-stat-label">YEARS IN BUSINESS</div>
                                    <div class="index-stat-description">Serving Pune since 2009</div>
                                </div>
                            </div>

                            <!-- Card 2: Projects Completed -->
                            <div class="index-stat-card index-card-dark" data-count="500">
                                <div class="index-card-background">
                                    <div class="index-card-pattern"></div>
                                </div>
                                <div class="index-card-content">
                                    <div class="index-stat-number">500</div>
                                    <div class="index-stat-label">PROJECTS COMPLETED</div>
                                    <div class="index-stat-description">Successful deliveries</div>
                                </div>
                            </div>

                            <!-- Card 3: Happy Clients -->
                            <div class="index-stat-card index-card-dark" data-count="5000">
                                <div class="index-card-background">
                                    <div class="index-card-pattern"></div>
                                </div>
                                <div class="index-card-content">
                                    <div class="index-stat-number">5000+</div>
                                    <div class="index-stat-label">HAPPY CLIENTS</div>
                                    <div class="index-stat-description">Satisfied families</div>
                                </div>
                            </div>

                            <!-- Card 4: Premium Properties -->
                            <div class="index-stat-card index-card-dark" data-target="50">
                                <div class="index-card-background">
                                    <div class="index-card-pattern"></div>
                                </div>
                                <div class="index-card-content">
                                    <div class="index-stat-number">50+</div>
                                    <div class="index-stat-label">PREMIUM PROPERTIES</div>
                                    <div class="index-stat-description">Luxury developments</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services-section d-none">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title">Our SERVICES</h2>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-image">
                        <img src="images/real.jpg" alt="Real Estate Investing">
                    </div>
                    <div class="service-content">
                        <h4>REAL ESTATE INVESTING</h4>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-image">
                        <img src="images/real1.jpg" alt="Property Valuation">
                    </div>
                    <div class="service-content">
                        <h4>PROPERTY VALUATION EXPERTISE</h4>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-image">
                        <img src="images/real3.jpg" alt="Real Estate Solutions">
                    </div>
                    <div class="service-content">
                        <h4>REAL ESTATE SOLUTIONS</h4>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Properties Section -->
    <section id="properties" class="index-properties-section">
        <div class="container">
            <div class="index-section-header text-center">
                <h2 class="index-section-title">Featured PROPERTIES</h2>
            </div>

            @if($featuredProjects->count() > 0)
                @foreach($featuredProjects as $index => $project)
                    <!-- Property {{ $index + 1 }} - {{ $index % 2 == 0 ? 'Content Left, Image Right' : 'Image Left, Content Right' }} -->
                    <div class="index-property-showcase {{ $index % 2 == 1 ? 'index-property-reverse' : '' }}" data-aos="fade-up">
                        <div class="row align-items-center">
                            <div class="col-lg-6">
                                <div class="index-property-info">
                                    <div class="index-property-number">NO. {{ $index + 1 }}</div>
                                    <h3 class="index-property-name">{{ strtoupper($project->title) }}</h3>
                                    <div class="index-property-type">{{ strtoupper($project->projectCategory->name) }}</div>
                                    <div class="index-property-location">{{ $project->location }}</div>
                                    <div class="index-property-description">
                                        {{ Str::limit($project->description, 150) }}
                                    </div>
                                    <div class="index-property-price">{{ $project->price_range }}</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="index-property-image-main">
                                    @if($project->first_image)
                                        <img src="{{ asset('storage/' . $project->first_image) }}" alt="{{ $project->title }}">
                                    @else
                                        <img src="{{ asset('images/real' . (($index % 3) + 1) . '.jpg') }}" alt="{{ $project->title }}">
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <!-- Fallback content when no featured projects are available -->
                <div class="index-property-showcase" data-aos="fade-up">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="index-property-info">
                                <div class="index-property-number">NO. 1</div>
                                <h3 class="index-property-name">PREMIUM PROPERTIES</h3>
                                <div class="index-property-type">LUXURY HOMES</div>
                                <div class="index-property-location">Pune</div>
                                <div class="index-property-description">
                                    Discover exceptional properties with our expert guidance and personalized service.
                                    We offer premium residential and commercial properties in prime locations.
                                </div>
                                <div class="index-property-price">Best Prices Available</div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="index-property-image-main">
                                <img src="{{ asset('images/real1.jpg') }}" alt="Premium Properties">
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- View All Properties Button -->
            <div class="text-center mt-5">
                <a href="{{ route('projects') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-building me-2"></i>
                    View All Properties
                </a>
            </div>
        </div>
    </section>

        <!-- Services Overview Section -->
    <section class="services-overview-section">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title">How We Serve You</h2>
                <p class="section-subtitle">Whether you're buying your dream home or optimizing property sales, we provide expert guidance every step of the way.</p>
            </div>
        </div>
    </section>

    <!-- For Home Buyers Section -->
    <section class="home-buyers-section" id="home-buyers">
        <div class="container">
            <!-- Central Header -->
            <div class="section-center-header" data-aos="zoom-in">
                <div class="center-icon-wrapper">
                    <div class="center-icon">
                        <i class="fas fa-home"></i>
                    </div>
                </div>
                <div class="service-badge pulse-animation">FOR HOME BUYERS</div>
                <h2 class="service-title typewriter">Your Ideal Home, Simplified</h2>
            </div>

            <!-- Services Grid 3-3 Layout -->
            <div class="services-grid-layout">
                <!-- Left Side Services -->
                <div class="services-column left-services">
                    <div class="service-item" data-aos="fade-right" data-aos-delay="100" data-number="1">
                        <div class="service-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="service-details">
                            <h4>Personalized Property Advisory</h4>
                            <p>Expert guidance tailored to your specific needs and preferences</p>
                        </div>
                    </div>

                    <div class="service-item" data-aos="fade-right" data-aos-delay="200" data-number="2">
                        <div class="service-icon">
                            <i class="fas fa-list-check"></i>
                        </div>
                        <div class="service-details">
                            <h4>Curated Project Shortlisting & Site Visits</h4>
                            <p>Handpicked properties that match your criteria with guided site visits</p>
                        </div>
                    </div>

                    <div class="service-item" data-aos="fade-right" data-aos-delay="300" data-number="3">
                        <div class="service-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="service-details">
                            <h4>Skilled Negotiation & Deal Support</h4>
                            <p>Professional negotiation to secure the best deals for you</p>
                        </div>
                    </div>
                </div>

                <!-- Center Divider -->
                <div class="center-divider">
                    <div class="divider-line"></div>
                </div>

                <!-- Right Side Services -->
                <div class="services-column right-services">
                    <div class="service-item" data-aos="fade-left" data-aos-delay="400" data-number="4">
                        <div class="service-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <div class="service-details">
                            <h4>Comprehensive Legal & Documentation Assistance</h4>
                            <p>Complete support with legal processes and documentation</p>
                        </div>
                    </div>

                    <div class="service-item" data-aos="fade-left" data-aos-delay="500" data-number="5">
                        <div class="service-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="service-details">
                            <h4>Strategic Investment Advisory</h4>
                            <p>Smart investment strategies for long-term value creation</p>
                        </div>
                    </div>

                    <div class="service-item" data-aos="fade-left" data-aos-delay="600" data-number="6">
                        <div class="service-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="service-details">
                            <h4>Continued Support Beyond Sale</h4>
                            <p>Ongoing assistance even after your property purchase</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="section-bottom" data-aos="fade-up" data-aos-delay="700">
                <!-- Testimonial -->
                <div class="testimonial-box">
                    <div class="testimonial-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="testimonial-text text-white">"Hestia Abodes made home buying effortless, guiding us every step!"</p>
                    <span class="testimonial-author">– Happy Homeowner</span>
                </div>

                <!-- CTA Button -->
                <div class="service-cta">
                    <a href="tel:+************" class="btn btn-primary">
                        <i class="fas fa-phone"></i> Find Your Dream Home
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- For Builders Section -->
    <section class="builders-section" id="builders">
        <div class="container">
            <!-- Central Header -->
            <div class="section-center-header" data-aos="zoom-in">
                <div class="center-icon-wrapper">
                    <div class="center-icon builders-center-icon">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="service-badge builders-badge pulse-animation">FOR BUILDERS</div>
                <h2 class="service-title typewriter">Exclusive Mandates for Optimized Sales</h2>
            </div>

            <!-- Services Grid Layout (Only 3 services for builders) -->
            <div class="builders-services-layout">
                <div class="service-item builders-service-item" data-aos="fade-up" data-aos-delay="100" data-number="1">
                    <div class="service-icon builders-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="service-details">
                        <h4>🚀 Focused Marketing Strategies</h4>
                        <p>Targeted marketing campaigns to reach the right buyers for your projects</p>
                    </div>
                </div>

                <div class="service-item builders-service-item" data-aos="fade-up" data-aos-delay="200" data-number="2">
                    <div class="service-icon builders-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="service-details">
                        <h4>🏆 Dedicated Sales Management</h4>
                        <p>Professional sales team dedicated to maximizing your project's potential</p>
                    </div>
                </div>

                <div class="service-item builders-service-item" data-aos="fade-up" data-aos-delay="300" data-number="3">
                    <div class="service-icon builders-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="service-details">
                        <h4>📈 Maximized Market Positioning</h4>
                        <p>Strategic positioning to ensure optimal market presence and sales velocity</p>
                    </div>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="section-bottom" data-aos="fade-up" data-aos-delay="400">
                <!-- CTA Button -->
                <div class="service-cta">
                    <a href="tel:+************" class="btn btn-outline-primary">
                        <i class="fas fa-phone"></i> Partner With Us
                    </a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Property Gallery Section -->
    <section id="gallery" class="gallery-section">
        <div class="container-fluid">
            <div class="section-header text-center">
                <h2 class="section-title">Property GALLERY</h2>
                <p class="section-subtitle">Explore our stunning collection of premium properties</p>
            </div>

            <!-- Gallery Filter Tabs -->
            <div class="gallery-filters text-center">
                <button class="filter-btn active" data-filter="all">All Properties</button>
                @foreach($projectCategories as $category)
                    <button class="filter-btn" data-filter="{{ $category->slug }}">{{ $category->name }}</button>
                @endforeach
            </div>

            <!-- Creative Gallery Layout -->
            <div class="gallery-creative">
                @if($galleryItems->count() > 0)
                    @foreach($galleryItems as $index => $gallery)
                        @if($index == 0)
                            <!-- Large Featured Image -->
                            <div class="gallery-item gallery-large" data-category="{{ $gallery->projectCategory->slug }}">
                                <div class="gallery-image">
                                    <img src="{{ asset('storage/' . $gallery->image) }}" alt="{{ $gallery->title }}">
                                    <div class="gallery-overlay">
                                        <div class="overlay-content">
                                            <span class="property-tag">FEATURED</span>
                                            <h3>{{ $gallery->title }}</h3>
                                            <p>{{ Str::limit($gallery->description, 80) }}</p>
                                            <div class="property-details">
                                                <span><i class="fas fa-tag"></i> {{ $gallery->projectCategory->name }}</span>
                                            </div>
                                            <a href="{{ route('gallery') }}" class="btn btn-outline-light btn-sm">View Details</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @elseif($index <= 2)
                            <!-- Medium Images -->
                            <div class="gallery-item gallery-medium" data-category="{{ $gallery->projectCategory->slug }}">
                                <div class="gallery-image">
                                    <img src="{{ asset('storage/' . $gallery->image) }}" alt="{{ $gallery->title }}">
                                    <div class="gallery-overlay">
                                        <div class="overlay-content">
                                            <span class="property-tag">{{ strtoupper($gallery->projectCategory->name) }}</span>
                                            <h4>{{ $gallery->title }}</h4>
                                            <p>{{ Str::limit($gallery->description, 50) }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <!-- Small Images -->
                            <div class="gallery-item gallery-small" data-category="{{ $gallery->projectCategory->slug }}">
                                <div class="gallery-image">
                                    <img src="{{ asset('storage/' . $gallery->image) }}" alt="{{ $gallery->title }}">
                                    <div class="gallery-overlay">
                                        <div class="overlay-content">
                                            <h5>{{ $gallery->title }}</h5>
                                            <p>{{ Str::limit($gallery->description, 30) }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach
                @else
                    <!-- Fallback content when no gallery items are available -->
                    <div class="gallery-item gallery-large" data-category="all">
                        <div class="gallery-image">
                            <img src="{{ asset('images/real1.jpg') }}" alt="Premium Properties">
                            <div class="gallery-overlay">
                                <div class="overlay-content">
                                    <span class="property-tag">FEATURED</span>
                                    <h3>Premium Properties</h3>
                                    <p>Discover our collection of luxury properties</p>
                                    <div class="property-details">
                                        <span><i class="fas fa-home"></i> Luxury Homes</span>
                                    </div>
                                    <a href="{{ route('gallery') }}" class="btn btn-outline-light btn-sm">View Gallery</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="gallery-item gallery-medium" data-category="all">
                        <div class="gallery-image">
                            <img src="{{ asset('images/real.jpg') }}" alt="Modern Apartments">
                            <div class="gallery-overlay">
                                <div class="overlay-content">
                                    <span class="property-tag">NEW</span>
                                    <h4>Modern Apartments</h4>
                                    <p>Contemporary living spaces</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="gallery-item gallery-medium" data-category="all">
                        <div class="gallery-image">
                            <img src="{{ asset('images/real3.jpg') }}" alt="Commercial Spaces">
                            <div class="gallery-overlay">
                                <div class="overlay-content">
                                    <span class="property-tag">PREMIUM</span>
                                    <h4>Commercial Spaces</h4>
                                    <p>Prime business locations</p>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- View More Button -->
            <div class="text-center mt-5">
                <a href="{{ route('gallery') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-images"></i> View Complete Gallery
                </a>
            </div>
        </div>
    </section>



    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <!-- Animated Background -->
        <div class="contact-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>

        <div class="container">
            <!-- Creative Header -->
            <div class="contact-header text-center">
                <div class="header-badge">CONTACT US</div>
                <h2 class="contact-title">
                    <span class="title-line">Let's Make Your</span>
                    <span class="title-highlight">Dream Home</span>
                    <span class="title-line">A Reality</span>
                </h2>
                <p class="contact-subtitle">Ready to take the next step? We're here to guide you through every detail of your property journey.</p>
            </div>

            <!-- Interactive Contact Cards -->
            <div class="contact-methods">
                <div class="method-card" data-method="call">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="method-content">
                        <h4>Call Us Now</h4>
                        <p>Speak directly with our experts</p>
                        <a href="tel:+************" class="method-link">📞 +91 ************</a>
                    </div>
                    <div class="method-hover-effect"></div>
                </div>

                <div class="method-card" data-method="email">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-content">
                        <h4>Email Us</h4>
                        <p>Get detailed information via email</p>
                        <a href="mailto:<EMAIL>" class="method-link">📩 <EMAIL></a>
                    </div>
                    <div class="method-hover-effect"></div>
                </div>

                <div class="method-card" data-method="visit">
                    <div class="method-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="method-content">
                        <h4>Visit Our Office</h4>
                        <p>Meet us in person for consultation</p>
                        <span class="method-link">Pune, Maharashtra</span>
                    </div>
                    <div class="method-hover-effect"></div>
                </div>

                <div class="method-card" data-method="form">
                    <div class="method-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="method-content">
                        <h4>Send Message</h4>
                        <p>Fill out our contact form below</p>
                        <span class="method-link">Quick Response</span>
                    </div>
                    <div class="method-hover-effect"></div>
                </div>
            </div>

            <!-- Enhanced Contact Form -->
            <div class="contact-form-section">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="form-container">
                            <div class="form-header">
                                <h3>Send Us a Message</h3>
                                <p>Fill out the form below and we'll get back to you within 24 hours</p>
                            </div>

                            <form class="contact-form" id="homeContactForm" action="{{ route('contact.store') }}" method="POST">
                                @csrf
                                <div class="form-row">
                                    <div class="form-group">
                                        <div class="input-wrapper">
                                            <input type="text" class="form-input" id="name" name="name" required>
                                            <label for="name" class="form-label">Full Name *</label>
                                            <div class="input-line"></div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="input-wrapper">
                                            <input type="email" class="form-input" id="email" name="email" required>
                                            <label for="email" class="form-label">Email Address *</label>
                                            <div class="input-line"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <div class="input-wrapper">
                                            <input type="tel" class="form-input" id="mobile" name="mobile" required maxlength="10">
                                            <label for="mobile" class="form-label">Mobile Number *</label>
                                            <div class="input-line"></div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="select-wrapper">
                                            <select class="form-select" id="propertyType" name="propertyType" required>
                                                <option value="">Select Property Type</option>
                                                <option value="residential">Residential</option>
                                                <option value="commercial">Commercial</option>
                                                <option value="investment">Investment</option>
                                                <option value="rental">Rental</option>
                                            </select>
                                            
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <div class="select-wrapper">
                                            <select class="form-select" id="location" name="location" required>
                                                <option value="">Select Preferred Location</option>
                                                <option value="baner">Baner</option>
                                                <option value="wakad">Wakad</option>
                                                <option value="hinjewadi">Hinjewadi</option>
                                                <option value="kharadi">Kharadi</option>
                                                <option value="koregaon-park">Koregaon Park</option>
                                                <option value="magarpatta">Magarpatta</option>
                                                <option value="aundh">Aundh</option>
                                                <option value="viman-nagar">Viman Nagar</option>
                                                <option value="hadapsar">Hadapsar</option>
                                                <option value="pune-city">Pune City</option>
                                                <option value="other">Other Location</option>
                                            </select>
                                            
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="select-wrapper">
                                            <select class="form-select" id="budgetRange" name="budgetRange" required>
                                                <option value="">Select Budget Range</option>
                                                <option value="under-50">Under ₹50 Lakhs</option>
                                                <option value="50-100">₹50 Lakhs - ₹1 Crore</option>
                                                <option value="100-200">₹1 Crore - ₹2 Crores</option>
                                                <option value="200-500">₹2 Crores - ₹5 Crores</option>
                                                <option value="above-500">Above ₹5 Crores</option>
                                            </select>
                                           
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group full-width">
                                    <div class="textarea-wrapper">
                                        <textarea class="form-textarea" id="message" name="message" rows="5" placeholder="Tell us about your requirements..." required></textarea>
                                        <label for="message" class="form-label">Your Message</label>
                                        <div class="input-line"></div>
                                    </div>
                                </div>

                                <div class="form-submit">
                                    <button type="submit" class="submit-btn">
                                        <span class="btn-text">Send Message</span>
                                        <span class="btn-icon"><i class="fas fa-paper-plane"></i></span>
                                        <div class="btn-ripple"></div>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Office Hours & Social -->
            <div class="contact-footer">
                <div class="row">
                    <div class="col-md-6">
                        <div class="office-hours">
                            <h4>Office Hours</h4>
                            <div class="hours-list">
                                <div class="hours-item">
                                    <span class="day">Monday - Saturday</span>
                                    <span class="time">9:00 AM - 7:00 PM</span>
                                </div>
                                <div class="hours-item">
                                    <span class="day">Sunday</span>
                                    <span class="time">10:00 AM - 5:00 PM</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="social-section">
                            <h4>Follow Us</h4>
                            <div class="social-links social-links-icons-only">
                                <a href="https://www.instagram.com/hestiaabodes?igsh=dnMwNTFvZGZ2dDU=" target="_blank" class="social-link social-link-icon" data-platform="instagram" title="Instagram">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="https://www.linkedin.com/company/hestia-abodes/" target="_blank" class="social-link social-link-icon" data-platform="linkedin" title="LinkedIn">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                                <a href="https://www.facebook.com/share/1CMF9kQhBP/" target="_blank" class="social-link social-link-icon" data-platform="facebook" title="Facebook">
                                    <i class="fab fa-facebook"></i>
                                </a>
                                <a href="https://www.youtube.com/@HestiaAbodes" target="_blank" class="social-link social-link-icon" data-platform="youtube" title="YouTube">
                                    <i class="fab fa-youtube"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Home Contact Form Specific Styles -->
    <style>
        .form-input.is-invalid,
        .form-textarea.is-invalid,
        .form-select.is-invalid {
            border-color: #dc3545;
        }

        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .alert {
            margin-bottom: 1.5rem;
            border-radius: 8px;
        }

        .form-input.has-value + .form-label,
        .form-textarea.has-value + .form-label {
            transform: translateY(-20px) scale(0.8);
            color: #6c757d;
        }

        .input-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .form-input:focus + .form-label + .input-line,
        .form-textarea:focus + .form-label + .input-line,
        .form-select:focus + .form-label + .input-line {
            transform: scaleX(1);
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            opacity: 0.7;
        }

        .btn-close:hover {
            opacity: 1;
        }

    </style>

    <!-- Home Contact Form JavaScript -->
    <script src="{{ asset('js/home-contact.js') }}"></script>

@endsection