@extends('layouts.app')

@section('title', $project->meta_title ?: $project->title)
@section('meta_description', $project->meta_description ?: $project->description)
@section('meta_keywords', $project->meta_keywords)

@section('body')
<!-- Hero Section -->
<section class="hero-section bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('projects') }}">Projects</a></li>
                        <li class="breadcrumb-item active">{{ $project->title }}</li>
                    </ol>
                </nav>
                <h1 class="display-4 fw-bold mb-3">{{ $project->title }}</h1>
                <p class="lead text-muted mb-4">{{ $project->location }}</p>
                <div class="d-flex flex-wrap gap-3 mb-4">
                    <span class="badge bg-primary fs-6">{{ $project->projectCategory->name }}</span>
                    <span class="badge bg-info fs-6">{{ $project->status }}</span>
                    @if($project->featured)
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-star me-1"></i>Featured
                        </span>
                    @endif
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="price-box bg-white p-4 rounded shadow">
                    <h3 class="text-success mb-2">{{ $project->price_range }}</h3>
                    <p class="text-muted mb-0">{{ $project->area_range }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Images -->
@if($project->images && count($project->images) > 0)
<section class="py-5">
    <div class="container">
        <div id="projectCarousel" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-inner">
                @foreach($project->images as $index => $image)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <img src="{{ asset('storage/' . $image) }}" 
                         class="d-block w-100 rounded" 
                         alt="{{ $project->title }}"
                         style="height: 500px; object-fit: cover;">
                </div>
                @endforeach
            </div>
            @if(count($project->images) > 1)
            <button class="carousel-control-prev" type="button" data-bs-target="#projectCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#projectCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
            </button>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Project Details -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Description -->
                <div class="mb-5">
                    <h2 class="mb-4">About This Project</h2>
                    <p class="lead">{{ $project->description }}</p>
                </div>

                <!-- Key Details -->
                <div class="mb-5">
                    <h3 class="mb-4">Key Details</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-3 fs-5"></i>
                                <div>
                                    <strong>Location</strong><br>
                                    <span class="text-muted">{{ $project->location }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building text-primary me-3 fs-5"></i>
                                <div>
                                    <strong>Property Type</strong><br>
                                    <span class="text-muted">{{ $project->type }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar text-primary me-3 fs-5"></i>
                                <div>
                                    <strong>Possession</strong><br>
                                    <span class="text-muted">{{ $project->possession }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user-tie text-primary me-3 fs-5"></i>
                                <div>
                                    <strong>Developer</strong><br>
                                    <span class="text-muted">{{ $project->developer }}</span>
                                </div>
                            </div>
                        </div>
                        @if($project->rera_number)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-certificate text-primary me-3 fs-5"></i>
                                <div>
                                    <strong>RERA Number</strong><br>
                                    <span class="text-muted">{{ $project->rera_number }}</span>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Configurations -->
                <div class="mb-5">
                    <h3 class="mb-4">Available Configurations</h3>
                    <div class="row">
                        @foreach($project->configurations as $config)
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 text-center">
                                <div class="card-body">
                                    <i class="fas fa-home text-primary fs-2 mb-3"></i>
                                    <h5 class="card-title">{{ $config }}</h5>
                                    <p class="card-text text-muted">{{ $project->area_range }}</p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Amenities -->
                <div class="mb-5">
                    <h3 class="mb-4">Amenities</h3>
                    <div class="row">
                        @foreach($project->amenities as $amenity)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3"></i>
                                <span>{{ $amenity }}</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 2rem;">
                    <!-- Contact Form -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-phone me-2"></i>
                                Get More Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="mb-3">
                                    <input type="text" class="form-control" placeholder="Your Name" required>
                                </div>
                                <div class="mb-3">
                                    <input type="email" class="form-control" placeholder="Your Email" required>
                                </div>
                                <div class="mb-3">
                                    <input type="tel" class="form-control" placeholder="Your Phone" required>
                                </div>
                                <div class="mb-3">
                                    <textarea class="form-control" rows="3" placeholder="Your Message"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Send Inquiry
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Quick Info -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Quick Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Price Range:</strong><br>
                                <span class="text-success">{{ $project->price_range }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Area:</strong><br>
                                <span>{{ $project->area_range }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                <span class="badge bg-info">{{ $project->status }}</span>
                            </div>
                            <div class="mb-0">
                                <strong>Possession:</strong><br>
                                <span>{{ $project->possession }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
@if($relatedProjects->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Related Projects</h2>
        <div class="row">
            @foreach($relatedProjects as $relatedProject)
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100">
                    @if($relatedProject->first_image)
                    <img src="{{ asset('storage/' . $relatedProject->first_image) }}" 
                         class="card-img-top" 
                         alt="{{ $relatedProject->title }}"
                         style="height: 200px; object-fit: cover;">
                    @endif
                    <div class="card-body">
                        <h5 class="card-title">{{ $relatedProject->title }}</h5>
                        <p class="card-text text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ $relatedProject->location }}
                        </p>
                        <p class="card-text">
                            <strong class="text-success">{{ $relatedProject->price_range }}</strong>
                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="{{ route('project-details', $relatedProject->slug) }}" class="btn btn-outline-primary btn-sm">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection
