@extends('layouts.app')

@section('title', $blog->meta_title ?: $blog->title)
@section('meta_description', $blog->meta_description ?: $blog->excerpt)
@section('meta_keywords', $blog->meta_keywords)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/blog-enhanced.css') }}">
<link rel="stylesheet" href="{{ asset('css/blog-details-enhanced.css') }}">
@endpush

@section('body')
<!-- Article Hero Section -->
<section class="article-hero-section">
    <div class="container">
        <div class="article-hero-content">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <div class="article-breadcrumb">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="{{ route('home') }}">
                                        <i class="fas fa-home me-1"></i>Home
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="{{ route('blog') }}">
                                        <i class="fas fa-blog me-1"></i>Blog
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">
                                    {{ Str::limit($blog->title, 50) }}
                                </li>
                            </ol>
                        </nav>
                    </div>

                    <h1 class="article-title">{{ $blog->title }}</h1>

                    <div class="article-badges">
                        <span class="article-badge" style="background-color: {{ $blog->blogCategory->color }}; color: white;">
                            <i class="fas fa-folder me-1"></i>{{ $blog->blogCategory->name }}
                        </span>
                        @if($blog->featured)
                            <span class="article-badge featured-badge">
                                <i class="fas fa-star me-1"></i>Featured Article
                            </span>
                        @endif
                    </div>

                    <div class="article-meta">
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <strong>{{ $blog->author }}</strong>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            {{ $blog->formatted_published_at }}
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            {{ $blog->read_time_text }}
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            {{ number_format($blog->views) }} views
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Image Section -->
@if($blog->first_image)
<section class="featured-image-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="featured-image-container">
                    <img src="{{ asset('storage/' . $blog->first_image) }}"
                         class="featured-image"
                         alt="{{ $blog->title }}">
                    <div class="image-overlay">
                        <p class="image-caption">{{ $blog->title }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Article Content Section -->
<section class="article-content-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Article Excerpt -->
                <div class="article-excerpt">
                    <i class="fas fa-quote-left me-2"></i>
                    {{ $blog->excerpt }}
                </div>

                <!-- Article Body -->
                <div class="article-body">
                    {!! $blog->content !!}
                </div>

                <!-- Gallery Images -->
                @if($blog->gallery_images && count($blog->gallery_images) > 0)
                <div class="gallery-section">
                    <h3 class="gallery-title">
                        <i class="fas fa-images me-2"></i>Gallery
                    </h3>
                    <div class="gallery-grid">
                        @foreach($blog->gallery_images as $image)
                        <div class="gallery-item" onclick="openImageModal('{{ asset('storage/' . $image) }}')">
                            <img src="{{ asset('storage/' . $image) }}"
                                 class="gallery-image"
                                 alt="{{ $blog->title }} - Gallery Image">
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Tags Section -->
                @if($blog->tags && count($blog->tags) > 0)
                <div class="tags-section">
                    <h4 class="tags-title">
                        <i class="fas fa-tags me-2"></i>Related Topics
                    </h4>
                    <div class="tags-container">
                        @foreach($blog->tags as $tag)
                            <a href="{{ route('blog') }}?search={{ $tag }}" class="tag-item">
                                <i class="fas fa-tag me-1"></i>{{ $tag }}
                            </a>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Social Share Section -->
                <div class="social-share-section">
                    <h4 class="share-title">
                        <i class="fas fa-share-alt me-2"></i>Share This Article
                    </h4>
                    <div class="share-buttons">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}"
                           target="_blank"
                           class="share-btn share-facebook">
                            <i class="fab fa-facebook-f"></i>
                            Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($blog->title) }}"
                           target="_blank"
                           class="share-btn share-twitter">
                            <i class="fab fa-twitter"></i>
                            Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->fullUrl()) }}"
                           target="_blank"
                           class="share-btn share-linkedin">
                            <i class="fab fa-linkedin-in"></i>
                            LinkedIn
                        </a>
                        <a href="https://wa.me/?text={{ urlencode($blog->title . ' - ' . request()->fullUrl()) }}"
                           target="_blank"
                           class="share-btn share-whatsapp">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp
                        </a>
                    </div>
                </div>

                <!-- Author Section -->
                <div class="author-section">
                    <div class="author-info">
                        <div class="author-avatar">
                            {{ strtoupper(substr($blog->author, 0, 1)) }}
                        </div>
                        <div class="author-details">
                            <h4>{{ $blog->author }}</h4>
                            <p class="author-bio">
                                Real estate expert with extensive knowledge of Pune's property market.
                                Specializing in residential and commercial properties with over 10 years of experience.
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Gallery Images -->
                @if($blog->gallery_images && count($blog->gallery_images) > 0)
                <div class="mb-5">
                    <h5 class="mb-3">Gallery</h5>
                    <div class="row">
                        @foreach($blog->gallery_images as $image)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <img src="{{ asset('storage/' . $image) }}" 
                                 class="img-fluid rounded shadow-sm" 
                                 alt="{{ $blog->title }}"
                                 style="height: 200px; width: 100%; object-fit: cover;">
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Share Buttons -->
                <div class="mb-5">
                    <h5 class="mb-3">Share this article</h5>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" 
                           target="_blank" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($blog->title) }}" 
                           target="_blank" 
                           class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->fullUrl()) }}" 
                           target="_blank" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                        </a>
                        <a href="https://wa.me/?text={{ urlencode($blog->title . ' - ' . request()->fullUrl()) }}" 
                           target="_blank" 
                           class="btn btn-outline-success btn-sm">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </a>
                    </div>
                </div>
                
                <!-- Author Info -->
                <div class="author-info bg-light p-4 rounded mb-5">
                    <div class="d-flex align-items-center">
                        <div class="author-avatar me-3">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px; font-size: 1.5rem;">
                                {{ substr($blog->author, 0, 1) }}
                            </div>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ $blog->author }}</h6>
                            <p class="text-muted mb-0">Real Estate Expert & Content Writer</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 2rem;">
                    <!-- Article Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Article Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Category:</strong><br>
                                <span class="badge" style="background-color: {{ $blog->blogCategory->color }};">
                                    {{ $blog->blogCategory->name }}
                                </span>
                            </div>
                            <div class="mb-3">
                                <strong>Published:</strong><br>
                                <small class="text-muted">{{ $blog->formatted_published_at }}</small>
                            </div>
                            <div class="mb-3">
                                <strong>Reading Time:</strong><br>
                                <small class="text-muted">{{ $blog->read_time_text }}</small>
                            </div>
                            <div class="mb-0">
                                <strong>Views:</strong><br>
                                <small class="text-muted">{{ number_format($blog->views) }} views</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Posts -->
                    @if($recentBlogs->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Recent Posts</h6>
                        </div>
                        <div class="card-body">
                            @foreach($recentBlogs as $recentBlog)
                            <div class="d-flex mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                @if($recentBlog->first_image)
                                <img src="{{ asset('storage/' . $recentBlog->first_image) }}" 
                                     alt="{{ $recentBlog->title }}" 
                                     class="me-3 rounded"
                                     style="width: 60px; height: 60px; object-fit: cover;">
                                @endif
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('blog-details', $recentBlog->slug) }}" class="text-decoration-none">
                                            {{ Str::limit($recentBlog->title, 50) }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ $recentBlog->formatted_published_at }}
                                    </small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                    
                    <!-- Categories -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Categories</h6>
                        </div>
                        <div class="card-body">
                            @foreach($categories as $category)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <a href="{{ route('blog') }}?category={{ $category->slug }}" 
                                   class="text-decoration-none d-flex align-items-center">
                                    <div class="category-color me-2" 
                                         style="width: 12px; height: 12px; background-color: {{ $category->color }}; border-radius: 50%;"></div>
                                    {{ $category->name }}
                                </a>
                                <span class="badge bg-light text-dark">{{ $category->blogs_count }}</span>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    
                    <!-- Contact CTA -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">Need Expert Advice?</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">Get personalized real estate consultation from our experts.</p>
                            <a href="{{ route('contact') }}" class="btn btn-primary w-100">
                                <i class="fas fa-phone me-2"></i>
                                Contact Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Articles -->
@if($relatedBlogs->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Related Articles</h2>
        <div class="row">
            @foreach($relatedBlogs as $relatedBlog)
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 blog-card">
                    @if($relatedBlog->first_image)
                    <img src="{{ asset('storage/' . $relatedBlog->first_image) }}" 
                         class="card-img-top" 
                         alt="{{ $relatedBlog->title }}"
                         style="height: 200px; object-fit: cover;">
                    @endif
                    <div class="card-body">
                        <h6 class="card-title">
                            <a href="{{ route('blog-details', $relatedBlog->slug) }}" class="text-decoration-none">
                                {{ Str::limit($relatedBlog->title, 60) }}
                            </a>
                        </h6>
                        <p class="card-text text-muted small">{{ Str::limit($relatedBlog->excerpt, 80) }}</p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>{{ $relatedBlog->formatted_published_at }}
                            </small>
                            <a href="{{ route('blog-details', $relatedBlog->slug) }}" class="btn btn-outline-primary btn-sm">
                                Read More
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
.article-content .content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.article-content .content h2 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.article-content .content h3 {
    color: #34495e;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.article-content .content ul, 
.article-content .content ol {
    margin-bottom: 1.5rem;
}

.article-content .content li {
    margin-bottom: 0.5rem;
}

.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.author-info {
    border-left: 4px solid var(--bs-primary);
}

.category-color {
    border: 1px solid #dee2e6;
}
</style>
@endpush
