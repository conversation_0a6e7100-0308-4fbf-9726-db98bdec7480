<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', isset($page_title) ? $page_title : '<PERSON>st<PERSON> Abodes - Premium Real Estate Consultancy in Pune')</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="@yield('meta_description', isset($page_description) ? $page_description : 'Hestia Abodes is Pune\'s trusted real estate consultancy with 21+ years of experience. Find your dream home or investment property with expert guidance.')">
    <meta name="keywords" content="@yield('meta_keywords', isset($page_keywords) ? $page_keywords : 'Pune real estate, property consultancy, real estate agents Pune, buy property Pune, investment properties')">
    <meta name="author" content="Hestia Abodes">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('title', isset($page_title) ? $page_title : 'Hestia Abodes - Premium Real Estate Consultancy in Pune')">
    <meta property="og:description" content="@yield('meta_description', isset($page_description) ? $page_description : 'Hestia Abodes is Pune\'s trusted real estate consultancy with 21+ years of experience.')">
    <meta property="og:type" content="website">
    <meta property="og:url" content="@yield('canonical_url', isset($canonical_url) ? $canonical_url : 'https://hestiaabodes.in/')">
    <meta property="og:image" content="https://hestiaabodes.in/images/Hestia Abodes All Platinum Grey Black logo.png">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('title', isset($page_title) ? $page_title : 'Hestia Abodes - Premium Real Estate Consultancy in Pune')">
    <meta name="twitter:description" content="@yield('meta_description', isset($page_description) ? $page_description : 'Hestia Abodes is Pune\'s trusted real estate consultancy with 21+ years of experience.')">
    <meta name="twitter:image" content="https://hestiaabodes.in/images/Hestia Abodes All Platinum Grey Black logo.png">

    <!-- Canonical URL -->
    <link rel="canonical" href="@yield('canonical_url', isset($canonical_url) ? $canonical_url : 'https://hestiaabodes.in/')">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
    <link rel="shortcut icon" type="image/png" href="{{ asset('favicon.png') }}">
    <link rel="apple-touch-icon" href="{{ asset('favicon.png') }}">
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Web App Manifest -->
    <link rel="manifest" href="{{ asset('site.webmanifest') }}">

    <!-- Theme Color for Mobile Browsers -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="msapplication-TileColor" content="#2c3e50">
    <meta name="msapplication-TileImage" content="{{ asset('favicon.png') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">

    <style>
        /* Newsletter Form Styles */
        .newsletter-form .btn {
            position: relative;
            overflow: hidden;
        }

        .newsletter-form .btn-text,
        .newsletter-form .btn-icon {
            transition: all 0.3s ease;
        }

        .newsletter-form .btn:disabled {
            opacity: 0.7;
        }

        .newsletter-message {
            animation: slideDown 0.3s ease;
        }

        .newsletter-message.alert {
            padding: 10px 15px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .newsletter-message.alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .newsletter-message.alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <!-- Debug CSS for Sidebar -->
    <style>
        /* Ensure sidebar is properly positioned and visible when active */
        .sidebar {
            z-index: 9999 !important;
        }

        .sidebar.active {
            right: 0 !important;
            visibility: visible !important;
        }

        .sidebar-overlay.active {
            opacity: 1 !important;
            visibility: visible !important;
            z-index: 9998 !important;
        }

        /* Debug: Temporarily show sidebar toggle button with background */
        #sidebarToggle, #mobileMenuToggle {
            background-color: rgba(0,0,0,0.1) !important;
            border-radius: 5px !important;
            padding: 8px !important;
        }

        /* Footer Featured Projects Styles */
        .footer-projects .footer-project-link {
            display: block;
            padding: 0;
            margin-bottom: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-project-item {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.05);
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .footer-project-link:hover .footer-project-item {
            background: rgba(255, 255, 255, 0.1);
            border-left-color: var(--secondary-color);
            transform: translateX(5px);
        }

        .footer-project-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-project-title {
            color: var(--white);
            font-size: 0.9rem;
            font-weight: 600;
            line-height: 1.2;
        }

        .footer-project-location {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .footer-project-location::before {
            content: '\f3c5';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            font-size: 0.7rem;
        }

        .footer-project-category {
            color: var(--secondary-color);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .footer-project-badge {
            color: #ffd700;
            font-size: 0.8rem;
            margin-left: 8px;
            opacity: 0.8;
        }

        .footer-project-link:hover .footer-project-badge {
            opacity: 1;
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .footer-project-item {
                padding: 10px;
            }

            .footer-project-title {
                font-size: 0.85rem;
            }

            .footer-project-location,
            .footer-project-category {
                font-size: 0.75rem;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top transparent-nav">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="{{ route('home') }}">
                <img src="{{ asset('public/images/Hestia Abodes All Platinum Grey Black logo.png') }}" alt="Hestia Abodes" class="brand-logo">
                <span class="brand-text">Hestia Abodes</span>
            </a>

            <!-- Desktop Menu -->
            <div class="navbar-nav d-none d-lg-flex ms-auto align-items-center">
                 <a class="nav-link" href="{{ route('home') }}">Home</a>
                <a class="nav-link" href="{{ route('about') }}">About Us</a>
                <a class="nav-link" href="{{ route('services') }}">Our Services</a>
                <a class="nav-link" href="{{ route('projects') }}">Projects</a>
                <a class="nav-link" href="{{ route('why-choose-us') }}">Why Choose Us</a>
                <a class="nav-link" href="tel:+919067881848" title="Call Us">
                    <i class="fas fa-phone"></i>
                </a>
                <a class="nav-link" href="#" id="sidebarToggle" title="Menu">
                    <i class="fas fa-bars"></i>
                </a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="navbar-toggler d-lg-none" type="button" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <img src="{{ asset('public/images/Hestia Abodes All Platinum Grey Black logo.png') }}" alt="Hestia Abodes" class="sidebar-logo">
                <span>Hestia Abodes</span>
            </div>
            <button class="btn-close" id="sidebarClose"></button>
        </div>
        <div class="sidebar-content">
            <ul class="sidebar-menu">
                <li><a href="{{ route('home') }}">Home</a></li>
                <li><a href="{{ route('about') }}">About Us</a></li>  
                <li><a href="{{ route('about') }}">Our Services</a></li>
                <li><a href="{{ route('projects') }}">Featured Projects</a></li>
                <li><a href="{{ route('gallery') }}">Gallery</a></li>
                <li><a href="{{ route('how-we-work') }}">How We Work</a></li>
                <li><a href="{{ route('careers') }}">Careers</a></li>
                <li><a href="{{ route('why-choose-us') }}">Why Choose Us</a></li>
                <li><a href="{{ route('blog') }}">Blog & Insights</a></li>
                <li><a href="{{ route('contact') }}">Contact Us</a></li>
            </ul>

            <hr class="sidebar-divider">

            <ul class="sidebar-menu sidebar-contact">
                <li><a href="{{ route('contact') }}">Book Appointment</a></li>
            </ul>

            <hr class="sidebar-divider">

            <div class="social-section">
                <span>Follow Us</span>
                <div class="social-icons">
                    <a href="https://www.instagram.com/hestiaabodes?igsh=dnMwNTFvZGZ2dDU="  target="_blank" title="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="https://www.linkedin.com/company/hestia-abodes/"  target="_blank" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                    <a href="https://www.facebook.com/share/1CMF9kQhBP/" target="_blank" title="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="https://www.youtube.com/@HestiaAbodes" target="_blank" title="YouTube"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>


        @yield('body')

       <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="newsletter-content text-center">
                <h3>Stay Updated with Pune's Real Estate Market</h3>
                <p>Get the latest property listings, market insights, and exclusive deals delivered to your inbox.</p>
                <form class="newsletter-form" id="newsletterForm" action="{{ route('newsletter.subscribe') }}" method="POST">
                    @csrf
                    <div class="input-group">
                        <input type="email" name="email" class="form-control" placeholder="Enter your email address" required>
                        <button class="btn btn-primary" type="submit">
                            <span class="btn-text">Subscribe</span>
                            <span class="btn-icon"><i class="fas fa-envelope"></i></span>
                        </button>
                    </div>
                    <div id="newsletterMessage" class="newsletter-message mt-2" style="display: none;"></div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="row">
                    <!-- Company Info -->
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-section">
                            <div class="footer-brand">
                                <img src="{{ asset('public/images/Hestia Abodes All Platinum Grey Black logo.png') }}" height="100px;" alt="Hestia Abodes" class="footer-logo">
                                <span style="color:white">Hestia Abodes</span>
                            </div>
                            <p class="footer-description">
                                Hestia Abodes is a premium real estate consultancy firm based in Pune, committed to helping homebuyers and investors make confident, informed decisions in an ever-evolving property market.
                            </p>
                            <div class="footer-stats">
                                <div class="stat-item">
                                    <span class="stat-number">21+</span>
                                    <span class="stat-label">Years</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">500+</span>
                                    <span class="stat-label">Homes</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">100%</span>
                                    <span class="stat-label">Satisfaction</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-section">
                            <h5>Quick Links</h5>
                            <ul class="footer-links">
                                <li><a href="{{ route('home') }}">Home</a></li>
                                <li><a href="{{ route('about') }}">About Us</a></li>
                                <li><a href="{{ route('services') }}">Services</a></li>
                                <li><a href="{{ route('why-choose-us') }}">Why Choose Us</a></li>
                                <li><a href="{{ route('projects') }}">Project</a></li>
                                <li><a href="{{ route('contact') }}">Contact</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Featured Projects -->
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-section">
                            <h5>Featured Projects</h5>
                            <ul class="footer-links footer-projects">
                                @if(isset($footerFeaturedProjects) && $footerFeaturedProjects->count() > 0)
                                    @foreach($footerFeaturedProjects as $project)
                                        <li>
                                            <a href="{{ route('project-details', $project->slug) }}" class="footer-project-link">
                                                <div class="footer-project-item">
                                                    <div class="footer-project-info">
                                                        <span class="footer-project-title">{{ Str::limit($project->title, 25) }}</span>
                                                        <span class="footer-project-location">{{ $project->location }}</span>
                                                        @if($project->projectCategory)
                                                            <span class="footer-project-category">{{ $project->projectCategory->name }}</span>
                                                        @endif
                                                    </div>
                                                    <div class="footer-project-badge">
                                                        <i class="fas fa-star"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    @endforeach
                                @else
                                    <li><a href="{{ route('projects') }}">View All Projects</a></li>
                                    <li><a href="{{ route('contact') }}">Contact for Details</a></li>
                                    <li><a href="{{ route('services') }}">Our Services</a></li>
                                @endif
                            </ul>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-section">
                            <h5>Get In Touch</h5>
                            <div class="footer-contact">
                                <div class="contact-item">
                                    <i class="fas fa-phone text-dark"></i>
                                    <a href="tel:+919067881848" class="text-white">📞 +91 ************</a>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <a class="text-white" href="mailto:<EMAIL>">📩 <EMAIL></a>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span class="text-white">Office 205, The Evoq, Wakad, Pune - 411057</span>
                                </div>
                            </div>

                            <div class="footer-social">
                                <h6>Follow Us</h6>
                                <div class="social-icons">
                                    <a href="https://www.instagram.com/hestiaabodes?igsh=dnMwNTFvZGZ2dDU=" target="_blank" title="Instagram"><i class="fab fa-instagram"></i></a>
                                    <a href="https://www.linkedin.com/company/hestia-abodes/" target="_blank" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                                    <a href="https://www.facebook.com/share/1CMF9kQhBP/" target="_blank" title="Facebook"><i class="fab fa-facebook"></i></a>
                                    <a href="https://www.youtube.com/@HestiaAbodes" target="_blank" title="YouTube"><i class="fab fa-youtube"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="footer-copyright">
                            &copy; 2025 Hestia Abodes. All rights reserved.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-bottom-links">
                            <a href="{{ route('privacy-policy') }}">Privacy Policy</a>
                            <span>|</span>
                            <a href="#terms">Terms of Service</a>
                            <span>|</span>
                            <a href="{{ route('sitemap') }}">Sitemap</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </a>

    <!-- WhatsApp Float Button -->
    <div class="whatsapp-float">
        <a href="https://wa.me/919067881848" target="_blank">
            <i class="fab fa-whatsapp"></i>
        </a>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ asset('js/script.js') }}"></script>

    @stack('scripts')

    <!-- Debug Script for Sidebar -->
    <script>
        $(document).ready(function() {
            console.log('Debug: Checking sidebar elements...');
            console.log('Sidebar exists:', $('#sidebar').length);
            console.log('Sidebar toggle exists:', $('#sidebarToggle').length);
            console.log('Mobile toggle exists:', $('#mobileMenuToggle').length);
            console.log('Sidebar overlay exists:', $('#sidebarOverlay').length);

            // Test sidebar functionality
            $('#sidebarToggle, #mobileMenuToggle').click(function(e) {
                e.preventDefault();
                console.log('Sidebar toggle clicked!');
                $('#sidebar').addClass('active');
                $('#sidebarOverlay').addClass('active');
                $('body').addClass('sidebar-open');
            });

            $('#sidebarClose, #sidebarOverlay').click(function() {
                console.log('Sidebar close clicked!');
                $('#sidebar').removeClass('active');
                $('#sidebarOverlay').removeClass('active');
                $('body').removeClass('sidebar-open');
            });
        });
    </script>
</body>
</html>
