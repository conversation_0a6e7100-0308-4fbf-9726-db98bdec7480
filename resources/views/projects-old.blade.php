@extends('layouts.app')

@section('title', 'Projects - Premium Properties | Hestia Abodes')
@section('meta_description', 'Explore our premium residential and commercial projects. Find your dream property with verified developers and best prices.')
@section('meta_keywords', 'projects, premium properties, residential projects, commercial properties, real estate')



@section('body')

<!-- Projects Hero Section -->
@include('partials.page-header', [
    'fallbackTitle' => 'Premium Projects',
    'fallbackSubtitle' => 'Discover our carefully curated selection of premium residential and commercial properties from verified developers with the best prices and locations in Pune'
])

<!-- Statistics Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="stat-item">
                    <h3 class="text-primary">{{ $projects->total() }}+</h3>
                    <p class="text-muted">Premium Projects</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <h3 class="text-primary">{{ $categories->count() }}</h3>
                    <p class="text-muted">Categories</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <h3 class="text-primary">{{ $projects->where('featured', true)->count() }}</h3>
                    <p class="text-muted">Featured Projects</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="py-4 bg-white">
    <div class="container">
        <div class="text-center mb-4">
            <h2>Browse Our Projects</h2>
            <p class="text-muted">Filter by category to find your perfect property</p>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <button class="btn btn-outline-primary active" data-filter="all">All Projects</button>
                    @foreach($categories as $category)
                        <button class="btn btn-outline-primary" data-filter="{{ $category->slug }}">
                            {{ $category->name }}
                        </button>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="py-5">
    <div class="container">
        @if($projects->count() > 0)
            <div class="row" id="projects-grid">
                @foreach($projects as $project)
                <div class="col-lg-4 col-md-6 mb-4 project-item" data-category="{{ $project->projectCategory->slug }}">
                    <div class="card h-100 project-card">
                        @if($project->first_image)
                        <div class="position-relative">
                            <img src="{{ asset('storage/' . $project->first_image) }}" 
                                 class="card-img-top" 
                                 alt="{{ $project->title }}"
                                 style="height: 250px; object-fit: cover;">
                            <div class="position-absolute top-0 end-0 p-3">
                                @if($project->featured)
                                    <span class="badge bg-warning">
                                        <i class="fas fa-star me-1"></i>Featured
                                    </span>
                                @endif
                            </div>
                            <div class="position-absolute bottom-0 start-0 p-3">
                                <span class="badge bg-info">{{ $project->status }}</span>
                            </div>
                        </div>
                        @endif
                        
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-primary">{{ $project->projectCategory->name }}</span>
                                <small class="text-muted">{{ $project->type }}</small>
                            </div>
                            
                            <h5 class="card-title">{{ $project->title }}</h5>
                            
                            <p class="card-text text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ $project->location }}
                            </p>
                            
                            <p class="card-text mb-2">
                                <small class="text-muted">By {{ $project->developer }}</small>
                            </p>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong class="text-success">{{ $project->price_range }}</strong>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ $project->area_range }}</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex flex-wrap gap-1">
                                    @foreach(array_slice($project->configurations, 0, 3) as $config)
                                        <span class="badge bg-light text-dark">{{ $config }}</span>
                                    @endforeach
                                    @if(count($project->configurations) > 3)
                                        <span class="badge bg-light text-dark">+{{ count($project->configurations) - 3 }} more</span>
                                    @endif
                                </div>
                            </div>
                            
                            <p class="card-text">
                                {{ Str::limit($project->description, 100) }}
                            </p>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ $project->possession }}
                                </small>
                                <a href="{{ route('project-details', $project->slug) }}" class="btn btn-primary btn-sm">
                                    View Details
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-5">
                {{ $projects->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h3 class="text-muted">No Projects Found</h3>
                <p class="text-muted">We're working on adding new projects. Please check back soon!</p>
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="mb-3">Looking for Something Specific?</h2>
                <p class="lead mb-0">Our experts can help you find the perfect property that matches your requirements and budget.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{{ route('contact') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-phone me-2"></i>
                    Contact Our Experts
                </a>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.project-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-outline-primary.active {
    background-color: var(--bs-primary);
    color: white;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const filterButtons = document.querySelectorAll('[data-filter]');
    const projectItems = document.querySelectorAll('.project-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter projects
            projectItems.forEach(item => {
                if (filter === 'all' || item.getAttribute('data-category') === filter) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
});
</script>
@endpush
