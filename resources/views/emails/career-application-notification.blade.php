<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Career Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .info-section {
            margin-bottom: 25px;
        }
        .info-section h3 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .info-label {
            font-weight: bold;
            width: 180px;
            color: #555;
        }
        .info-value {
            flex: 1;
            color: #333;
        }
        .status-badge {
            background-color: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .cover-letter {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>New Career Application</h1>
            <p>Hestia Abodes - Career Portal</p>
        </div>

        <div class="info-section">
            <h3>Applicant Information</h3>
            <div class="info-row">
                <div class="info-label">Name:</div>
                <div class="info-value">{{ $application->name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Email:</div>
                <div class="info-value">{{ $application->email }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Phone:</div>
                <div class="info-value">{{ $application->phone }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Position Applied:</div>
                <div class="info-value"><strong>{{ $application->position_applied }}</strong></div>
            </div>
            <div class="info-row">
                <div class="info-label">Status:</div>
                <div class="info-value"><span class="status-badge">{{ ucfirst($application->status) }}</span></div>
            </div>
        </div>

        @if($application->cover_letter)
        <div class="info-section">
            <h3>Message</h3>
            <div class="cover-letter">
                {{ $application->cover_letter }}
            </div>
        </div>
        @endif

        <div class="info-section">
            <h3>Application Details</h3>
            <div class="info-row">
                <div class="info-label">Application ID:</div>
                <div class="info-value">#{{ $application->id }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Applied On:</div>
                <div class="info-value">{{ $application->created_at ? $application->created_at->format('F j, Y \a\t g:i A') : 'N/A' }}</div>
            </div>
            @if($application->ip_address)
            <div class="info-row">
                <div class="info-label">IP Address:</div>
                <div class="info-value">{{ $application->ip_address }}</div>
            </div>
            @endif
            @if($application->resume_path)
            <div class="info-row">
                <div class="info-label">Resume:</div>
                <div class="info-value">Attached to this email</div>
            </div>
            @endif
        </div>

        <div class="footer">
            <p>This application was submitted through the Hestia Abodes career portal.</p>
            <p>Please review the application and respond accordingly.</p>
            <p><strong>Hestia Abodes</strong> - Premium Real Estate Consultancy</p>
        </div>
    </div>
</body>
</html>
