@extends('layouts.app')

@section('title', 'Real Estate Blog & Insights - Hestia Abodes | Pune Property Market Trends')
@section('meta_description', 'Stay updated with latest real estate insights, market trends, and expert advice from Pune\'s leading property consultants. Read about investment tips, legal guides, and market analysis.')
@section('meta_keywords', 'Pune real estate blog, property market trends, real estate insights, investment tips, property buying guide, Pune property news, real estate advice')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/blog-enhanced.css') }}">
@endpush

@section('body')
    <!-- Blog Hero Section -->
    @include('partials.page-header', [
        'sectionClass' => 'blog-hero-section',
        'fallbackTitle' => 'Real Estate Insights & Market Trends',
        'fallbackSubtitle' => 'Stay informed with expert analysis, market trends, and valuable insights from Pune\'s real estate experts'
    ])

    <!-- Search and Filter Section -->
    <section class="search-filter-section py-4 bg-light">
        <div class="container">
            <!-- Category Filter Row -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="category-filter">
                        <div class="d-flex flex-wrap gap-2 justify-content-center">
                            <a href="{{ route('blog') }}"
                               class="filter-btn {{ !request('category') ? 'active' : '' }}">
                                All Categories
                            </a>
                            @foreach($categories as $category)
                                <a href="{{ route('blog') }}?category={{ $category->slug }}{{ request('search') ? '&search=' . request('search') : '' }}"
                                   class="filter-btn {{ request('category') == $category->slug ? 'active' : '' }}"
                                   style="--category-color: {{ $category->color }}">
                                    {{ $category->name }} ({{ $category->blogs_count }})
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Form Row -->
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <form method="GET" action="{{ route('blog') }}" class="search-form">
                        <div class="input-group">
                            <input type="text"
                                   name="search"
                                   class="form-control form-control-lg"
                                   placeholder="Search articles by title, content, or tags..."
                                   value="{{ request('search') }}">
                            <button class="btn btn-primary btn-lg" type="submit">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                        @if(request('category'))
                            <input type="hidden" name="category" value="{{ request('category') }}">
                        @endif
                    </form>
                </div>
            </div>

            <!-- Active Filters -->
            @if(request('search') || request('category'))
                <div class="active-filters mt-3">
                    <span class="me-2">Active filters:</span>
                    @if(request('search'))
                        <span class="badge bg-primary me-2">
                            Search: "{{ request('search') }}"
                            <a href="{{ route('blog') }}{{ request('category') ? '?category=' . request('category') : '' }}"
                               class="text-white ms-1">×</a>
                        </span>
                    @endif
                    @if(request('category'))
                        <span class="badge bg-secondary me-2">
                            Category: {{ $categories->where('slug', request('category'))->first()->name ?? request('category') }}
                            <a href="{{ route('blog') }}{{ request('search') ? '?search=' . request('search') : '' }}"
                               class="text-white ms-1">×</a>
                        </span>
                    @endif
                    <a href="{{ route('blog') }}" class="btn btn-sm btn-outline-secondary">Clear All</a>
                </div>
            @endif
        </div>
    </section>

    <!-- Featured Article Section -->
    @if($featuredBlogs->count() > 0)
    <section class="featured-article-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>Featured Articles</h2>
                <p>Our latest insights on Pune's real estate market</p>
            </div>

            @foreach($featuredBlogs as $index => $featuredBlog)
                @if($index == 0)
                    <!-- Main Featured Article -->
                    <div class="featured-article mb-5">
                        <div class="row align-items-center">
                            <div class="col-lg-6">
                                <div class="article-image">
                                    @if($featuredBlog->first_image)
                                        <img src="{{ asset('storage/' . $featuredBlog->first_image) }}"
                                             alt="{{ $featuredBlog->title }}"
                                             class="img-fluid">
                                    @else
                                        <img src="{{ asset('images/real3.jpg') }}"
                                             alt="{{ $featuredBlog->title }}"
                                             class="img-fluid">
                                    @endif
                                    <div class="article-category" style="background-color: {{ $featuredBlog->blogCategory->color }}">
                                        {{ $featuredBlog->blogCategory->name }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="article-content">
                                    <div class="article-meta">
                                        <span class="article-date">
                                            <i class="fas fa-calendar"></i> {{ $featuredBlog->formatted_published_at }}
                                        </span>
                                        <span class="article-read-time">
                                            <i class="fas fa-clock"></i> {{ $featuredBlog->read_time_text }}
                                        </span>
                                        <span class="article-views">
                                            <i class="fas fa-eye"></i> {{ number_format($featuredBlog->views) }} views
                                        </span>
                                    </div>
                                    <h3 class="article-title">
                                        <a href="{{ route('blog-details', $featuredBlog->slug) }}">
                                            {{ $featuredBlog->title }}
                                        </a>
                                    </h3>
                                    <p class="article-excerpt">
                                        {{ $featuredBlog->excerpt }}
                                    </p>
                                    @if($featuredBlog->tags && count($featuredBlog->tags) > 0)
                                        <div class="article-tags">
                                            @foreach(array_slice($featuredBlog->tags, 0, 3) as $tag)
                                                <span class="tag">{{ $tag }}</span>
                                            @endforeach
                                        </div>
                                    @endif
                                    <a href="{{ route('blog-details', $featuredBlog->slug) }}" class="btn btn-primary">
                                        Read Full Article
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @endforeach

            <!-- Additional Featured Articles -->
            @if($featuredBlogs->count() > 1)
                <div class="row">
                    @foreach($featuredBlogs->skip(1) as $featuredBlog)
                        <div class="col-lg-6 mb-4">
                            <article class="blog-card featured-card">
                                <div class="blog-card-image">
                                    @if($featuredBlog->first_image)
                                        <img src="{{ asset('storage/' . $featuredBlog->first_image) }}"
                                             alt="{{ $featuredBlog->title }}"
                                             class="img-fluid">
                                    @else
                                        <img src="{{ asset('images/real3.jpg') }}"
                                             alt="{{ $featuredBlog->title }}"
                                             class="img-fluid">
                                    @endif
                                    <div class="blog-category" style="background-color: {{ $featuredBlog->blogCategory->color }}">
                                        {{ $featuredBlog->blogCategory->name }}
                                    </div>
                                    <div class="featured-badge">
                                        <i class="fas fa-star"></i> Featured
                                    </div>
                                </div>
                                <div class="blog-card-content">
                                    <div class="blog-meta">
                                        <span class="blog-date">
                                            <i class="fas fa-calendar"></i> {{ $featuredBlog->formatted_published_at }}
                                        </span>
                                        <span class="blog-read-time">
                                            <i class="fas fa-clock"></i> {{ $featuredBlog->read_time_text }}
                                        </span>
                                    </div>
                                    <h3 class="blog-title">
                                        <a href="{{ route('blog-details', $featuredBlog->slug) }}">
                                            {{ Str::limit($featuredBlog->title, 60) }}
                                        </a>
                                    </h3>
                                    <p class="blog-excerpt">
                                        {{ Str::limit($featuredBlog->excerpt, 120) }}
                                    </p>
                                    @if($featuredBlog->tags && count($featuredBlog->tags) > 0)
                                        <div class="blog-tags">
                                            @foreach(array_slice($featuredBlog->tags, 0, 2) as $tag)
                                                <span class="tag">{{ $tag }}</span>
                                            @endforeach
                                        </div>
                                    @endif
                                    <a href="{{ route('blog-details', $featuredBlog->slug) }}" class="read-more-btn">
                                        Read More <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </article>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </section>
    @endif

    <!-- Blog Articles Grid -->
    <section class="blog-articles-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>
                    @if(request('search'))
                        Search Results for "{{ request('search') }}"
                    @elseif(request('category'))
                        {{ $categories->where('slug', request('category'))->first()->name ?? 'Category' }} Articles
                    @else
                        Latest Articles
                    @endif
                </h2>
                <p>
                    @if($blogs->total() > 0)
                        Showing {{ $blogs->firstItem() }} to {{ $blogs->lastItem() }} of {{ $blogs->total() }} articles
                    @else
                        No articles found matching your criteria
                    @endif
                </p>
            </div>

            @if($blogs->count() > 0)
                <div class="articles-grid">
                    <div class="row">
                        @foreach($blogs as $blog)
                            <div class="col-lg-4 col-md-6 mb-4">
                                <article class="blog-card">
                                    <div class="blog-card-image">
                                        @if($blog->first_image)
                                            <img src="{{ asset('storage/' . $blog->first_image) }}"
                                                 alt="{{ $blog->title }}"
                                                 class="img-fluid">
                                        @else
                                            <img src="{{ asset('images/real3.jpg') }}"
                                                 alt="{{ $blog->title }}"
                                                 class="img-fluid">
                                        @endif
                                        <div class="blog-category" style="background-color: {{ $blog->blogCategory->color }}">
                                            {{ $blog->blogCategory->name }}
                                        </div>
                                        @if($blog->featured)
                                            <div class="featured-badge">
                                                <i class="fas fa-star"></i> Featured
                                            </div>
                                        @endif
                                    </div>
                                    <div class="blog-card-content">
                                        <div class="blog-meta">
                                            <span class="blog-date">
                                                <i class="fas fa-calendar"></i> {{ $blog->formatted_published_at }}
                                            </span>
                                            <span class="blog-read-time">
                                                <i class="fas fa-clock"></i> {{ $blog->read_time_text }}
                                            </span>
                                            <span class="blog-views">
                                                <i class="fas fa-eye"></i> {{ number_format($blog->views) }}
                                            </span>
                                        </div>
                                        <h3 class="blog-title">
                                            <a href="{{ route('blog-details', $blog->slug) }}">
                                                {{ Str::limit($blog->title, 60) }}
                                            </a>
                                        </h3>
                                        <p class="blog-excerpt">
                                            {{ Str::limit($blog->excerpt, 120) }}
                                        </p>
                                        @if($blog->tags && count($blog->tags) > 0)
                                            <div class="blog-tags">
                                                @foreach(array_slice($blog->tags, 0, 2) as $tag)
                                                    <span class="tag">{{ $tag }}</span>
                                                @endforeach
                                                @if(count($blog->tags) > 2)
                                                    <span class="tag-more">+{{ count($blog->tags) - 2 }} more</span>
                                                @endif
                                            </div>
                                        @endif
                                        <a href="{{ route('blog-details', $blog->slug) }}" class="read-more-btn">
                                            Read More <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </article>
                            </div>
                        @endforeach
                    </div>
                </div>


                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-5">
                    {{ $blogs->appends(request()->query())->links('pagination::bootstrap-4') }}
                </div>
            @else
                <!-- No Articles Found -->
                <div class="no-articles-found text-center py-5">
                    <div class="no-articles-icon mb-4">
                        <i class="fas fa-search fa-4x text-muted"></i>
                    </div>
                    <h3 class="text-muted mb-3">No Articles Found</h3>
                    <p class="text-muted mb-4">
                        @if(request('search'))
                            We couldn't find any articles matching your search for "<strong>{{ request('search') }}</strong>".
                        @elseif(request('category'))
                            No articles found in this category.
                        @else
                            No articles are currently available.
                        @endif
                    </p>
                    <div class="no-articles-actions">
                        @if(request('search') || request('category'))
                            <a href="{{ route('blog') }}" class="btn btn-primary me-2">
                                <i class="fas fa-arrow-left me-1"></i> View All Articles
                            </a>
                        @endif
                        <a href="{{ route('contact') }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-1"></i> Contact Us
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Sidebar Section -->
    @if($recentBlogs->count() > 0 || $categories->count() > 0)
    <section class="blog-sidebar-section py-5 bg-light">
        <div class="container">
            <div class="row">
                <!-- Categories -->
                @if($categories->count() > 0)
                <div class="col-lg-6 mb-4">
                    <div class="sidebar-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-folder me-2"></i>Categories
                        </h4>
                        <div class="categories-list">
                            @foreach($categories as $category)
                                <div class="category-item">
                                    <a href="{{ route('blog') }}?category={{ $category->slug }}"
                                       class="d-flex justify-content-between align-items-center text-decoration-none">
                                        <div class="d-flex align-items-center">
                                            <div class="category-color me-2"
                                                 style="width: 12px; height: 12px; background-color: {{ $category->color }}; border-radius: 50%;"></div>
                                            <span>{{ $category->name }}</span>
                                        </div>
                                        <span class="badge bg-light text-dark">{{ $category->blogs_count }}</span>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Recent Articles -->
                @if($recentBlogs->count() > 0)
                <div class="col-lg-6 mb-4">
                    <div class="sidebar-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-clock me-2"></i>Recent Articles
                        </h4>
                        <div class="recent-articles">
                            @foreach($recentBlogs as $recentBlog)
                                <div class="recent-article-item">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            @if($recentBlog->first_image)
                                                <img src="{{ asset('storage/' . $recentBlog->first_image) }}"
                                                     alt="{{ $recentBlog->title }}"
                                                     class="img-fluid rounded">
                                            @else
                                                <div class="placeholder-image rounded d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="col-8">
                                            <h6 class="article-title mb-1">
                                                <a href="{{ route('blog-details', $recentBlog->slug) }}" class="text-decoration-none">
                                                    {{ Str::limit($recentBlog->title, 50) }}
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>{{ $recentBlog->formatted_published_at }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </section>
    @endif

@endsection

@push('styles')
<style>
.blog-stats .stat-item {
    display: inline-block;
    text-align: center;
    margin: 0 1rem;
}

.blog-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #fff;
}

.blog-stats .stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.search-filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.category-filter {
    margin-bottom: 1rem;
}

.search-form {
    max-width: 100%;
}

.search-form .input-group {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
}

.search-form .form-control {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
}

.search-form .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.search-form .btn {
    border: none;
    padding: 1rem 2rem;
    font-weight: 600;
}

.filter-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: white;
    color: #6c757d;
    text-decoration: none;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
    margin: 0.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--category-color, #007bff);
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: var(--category-color, #007bff);
}

.active-filters .badge {
    font-size: 0.8rem;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ffc107;
    color: #000;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
}

.blog-meta .blog-views {
    margin-left: 0.5rem;
}

.tag-more {
    background: #e9ecef;
    color: #6c757d;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-left: 0.25rem;
}

.no-articles-found {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.sidebar-widget {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.widget-title {
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
}

.recent-article-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.recent-article-item:last-child {
    border-bottom: none;
}

.placeholder-image {
    height: 60px;
    background: #f8f9fa;
}

.category-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.category-item:last-child {
    border-bottom: none;
}

.category-item:hover {
    background: #f8f9fa;
    margin: 0 -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
}



/* Responsive Design for Search and Filter */
@media (max-width: 768px) {
    .filter-btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
        margin: 0.15rem;
    }

    .search-form .form-control {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .search-form .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .category-filter {
        text-align: center;
    }


}

@media (max-width: 576px) {
    .filter-btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        margin: 0.1rem;
    }

    .search-form .input-group {
        border-radius: 20px;
    }

}
</style>
@endpush