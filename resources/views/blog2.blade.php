@extends('layouts.app')

@section('title', 'Real Estate Blog & Insights | Hestia Abodes')
@section('meta_description', 'Stay updated with latest real estate insights, market trends, and expert advice from Pune\'s leading property consultants.')
@section('meta_keywords', 'Pune real estate blog, property market trends, real estate insights, investment tips, property buying guide')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/blog-enhanced.css') }}">
@endpush

@section('body')
<!-- Hero Section with Enhanced Design -->
<section class="blog-hero-section">
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb breadcrumb-light">
                        <li class="breadcrumb-item">
                            <a href="{{ route('home') }}" class="text-white-50 text-decoration-none">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white">
                            <i class="fas fa-blog me-1"></i>Blog
                        </li>
                    </ol>
                </nav>
                <h1 class="hero-title mb-4">Real Estate Insights & Market Trends</h1>
                <p class="hero-subtitle mb-4">Stay informed with expert analysis, market trends, and valuable insights from Pune's leading real estate consultants</p>
                <div class="hero-stats d-flex flex-wrap gap-4">
                    <div class="stat-item">
                        <div class="stat-number">{{ $blogs->total() }}+</div>
                        <div class="stat-label">Expert Articles</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ $categories->count() }}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ $featuredBlogs->count() }}</div>
                        <div class="stat-label">Featured Posts</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-search-box">
                    <form method="GET" action="{{ route('blog') }}" class="search-form">
                        <div class="input-group">
                            <input type="text"
                                   class="form-control search-input"
                                   name="search"
                                   placeholder="Search articles..."
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-search">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Articles Section -->
@if($featuredBlogs->count() > 0)
<section class="featured-articles-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <div class="section-badge">
                <i class="fas fa-star me-2"></i>Featured Articles
            </div>
            <h2 class="section-title">Editor's Choice</h2>
            <p class="section-subtitle">Our most popular and insightful posts handpicked by our experts</p>
        </div>

        <div class="row">
            @foreach($featuredBlogs as $index => $blog)
            <div class="col-lg-{{ $index === 0 ? '8' : '4' }} col-md-6 mb-4">
                <article class="featured-article-card {{ $index === 0 ? 'main-featured' : 'side-featured' }}">
                    <div class="article-image-container">
                        @if($blog->first_image)
                        <img src="{{ asset('storage/' . $blog->first_image) }}"
                             class="article-image"
                             alt="{{ $blog->title }}">
                        @else
                        <div class="article-image-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                        @endif
                        <div class="article-overlay">
                            <div class="article-badges">
                                <span class="badge featured-badge">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                                <span class="badge category-badge" style="background-color: {{ $blog->blogCategory->color }};">
                                    {{ $blog->blogCategory->name }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="article-content">
                        <div class="article-meta">
                            <span class="meta-item">
                                <i class="fas fa-user"></i>
                                {{ $blog->author }}
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-calendar"></i>
                                {{ $blog->formatted_published_at }}
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-clock"></i>
                                {{ $blog->read_time_text }}
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-eye"></i>
                                {{ number_format($blog->views) }}
                            </span>
                        </div>

                        <h{{ $index === 0 ? '3' : '5' }} class="article-title">
                            <a href="{{ route('blog-details', $blog->slug) }}" class="title-link">
                                {{ $blog->title }}
                            </a>
                        </h{{ $index === 0 ? '3' : '5' }}>

                        <p class="article-excerpt">{{ $blog->excerpt_limited }}</p>

                        <div class="article-footer">
                            <a href="{{ route('blog-details', $blog->slug) }}" class="read-more-btn">
                                Read Full Article
                                <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    </div>
                </article>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Main Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Search and Filter -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h3>Latest Articles</h3>
                        <p class="text-muted mb-0">{{ $blogs->total() }} articles found</p>
                    </div>
                    <div class="d-flex gap-2">
                        <!-- Search Form -->
                        <form method="GET" class="d-flex">
                            <input type="text" 
                                   class="form-control form-control-sm" 
                                   name="search" 
                                   placeholder="Search articles..." 
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary btn-sm ms-2">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Category Filter -->
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{{ route('blog') }}" 
                           class="btn btn-sm {{ !request('category') ? 'btn-primary' : 'btn-outline-primary' }}">
                            All Categories
                        </a>
                        @foreach($categories as $category)
                        <a href="{{ route('blog') }}?category={{ $category->slug }}" 
                           class="btn btn-sm {{ request('category') === $category->slug ? 'btn-primary' : 'btn-outline-primary' }}"
                           style="{{ request('category') === $category->slug ? 'background-color: ' . $category->color . '; border-color: ' . $category->color : '' }}">
                            {{ $category->name }} ({{ $category->blogs_count }})
                        </a>
                        @endforeach
                    </div>
                </div>
                
                <!-- Blog Posts Grid -->
                @if($blogs->count() > 0)
                    <div class="row">
                        @foreach($blogs as $blog)
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 blog-card">
                                @if($blog->first_image)
                                <div class="position-relative">
                                    <img src="{{ asset('storage/' . $blog->first_image) }}" 
                                         class="card-img-top" 
                                         alt="{{ $blog->title }}"
                                         style="height: 200px; object-fit: cover;">
                                    <div class="position-absolute bottom-0 start-0 p-3">
                                        <span class="badge" style="background-color: {{ $blog->blogCategory->color }};">
                                            {{ $blog->blogCategory->name }}
                                        </span>
                                    </div>
                                </div>
                                @endif
                                
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="{{ route('blog-details', $blog->slug) }}" class="text-decoration-none">
                                            {{ $blog->title }}
                                        </a>
                                    </h5>
                                    
                                    <p class="card-text text-muted">{{ $blog->excerpt_limited }}</p>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ $blog->author }}
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-eye me-1"></i>{{ $blog->views }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ $blog->formatted_published_at }}
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>{{ $blog->read_time_text }}
                                        </small>
                                        <a href="{{ route('blog-details', $blog->slug) }}" class="btn btn-primary btn-sm">
                                            Read More
                                            <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-5">
                        {{ $blogs->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Articles Found</h4>
                        <p class="text-muted">Try adjusting your search or filter criteria.</p>
                        <a href="{{ route('blog') }}" class="btn btn-primary">View All Articles</a>
                    </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 2rem;">
                    <!-- Recent Posts -->
                    @if($recentBlogs->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Posts
                            </h5>
                        </div>
                        <div class="card-body">
                            @foreach($recentBlogs as $recentBlog)
                            <div class="d-flex mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                @if($recentBlog->first_image)
                                <img src="{{ asset('storage/' . $recentBlog->first_image) }}" 
                                     alt="{{ $recentBlog->title }}" 
                                     class="me-3 rounded"
                                     style="width: 60px; height: 60px; object-fit: cover;">
                                @endif
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('blog-details', $recentBlog->slug) }}" class="text-decoration-none">
                                            {{ Str::limit($recentBlog->title, 50) }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ $recentBlog->formatted_published_at }}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-eye me-1"></i>{{ $recentBlog->views }}
                                    </small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                    
                    <!-- Categories -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-folder me-2"></i>
                                Categories
                            </h5>
                        </div>
                        <div class="card-body">
                            @foreach($categories as $category)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <a href="{{ route('blog') }}?category={{ $category->slug }}" 
                                   class="text-decoration-none d-flex align-items-center">
                                    <div class="category-color me-2" 
                                         style="width: 12px; height: 12px; background-color: {{ $category->color }}; border-radius: 50%;"></div>
                                    {{ $category->name }}
                                </a>
                                <span class="badge bg-light text-dark">{{ $category->blogs_count }}</span>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    
                    <!-- Newsletter -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>
                                Stay Updated
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">Get the latest real estate insights delivered to your inbox.</p>
                            <form>
                                <div class="mb-3">
                                    <input type="email" class="form-control" placeholder="Your email address" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Subscribe
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.blog-card.featured {
    border: 2px solid #ffc107;
}

.stats-box {
    backdrop-filter: blur(10px);
}

.breadcrumb-dark .breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255,255,255,0.5);
}

.category-color {
    border: 1px solid #dee2e6;
}
</style>
@endpush
