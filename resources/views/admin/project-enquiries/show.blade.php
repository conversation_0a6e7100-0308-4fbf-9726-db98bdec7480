@extends('admin.layouts.dashboard')

@section('title', 'Project Enquiry Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Enquiry Details</h1>
            <p class="mb-0 text-muted">View and manage project enquiry information</p>
        </div>
        <div>
            <a href="{{ route('admin.project-enquiries.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('admin.project-enquiries.edit', $projectEnquiry) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Update Status
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Name:</label>
                                <p class="mb-2">{{ $projectEnquiry->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Email:</label>
                                <p class="mb-2">
                                    <a href="mailto:{{ $projectEnquiry->email }}">{{ $projectEnquiry->email }}</a>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Phone:</label>
                                <p class="mb-2">
                                    <a href="tel:{{ $projectEnquiry->phone }}">{{ $projectEnquiry->phone }}</a>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Interest:</label>
                                <p class="mb-2">
                                    <span class="badge badge-info">{{ $projectEnquiry->interest_display }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($projectEnquiry->message)
                        <div class="form-group">
                            <label class="font-weight-bold">Message:</label>
                            <div class="border p-3 bg-light rounded">
                                {{ $projectEnquiry->message }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Project Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Project Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="font-weight-bold">Project Name:</label>
                                <p class="mb-2">{{ $projectEnquiry->project_name }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            @if($projectEnquiry->project)
                                <a href="{{ route('admin.projects.show', $projectEnquiry->project) }}" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt"></i> View Project
                                </a>
                            @endif
                        </div>
                    </div>

                    @if($projectEnquiry->project)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Location:</label>
                                    <p class="mb-2">{{ $projectEnquiry->project->location }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Category:</label>
                                    <p class="mb-2">{{ $projectEnquiry->project->category->name ?? 'N/A' }}</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Admin Notes -->
            @if($projectEnquiry->admin_notes)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Admin Notes</h6>
                    </div>
                    <div class="card-body">
                        <div class="border p-3 bg-light rounded">
                            {{ $projectEnquiry->admin_notes }}
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Status and Timeline -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status & Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Current Status:</label>
                        <p class="mb-3">
                            <span class="badge {{ $projectEnquiry->status_badge_class }} badge-lg">
                                {{ $projectEnquiry->status_display }}
                            </span>
                        </p>
                    </div>

                    <div class="form-group">
                        <label class="font-weight-bold">Enquiry Date:</label>
                        <p class="mb-2">{{ $projectEnquiry->formatted_created_at }}</p>
                        <p class="text-muted small">{{ $projectEnquiry->time_ago }}</p>
                    </div>

                    @if($projectEnquiry->contacted_at)
                        <div class="form-group">
                            <label class="font-weight-bold">Contacted Date:</label>
                            <p class="mb-2">{{ $projectEnquiry->contacted_at->format('M d, Y h:i A') }}</p>
                            <p class="text-muted small">{{ $projectEnquiry->contacted_at->diffForHumans() }}</p>
                        </div>
                    @endif

                    <div class="form-group">
                        <label class="font-weight-bold">Last Updated:</label>
                        <p class="mb-2">{{ $projectEnquiry->updated_at->format('M d, Y h:i A') }}</p>
                        <p class="text-muted small">{{ $projectEnquiry->updated_at->diffForHumans() }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ $projectEnquiry->email }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                        <a href="tel:{{ $projectEnquiry->phone }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-phone"></i> Call Customer
                        </a>
                        <a href="{{ route('admin.project-enquiries.edit', $projectEnquiry) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Update Status
                        </a>
                    </div>
                </div>
            </div>

            <!-- Delete Action -->
            <div class="card shadow border-danger">
                <div class="card-header py-3 bg-danger">
                    <h6 class="m-0 font-weight-bold text-white">Danger Zone</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">Once you delete this enquiry, there is no going back.</p>
                    <form action="{{ route('admin.project-enquiries.destroy', $projectEnquiry) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this enquiry?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash"></i> Delete Enquiry
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
