@extends('admin.layouts.dashboard')

@section('title', 'Update Project Enquiry')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Update Enquiry Status</h1>
            <p class="mb-0 text-muted">Update status and add notes for project enquiry</p>
        </div>
        <div>
            <a href="{{ route('admin.project-enquiries.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('admin.project-enquiries.show', $projectEnquiry) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Details
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Update Form -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Update Status & Notes</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.project-enquiries.update', $projectEnquiry) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="font-weight-bold">Status <span class="text-danger">*</span></label>
                                    <select name="status" id="status" class="form-control @error('status') is-invalid @enderror" required>
                                        <option value="new" {{ $projectEnquiry->status == 'new' ? 'selected' : '' }}>New</option>
                                        <option value="contacted" {{ $projectEnquiry->status == 'contacted' ? 'selected' : '' }}>Contacted</option>
                                        <option value="interested" {{ $projectEnquiry->status == 'interested' ? 'selected' : '' }}>Interested</option>
                                        <option value="not_interested" {{ $projectEnquiry->status == 'not_interested' ? 'selected' : '' }}>Not Interested</option>
                                        <option value="converted" {{ $projectEnquiry->status == 'converted' ? 'selected' : '' }}>Converted</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Current status: <span class="badge {{ $projectEnquiry->status_badge_class }}">{{ $projectEnquiry->status_display }}</span>
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Current Status Info</label>
                                    <div class="border p-3 bg-light rounded">
                                        <div><strong>Status:</strong> {{ $projectEnquiry->status_display }}</div>
                                        <div><strong>Last Updated:</strong> {{ $projectEnquiry->updated_at->format('M d, Y h:i A') }}</div>
                                        @if($projectEnquiry->contacted_at)
                                            <div><strong>Contacted:</strong> {{ $projectEnquiry->contacted_at->format('M d, Y h:i A') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="admin_notes" class="font-weight-bold">Admin Notes</label>
                            <textarea name="admin_notes" id="admin_notes" rows="5" 
                                      class="form-control @error('admin_notes') is-invalid @enderror" 
                                      placeholder="Add notes about this enquiry, follow-up actions, customer feedback, etc.">{{ old('admin_notes', $projectEnquiry->admin_notes) }}</textarea>
                            @error('admin_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">These notes are for internal use only and will not be visible to the customer.</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Enquiry
                            </button>
                            <a href="{{ route('admin.project-enquiries.show', $projectEnquiry) }}" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Customer & Project Info -->
        <div class="col-lg-4">
            <!-- Customer Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Summary</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Name:</label>
                        <p class="mb-2">{{ $projectEnquiry->name }}</p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Email:</label>
                        <p class="mb-2">
                            <a href="mailto:{{ $projectEnquiry->email }}">{{ $projectEnquiry->email }}</a>
                        </p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Phone:</label>
                        <p class="mb-2">
                            <a href="tel:{{ $projectEnquiry->phone }}">{{ $projectEnquiry->phone }}</a>
                        </p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Interest:</label>
                        <p class="mb-2">
                            <span class="badge badge-info">{{ $projectEnquiry->interest_display }}</span>
                        </p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Enquiry Date:</label>
                        <p class="mb-2">{{ $projectEnquiry->formatted_created_at }}</p>
                        <p class="text-muted small">{{ $projectEnquiry->time_ago }}</p>
                    </div>
                </div>
            </div>

            <!-- Project Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Project Summary</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Project:</label>
                        <p class="mb-2">{{ $projectEnquiry->project_name }}</p>
                    </div>
                    @if($projectEnquiry->project)
                        <div class="form-group">
                            <label class="font-weight-bold">Location:</label>
                            <p class="mb-2">{{ $projectEnquiry->project->location }}</p>
                        </div>
                        <div class="form-group">
                            <label class="font-weight-bold">Category:</label>
                            <p class="mb-2">{{ $projectEnquiry->project->category->name ?? 'N/A' }}</p>
                        </div>
                        <div class="form-group">
                            <a href="{{ route('admin.projects.show', $projectEnquiry->project) }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt"></i> View Project Details
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Customer Message -->
            @if($projectEnquiry->message)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Customer Message</h6>
                    </div>
                    <div class="card-body">
                        <div class="border p-3 bg-light rounded">
                            {{ $projectEnquiry->message }}
                        </div>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ $projectEnquiry->email }}" class="btn btn-outline-primary btn-sm mb-2">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                        <a href="tel:{{ $projectEnquiry->phone }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-phone"></i> Call Customer
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
