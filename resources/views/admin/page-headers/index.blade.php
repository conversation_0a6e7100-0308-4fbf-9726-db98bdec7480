@extends('admin.layouts.dashboard')

@section('title', 'Page Headers Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-heading me-2"></i>Page Headers Management
                    </h3>
                    <a href="{{ route('admin.page-headers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Header
                    </a>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($headers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Page Name</th>
                                        <th>Background Image</th>
                                        <th>Title</th>
                                        <th>Subtitle</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($headers as $header)
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">{{ ucfirst($header->page_name) }}</span>
                                            </td>
                                            <td>
                                                @if($header->background_image)
                                                    <img src="{{ $header->background_image_url }}" 
                                                         alt="Header Image" 
                                                         class="img-thumbnail" 
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                @else
                                                    <span class="text-muted">No Image</span>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $header->title }}</strong>
                                            </td>
                                            <td>
                                                {{ Str::limit($header->subtitle, 40) }}
                                            </td>
                                            <td>
                                                @if($header->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.page-headers.show', $header) }}" 
                                                       class="btn btn-sm btn-outline-info" 
                                                       title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.page-headers.edit', $header) }}" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.page-headers.destroy', $header) }}" 
                                                          method="POST" 
                                                          class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this page header?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-heading fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Page Headers Found</h5>
                            <p class="text-muted">Create your first page header to get started.</p>
                            <a href="{{ route('admin.page-headers.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add New Header
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Setup Card -->
            @if($headers->count() == 0)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>Quick Setup Guide
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">Get started by creating headers for your main pages:</p>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-info-circle fa-2x text-info mb-2"></i>
                                    <h6>About Page</h6>
                                    <small class="text-muted">page_name: about</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-building fa-2x text-success mb-2"></i>
                                    <h6>Projects Page</h6>
                                    <small class="text-muted">page_name: projects</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-blog fa-2x text-warning mb-2"></i>
                                    <h6>Blog Page</h6>
                                    <small class="text-muted">page_name: blog</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-envelope fa-2x text-danger mb-2"></i>
                                    <h6>Contact Page</h6>
                                    <small class="text-muted">page_name: contact</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-star fa-2x text-primary mb-2"></i>
                                    <h6>Why Choose Us</h6>
                                    <small class="text-muted">page_name: why-choose-us</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-images fa-2x text-secondary mb-2"></i>
                                    <h6>Gallery</h6>
                                    <small class="text-muted">page_name: gallery</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-cogs fa-2x text-dark mb-2"></i>
                                    <h6>How We Work</h6>
                                    <small class="text-muted">page_name: how-we-work</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-shield-alt fa-2x text-muted mb-2"></i>
                                    <h6>Privacy Policy</h6>
                                    <small class="text-muted">page_name: privacy-policy</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-hide alerts after 5 seconds
setTimeout(function() {
    $('.alert').fadeOut('slow');
}, 5000);
</script>
@endpush
