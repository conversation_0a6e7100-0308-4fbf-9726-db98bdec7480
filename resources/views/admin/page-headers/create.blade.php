@extends('admin.layouts.dashboard')

@section('title', 'Create Page Header')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Create New Page Header
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.page-headers.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ route('admin.page-headers.store') }}" enctype="multipart/form-data" id="headerForm">
                    @csrf
                    <div class="card-body">
                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        <div class="row">
                            <div class="col-md-8">
                                <!-- Page Name -->
                                <div class="mb-3">
                                    <label for="page_name" class="form-label">Page Name <span class="text-danger">*</span></label>
                                    <select class="form-select @error('page_name') is-invalid @enderror" 
                                            id="page_name" 
                                            name="page_name" 
                                            required>
                                        <option value="">Select a page</option>
                                        <option value="about" {{ old('page_name') == 'about' ? 'selected' : '' }}>About</option>
                                        <option value="projects" {{ old('page_name') == 'projects' ? 'selected' : '' }}>Projects</option>
                                        <option value="blog" {{ old('page_name') == 'blog' ? 'selected' : '' }}>Blog</option>
                                        <option value="contact" {{ old('page_name') == 'contact' ? 'selected' : '' }}>Contact</option>
                                        <option value="services" {{ old('page_name') == 'services' ? 'selected' : '' }}>Services</option>
                                        <option value="careers" {{ old('page_name') == 'careers' ? 'selected' : '' }}>Careers</option>
                                        <option value="why-choose-us" {{ old('page_name') == 'why-choose-us' ? 'selected' : '' }}>Why Choose Us</option>
                                        <option value="gallery" {{ old('page_name') == 'gallery' ? 'selected' : '' }}>Gallery</option>
                                        <option value="how-we-work" {{ old('page_name') == 'how-we-work' ? 'selected' : '' }}>How We Work</option>
                                        <option value="privacy-policy" {{ old('page_name') == 'privacy-policy' ? 'selected' : '' }}>Privacy Policy</option>
                                        <option value="other" {{ old('page_name') == 'other' ? 'selected' : '' }}>Other (Custom)</option>
                                    </select>
                                    @error('page_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Select the page this header will be used for</small>
                                </div>

                                <!-- Custom Page Name (shown when "Other" is selected) -->
                                <div class="mb-3" id="custom-page-name" style="display: none;">
                                    <label for="custom_page_name" class="form-label">Custom Page Name</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="custom_page_name" 
                                           placeholder="Enter custom page name (e.g., portfolio, testimonials)">
                                    <small class="form-text text-muted">Use lowercase letters and hyphens only (e.g., my-page)</small>
                                </div>

                                <!-- Title -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control @error('title') is-invalid @enderror" 
                                           id="title" 
                                           name="title" 
                                           value="{{ old('title') }}" 
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Main heading that will appear on the page</small>
                                </div>

                                <!-- Subtitle -->
                                <div class="mb-3">
                                    <label for="subtitle" class="form-label">Subtitle</label>
                                    <input type="text" 
                                           class="form-control @error('subtitle') is-invalid @enderror" 
                                           id="subtitle" 
                                           name="subtitle" 
                                           value="{{ old('subtitle') }}">
                                    @error('subtitle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Optional subtitle or description</small>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Background Image -->
                                <div class="mb-3">
                                    <label for="background_image" class="form-label">Background Image</label>
                                    <input type="file" 
                                           class="form-control @error('background_image') is-invalid @enderror" 
                                           id="background_image" 
                                           name="background_image" 
                                           accept="image/jpeg,image/png,image/jpg,image/gif,image/webp">
                                    @error('background_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Optional. Recommended size: 1920x600px. Max: 2MB</small>
                                    
                                    <!-- Image Preview -->
                                    <div id="image-preview" class="mt-3" style="display: none;">
                                        <img id="preview-img" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Status
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Only active headers will be displayed</small>
                                </div>

                                <!-- Preview Card -->
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-eye me-2"></i>Preview
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="header-preview" class="text-center p-3 border rounded" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), #f8f9fa;">
                                            <h4 id="preview-title" class="text-white mb-2">Your Title Here</h4>
                                            <p id="preview-subtitle" class="text-white-50 mb-0">Your subtitle here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.page-headers.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-2"></i>Create Header
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Handle custom page name visibility
document.getElementById('page_name').addEventListener('change', function() {
    const customDiv = document.getElementById('custom-page-name');
    const customInput = document.getElementById('custom_page_name');
    
    if (this.value === 'other') {
        customDiv.style.display = 'block';
        customInput.required = true;
    } else {
        customDiv.style.display = 'none';
        customInput.required = false;
        customInput.value = '';
    }
});

// Handle custom page name input
document.getElementById('custom_page_name').addEventListener('input', function() {
    // Just validate the input, don't update the select value yet
    this.value = this.value.toLowerCase().replace(/[^a-z0-9-]/g, '-');
});

// Image preview
document.getElementById('background_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-img').src = e.target.result;
            document.getElementById('image-preview').style.display = 'block';
            
            // Update header preview background
            document.getElementById('header-preview').style.backgroundImage = 
                `linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url(${e.target.result})`;
            document.getElementById('header-preview').style.backgroundSize = 'cover';
            document.getElementById('header-preview').style.backgroundPosition = 'center';
        };
        reader.readAsDataURL(file);
    }
});

// Live preview updates
document.getElementById('title').addEventListener('input', function() {
    document.getElementById('preview-title').textContent = this.value || 'Your Title Here';
});

document.getElementById('subtitle').addEventListener('input', function() {
    const previewSubtitle = document.getElementById('preview-subtitle');
    if (this.value) {
        previewSubtitle.textContent = this.value;
        previewSubtitle.style.display = 'block';
    } else {
        previewSubtitle.textContent = 'Your subtitle here';
        previewSubtitle.style.display = 'block';
    }
});

// Form submission
document.getElementById('headerForm').addEventListener('submit', function(e) {
    // Handle custom page name
    const pageNameSelect = document.getElementById('page_name');
    const customPageName = document.getElementById('custom_page_name');
    
    if (pageNameSelect.value === 'other' && customPageName.value) {
        pageNameSelect.value = customPageName.value.toLowerCase().replace(/[^a-z0-9-]/g, '-');
    }

    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Header...';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
@endpush
