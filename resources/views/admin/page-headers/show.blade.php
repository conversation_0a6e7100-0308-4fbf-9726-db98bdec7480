@extends('admin.layouts.dashboard')

@section('title', 'View Page Header')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>View Page Header
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.page-headers.edit', $pageHeader) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit
                        </a>
                        <a href="{{ route('admin.page-headers.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Page Information -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Page Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Page Name:</strong>
                                        <span class="badge bg-primary ms-2">{{ ucfirst($pageHeader->page_name) }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Status:</strong>
                                        @if($pageHeader->is_active)
                                            <span class="badge bg-success ms-2">Active</span>
                                        @else
                                            <span class="badge bg-danger ms-2">Inactive</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Title -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Title</h5>
                                <h3 class="mb-0">{{ $pageHeader->title }}</h3>
                            </div>

                            <!-- Subtitle -->
                            @if($pageHeader->subtitle)
                                <div class="mb-4">
                                    <h5 class="text-muted mb-2">Subtitle</h5>
                                    <p class="lead mb-0">{{ $pageHeader->subtitle }}</p>
                                </div>
                            @endif

                            <!-- Header Preview -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Header Preview</h5>
                                <div class="border rounded overflow-hidden">
                                    <div class="text-center p-5" 
                                         style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)){{ $pageHeader->background_image ? ', url(' . $pageHeader->background_image_url . ')' : ', #6c757d' }}; background-size: cover; background-position: center; min-height: 200px; display: flex; flex-direction: column; justify-content: center;">
                                        <h1 class="text-white mb-3">{{ $pageHeader->title }}</h1>
                                        @if($pageHeader->subtitle)
                                            <p class="text-white-50 lead mb-0">{{ $pageHeader->subtitle }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Metadata -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Metadata</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Created:</strong>
                                        <span class="ms-2">{{ $pageHeader->created_at->format('M d, Y \a\t g:i A') }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Last Updated:</strong>
                                        <span class="ms-2">{{ $pageHeader->updated_at->format('M d, Y \a\t g:i A') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Background Image -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Background Image</h5>
                                @if($pageHeader->background_image)
                                    <div class="text-center">
                                        <img src="{{ $pageHeader->background_image_url }}" 
                                             alt="{{ $pageHeader->title }}" 
                                             class="img-fluid rounded shadow-sm" 
                                             style="max-height: 300px;">
                                        <div class="mt-2">
                                            <a href="{{ $pageHeader->background_image_url }}" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-expand me-2"></i>View Full Size
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center text-muted py-4 border rounded">
                                        <i class="fas fa-image fa-3x mb-3"></i>
                                        <p>No background image uploaded</p>
                                        <small class="text-muted">A default background will be used</small>
                                    </div>
                                @endif
                            </div>

                            <!-- Usage Information -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Usage Information
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>Page URL:</strong></p>
                                    <code class="d-block mb-3">{{ url('/' . $pageHeader->page_name) }}</code>
                                    
                                    <p class="mb-2"><strong>Template Usage:</strong></p>
                                    <small class="text-muted">
                                        This header will be automatically loaded when the 
                                        <code>{{ $pageHeader->page_name }}.blade.php</code> template is rendered.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Last updated: {{ $pageHeader->updated_at->format('M d, Y \a\t g:i A') }}
                            </small>
                        </div>
                        <div>
                            <a href="{{ route('admin.page-headers.edit', $pageHeader) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-2"></i>Edit Header
                            </a>
                            <form action="{{ route('admin.page-headers.destroy', $pageHeader) }}" 
                                  method="POST" 
                                  class="d-inline ms-2"
                                  onsubmit="return confirm('Are you sure you want to delete this page header?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
