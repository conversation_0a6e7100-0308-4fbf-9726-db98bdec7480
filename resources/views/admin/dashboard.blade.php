@extends('admin.layouts.dashboard')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Welcome Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="content-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
            <div class="content-card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">Welcome back, {{ auth('admin')->user()->name }}! 👋</h2>
                        <p class="mb-0 opacity-75">Here's what's happening with your real estate business today.</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-inline-flex align-items-center justify-content-center bg-white bg-opacity-20 rounded-circle" style="width: 80px; height: 80px;">
                            <i class="fas fa-chart-line text-white" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card primary">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Conversion Rate</h6>
                    <h2 class="stats-value">{{ $stats['conversion_rate'] }}%</h2>
                    <p class="stats-change">
                        <i class="fas fa-chart-line text-success me-1"></i>
                        Enquiry to sale conversion
                    </p>
                </div>
                <div class="stats-icon primary">
                    <i class="fas fa-percentage"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card success">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Response Rate</h6>
                    <h2 class="stats-value">{{ $stats['response_rate'] }}%</h2>
                    <p class="stats-change">
                        <i class="fas fa-reply text-success me-1"></i>
                        Customer response rate
                    </p>
                </div>
                <div class="stats-icon success">
                    <i class="fas fa-comments"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card warning">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">New Enquiries</h6>
                    <h2 class="stats-value">{{ $stats['new_enquiries'] }}</h2>
                    <p class="stats-change">
                        <i class="fas fa-clock text-warning me-1"></i>
                        Awaiting response
                    </p>
                </div>
                <div class="stats-icon warning">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card danger">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Today's Enquiries</h6>
                    <h2 class="stats-value">{{ $stats['today_enquiries'] }}</h2>
                    <p class="stats-change">
                        <i class="fas fa-calendar-day text-info me-1"></i>
                        Received today
                    </p>
                </div>
                <div class="stats-icon danger">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Project & Content Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card info">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Total Projects</h6>
                    <h2 class="stats-value">{{ $stats['total_projects'] }}</h2>
                    <p class="stats-change">
                        <i class="fas fa-building text-info me-1"></i>
                        {{ $stats['active_projects'] }} active
                    </p>
                </div>
                <div class="stats-icon info">
                    <i class="fas fa-building"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card purple">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Total Enquiries</h6>
                    <h2 class="stats-value">{{ $stats['total_enquiries'] }}</h2>
                    <p class="stats-change">
                        <i class="fas fa-chart-bar text-purple me-1"></i>
                        {{ $stats['converted_enquiries'] }} converted
                    </p>
                </div>
                <div class="stats-icon purple">
                    <i class="fas fa-clipboard-list"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card secondary">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Blog Posts</h6>
                    <h2 class="stats-value">{{ $stats['total_blogs'] }}</h2>
                    <p class="stats-change">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        {{ $stats['published_blogs'] }} published
                    </p>
                </div>
                <div class="stats-icon secondary">
                    <i class="fas fa-blog"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card dark">
            <div class="stats-header">
                <div>
                    <h6 class="stats-title">Subscribers</h6>
                    <h2 class="stats-value">{{ $stats['active_subscribers'] }}</h2>
                    <p class="stats-change">
                        <i class="fas fa-arrow-up text-success me-1"></i>
                        {{ $stats['this_week_subscribers'] }} this week
                    </p>
                </div>
                <div class="stats-icon dark">
                    <i class="fas fa-newspaper"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Recent Enquiries -->
        <div class="content-card mb-4">
            <div class="content-card-header d-flex justify-content-between align-items-center">
                <h5 class="content-card-title">
                    <i class="fas fa-clipboard-list"></i>
                    Recent Project Enquiries
                </h5>
                <a href="{{ route('admin.project-enquiries.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="content-card-body">
                @if($recentEnquiries->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Project</th>
                                    <th>Interest</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentEnquiries as $enquiry)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $enquiry->name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $enquiry->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ Str::limit($enquiry->project_name, 20) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info badge-sm">{{ $enquiry->interest_display }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $enquiry->status_badge_class }} badge-sm">{{ $enquiry->status_display }}</span>
                                    </td>
                                    <td>
                                        <small>{{ $enquiry->created_at->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.project-enquiries.show', $enquiry) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">No enquiries yet</h6>
                        <p class="text-muted">Project enquiries will appear here when customers show interest.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Activity Timeline -->
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-activity"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="content-card-body">
                <div class="timeline">
                    @if($recentContacts->count() > 0)
                        @foreach($recentContacts as $contact)
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div>
                                <h6 class="timeline-title">New Contact Submission</h6>
                                <p class="timeline-text">{{ $contact->name }} submitted a contact form: "{{ Str::limit($contact->subject, 50) }}"</p>
                                <div class="timeline-time">{{ $contact->created_at->format('M d, Y \a\t g:i A') }}</div>
                            </div>
                        </div>
                        @endforeach
                    @endif

                    @if($recentBlogs->count() > 0)
                        @foreach($recentBlogs->take(2) as $blog)
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div>
                                <h6 class="timeline-title">Blog Post Published</h6>
                                <p class="timeline-text">New blog post "{{ $blog->title }}" was published in {{ $blog->blogCategory->name ?? 'General' }} category.</p>
                                <div class="timeline-time">{{ $blog->created_at->format('M d, Y \a\t g:i A') }}</div>
                            </div>
                        </div>
                        @endforeach
                    @endif

                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div>
                            <h6 class="timeline-title">System Status</h6>
                            <p class="timeline-text">All systems are running smoothly. Dashboard loaded successfully.</p>
                            <div class="timeline-time">{{ now()->format('M d, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Statistics Summary -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-chart-pie"></i>
                    Quick Stats
                </h5>
            </div>
            <div class="content-card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <div class="fw-bold text-primary fs-4">{{ $stats['this_week_enquiries'] }}</div>
                            <small class="text-muted">This Week</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="fw-bold text-success fs-4">{{ $stats['this_month_enquiries'] }}</div>
                        <small class="text-muted">This Month</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fw-bold text-warning fs-4">{{ $stats['contacted_enquiries'] }}</div>
                            <small class="text-muted">Contacted</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold text-info fs-4">{{ $stats['featured_projects'] }}</div>
                        <small class="text-muted">Featured</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Urgent Actions -->
        @if($stats['new_enquiries'] > 0 || $stats['new_contacts'] > 0)
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Urgent Actions
                </h5>
            </div>
            <div class="content-card-body">
                @if($stats['new_enquiries'] > 0)
                <div class="alert alert-warning d-flex align-items-center mb-3">
                    <i class="fas fa-bell me-2"></i>
                    <div class="flex-grow-1">
                        <strong>{{ $stats['new_enquiries'] }} new enquiries</strong> waiting for response
                    </div>
                    <a href="{{ route('admin.project-enquiries.index') }}" class="btn btn-sm btn-warning">View</a>
                </div>
                @endif

                @if($stats['new_contacts'] > 0)
                <div class="alert alert-info d-flex align-items-center mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    <div class="flex-grow-1">
                        <strong>{{ $stats['new_contacts'] }} new contacts</strong> to review
                    </div>
                    <a href="{{ route('admin.contact-submissions.index') }}" class="btn btn-sm btn-info">View</a>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Quick Actions -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="quick-actions">
                    <a href="{{ route('admin.projects.create') }}" class="quick-action-btn">
                        <i class="fas fa-plus-circle text-primary"></i>
                        <span>Add New Project</span>
                    </a>
                    <a href="{{ route('admin.project-enquiries.index') }}" class="quick-action-btn">
                        <i class="fas fa-clipboard-list text-warning"></i>
                        <span>Manage Enquiries</span>
                        @if($stats['new_enquiries'] > 0)
                            <span class="badge badge-warning badge-sm ms-auto">{{ $stats['new_enquiries'] }}</span>
                        @endif
                    </a>
                    <a href="{{ route('admin.blogs.create') }}" class="quick-action-btn">
                        <i class="fas fa-pen-alt text-success"></i>
                        <span>Write Blog Post</span>
                    </a>
                    <a href="{{ route('admin.galleries.create') }}" class="quick-action-btn">
                        <i class="fas fa-camera-retro text-info"></i>
                        <span>Upload Gallery</span>
                    </a>
                    <a href="{{ route('admin.contact-submissions.index') }}" class="quick-action-btn">
                        <i class="fas fa-envelope text-danger"></i>
                        <span>Contact Submissions</span>
                        @if($stats['new_contacts'] > 0)
                            <span class="badge badge-danger badge-sm ms-auto">{{ $stats['new_contacts'] }}</span>
                        @endif
                    </a>
                </div>
            </div>
        </div>

        <!-- Admin Profile -->
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-user-circle"></i>
                    Admin Profile
                </h5>
            </div>
            <div class="content-card-body">
                <div class="text-center mb-3">
                    <div class="d-inline-flex align-items-center justify-content-center bg-primary rounded-circle" style="width: 60px; height: 60px;">
                        <i class="fas fa-user text-white fs-4"></i>
                    </div>
                </div>
                <h6 class="text-center mb-3">{{ auth('admin')->user()->name }}</h6>
                <div class="small text-muted text-center">
                    <div class="mb-2">
                        <i class="fas fa-envelope me-1"></i>
                        {{ auth('admin')->user()->email }}
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-clock me-1"></i>
                        Last login: {{ now()->format('M d, Y') }}
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.profile') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-user-edit me-1"></i>
                        Update Profile
                    </a>
                    <a href="{{ route('admin.password.reset') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-shield-alt me-1"></i>
                        Change Password
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
