@extends('admin.layouts.dashboard')

@section('title', 'Contact Submission Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Contact Submission Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.contact-submissions.index') }}">Contact Submissions</a></li>
                    <li class="breadcrumb-item active">{{ $contactSubmission->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.contact-submissions.edit', $contactSubmission) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Update Status
            </a>
            <a href="{{ route('admin.contact-submissions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Contact Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                    {!! $contactSubmission->status_badge !!}
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Full Name:</label>
                                <p class="mb-2">{{ $contactSubmission->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Email Address:</label>
                                <p class="mb-2">
                                    <a href="mailto:{{ $contactSubmission->email }}">{{ $contactSubmission->email }}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Mobile Number:</label>
                                <p class="mb-2">
                                    <a href="tel:{{ $contactSubmission->mobile }}">{{ $contactSubmission->mobile }}</a>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Submission Date:</label>
                                <p class="mb-2">
                                    {{ $contactSubmission->created_at->format('F d, Y \a\t g:i A') }}
                                    <small class="text-muted">({{ $contactSubmission->time_ago }})</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Requirements -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Property Requirements</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="font-weight-bold">Property Type:</label>
                                <p class="mb-2">
                                    <span class="badge badge-info">{{ $contactSubmission->property_type_display }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="font-weight-bold">Budget Range:</label>
                                <p class="mb-2">
                                    <span class="badge badge-success">{{ $contactSubmission->budget_range_display }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="font-weight-bold">Preferred Location:</label>
                                <p class="mb-2">
                                    <span class="badge badge-warning">{{ $contactSubmission->location_display }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Message</h6>
                </div>
                <div class="card-body">
                    <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ $contactSubmission->message }}</p>
                    </div>
                </div>
            </div>

            <!-- Admin Notes -->
            @if($contactSubmission->admin_notes)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Admin Notes</h6>
                    </div>
                    <div class="card-body">
                        <div class="bg-warning bg-opacity-10 p-3 rounded border-left border-warning">
                            <p class="mb-0">{{ $contactSubmission->admin_notes }}</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ $contactSubmission->email }}?subject=Re: Your Property Inquiry&body=Dear {{ $contactSubmission->name }},%0D%0A%0D%0AThank you for your inquiry about {{ $contactSubmission->property_type_display }} properties in {{ $contactSubmission->location_display }}.%0D%0A%0D%0ABest regards,%0D%0AHestia Abodes Team" 
                           class="btn btn-primary">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                        <a href="tel:{{ $contactSubmission->mobile }}" class="btn btn-success">
                            <i class="fas fa-phone"></i> Call Customer
                        </a>
                        <a href="{{ route('admin.contact-submissions.edit', $contactSubmission) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Update Status
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Information</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Submission Received</h6>
                                <p class="text-muted mb-0">{{ $contactSubmission->created_at->format('M d, Y \a\t g:i A') }}</p>
                            </div>
                        </div>
                        
                        @if($contactSubmission->contacted_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Customer Contacted</h6>
                                    <p class="text-muted mb-0">{{ $contactSubmission->contacted_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($contactSubmission->updated_at != $contactSubmission->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Last Updated</h6>
                                    <p class="text-muted mb-0">{{ $contactSubmission->updated_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Technical Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Technical Information</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="mb-2">
                            <strong>Submission ID:</strong> #{{ $contactSubmission->id }}
                        </div>
                        @if($contactSubmission->ip_address)
                            <div class="mb-2">
                                <strong>IP Address:</strong> {{ $contactSubmission->ip_address }}
                            </div>
                        @endif
                        @if($contactSubmission->user_agent)
                            <div class="mb-2">
                                <strong>User Agent:</strong> 
                                <span class="text-muted">{{ Str::limit($contactSubmission->user_agent, 50) }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content h6 {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.timeline-content p {
    font-size: 0.75rem;
}
</style>
@endsection
