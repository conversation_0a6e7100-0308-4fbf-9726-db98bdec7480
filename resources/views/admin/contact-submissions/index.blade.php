@extends('admin.layouts.dashboard')

@section('title', 'Contact Submissions')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Contact Submissions</h1>
            <p class="mb-0 text-muted">Manage customer inquiries and contact form submissions</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Submissions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">New</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['new'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">In Progress</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['in_progress'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Closed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['closed'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.contact-submissions.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                @foreach(\App\Models\ContactSubmission::getStatusOptions() as $value => $label)
                                    <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="property_type">Property Type</label>
                            <select name="property_type" id="property_type" class="form-control">
                                <option value="">All Types</option>
                                @foreach(\App\Models\ContactSubmission::getPropertyTypeOptions() as $value => $label)
                                    <option value="{{ $value }}" {{ request('property_type') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" placeholder="Name, email, mobile..." value="{{ request('search') }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('admin.contact-submissions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Submissions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Contact Submissions</h6>
            <div>
                <button type="button" class="btn btn-sm btn-info" onclick="toggleBulkActions()">
                    <i class="fas fa-tasks"></i> Bulk Actions
                </button>
            </div>
        </div>
        <div class="card-body">
            @if($submissions->count() > 0)
                <!-- Bulk Actions Form (Hidden by default) -->
                <div id="bulkActionsForm" style="display: none;" class="mb-3 p-3 bg-light rounded">
                    <form method="POST" action="{{ route('admin.contact-submissions.bulk-update') }}">
                        @csrf
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <label for="bulk_status">Update Status To:</label>
                                <select name="bulk_status" id="bulk_status" class="form-control" required>
                                    @foreach(\App\Models\ContactSubmission::getStatusOptions() as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Update Selected
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">
                                    Cancel
                                </button>
                            </div>
                        </div>
                        <div id="selectedSubmissions"></div>
                    </form>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Property Details</th>
                                <th>Status</th>
                                <th>Submitted</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($submissions as $submission)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="submission-checkbox" value="{{ $submission->id }}">
                                    </td>
                                    <td>
                                        <strong>{{ $submission->name }}</strong>
                                        @if($submission->status === 'new')
                                            <span class="badge badge-primary badge-sm ml-1">NEW</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="small">
                                            <i class="fas fa-envelope"></i> {{ $submission->email }}<br>
                                            <i class="fas fa-phone"></i> {{ $submission->mobile }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <strong>Type:</strong> {{ $submission->property_type_display }}<br>
                                            <strong>Budget:</strong> {{ $submission->budget_range_display }}<br>
                                            <strong>Location:</strong> {{ $submission->location_display }}
                                        </div>
                                    </td>
                                    <td>{!! $submission->status_badge !!}</td>
                                    <td>
                                        <div class="small">
                                            {{ $submission->created_at->format('M d, Y') }}<br>
                                            <span class="text-muted">{{ $submission->time_ago }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.contact-submissions.show', $submission) }}" 
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.contact-submissions.edit', $submission) }}" 
                                               class="btn btn-sm btn-warning" title="Edit Status">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="{{ route('admin.contact-submissions.destroy', $submission) }}" 
                                                  style="display: inline;" 
                                                  onsubmit="return confirm('Are you sure you want to delete this submission?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $submissions->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No contact submissions found</h5>
                    <p class="text-muted">Contact submissions will appear here when customers submit the contact form.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function toggleBulkActions() {
    const form = document.getElementById('bulkActionsForm');
    const isVisible = form.style.display !== 'none';
    form.style.display = isVisible ? 'none' : 'block';
    
    if (!isVisible) {
        updateSelectedSubmissions();
    }
}

function updateSelectedSubmissions() {
    const checkboxes = document.querySelectorAll('.submission-checkbox:checked');
    const container = document.getElementById('selectedSubmissions');
    
    container.innerHTML = '';
    checkboxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'submissions[]';
        input.value = checkbox.value;
        container.appendChild(input);
    });
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.submission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedSubmissions();
});

// Update selected submissions when individual checkboxes change
document.querySelectorAll('.submission-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedSubmissions);
});
</script>
@endsection
