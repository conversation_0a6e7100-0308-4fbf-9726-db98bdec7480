@extends('admin.layouts.dashboard')

@section('title', 'Update Contact Submission')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Update Contact Submission</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.contact-submissions.index') }}">Contact Submissions</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.contact-submissions.show', $contactSubmission) }}">{{ $contactSubmission->name }}</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.contact-submissions.show', $contactSubmission) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Details
            </a>
            <a href="{{ route('admin.contact-submissions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Update Form -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Update Status & Notes</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.contact-submissions.update', $contactSubmission) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="font-weight-bold">Status <span class="text-danger">*</span></label>
                                    <select name="status" id="status" class="form-control @error('status') is-invalid @enderror" required>
                                        @foreach(\App\Models\ContactSubmission::getStatusOptions() as $value => $label)
                                            <option value="{{ $value }}" {{ old('status', $contactSubmission->status) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Update the status based on your interaction with the customer.
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Current Status</label>
                                    <div class="mt-2">
                                        {!! $contactSubmission->status_badge !!}
                                        @if($contactSubmission->contacted_at)
                                            <div class="small text-muted mt-1">
                                                Contacted on {{ $contactSubmission->contacted_at->format('M d, Y \a\t g:i A') }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="admin_notes" class="font-weight-bold">Admin Notes</label>
                            <textarea name="admin_notes" id="admin_notes" rows="5" 
                                      class="form-control @error('admin_notes') is-invalid @enderror" 
                                      placeholder="Add notes about your interaction with the customer, follow-up actions, or any other relevant information...">{{ old('admin_notes', $contactSubmission->admin_notes) }}</textarea>
                            @error('admin_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                These notes are for internal use and will help track the progress of this inquiry.
                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Submission
                            </button>
                            <a href="{{ route('admin.contact-submissions.show', $contactSubmission) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information Summary -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact Summary</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle bg-primary text-white mb-2">
                            {{ strtoupper(substr($contactSubmission->name, 0, 2)) }}
                        </div>
                        <h6 class="mb-1">{{ $contactSubmission->name }}</h6>
                        <p class="text-muted small mb-0">Submitted {{ $contactSubmission->time_ago }}</p>
                    </div>

                    <hr>

                    <div class="contact-info">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope text-primary mr-2"></i>
                            <small>{{ $contactSubmission->email }}</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-phone text-success mr-2"></i>
                            <small>{{ $contactSubmission->mobile }}</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-home text-info mr-2"></i>
                            <small>{{ $contactSubmission->property_type_display }}</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-rupee-sign text-warning mr-2"></i>
                            <small>{{ $contactSubmission->budget_range_display }}</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-map-marker-alt text-danger mr-2"></i>
                            <small>{{ $contactSubmission->location_display }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ $contactSubmission->email }}?subject=Re: Your Property Inquiry&body=Dear {{ $contactSubmission->name }},%0D%0A%0D%0AThank you for your inquiry about {{ $contactSubmission->property_type_display }} properties in {{ $contactSubmission->location_display }}.%0D%0A%0D%0ABest regards,%0D%0AHestia Abodes Team" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                        <a href="tel:{{ $contactSubmission->mobile }}" class="btn btn-success btn-sm">
                            <i class="fas fa-phone"></i> Call Customer
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status Guide -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Guide</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="mb-2">
                            <span class="badge bg-primary">New</span>
                            <span class="ml-2">Fresh submission, not yet contacted</span>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-info">Contacted</span>
                            <span class="ml-2">Customer has been reached out to</span>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-warning">In Progress</span>
                            <span class="ml-2">Actively working on the inquiry</span>
                        </div>
                        <div class="mb-0">
                            <span class="badge bg-success">Closed</span>
                            <span class="ml-2">Inquiry resolved or completed</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto;
}

.contact-info i {
    width: 16px;
    text-align: center;
}
</style>
@endsection
