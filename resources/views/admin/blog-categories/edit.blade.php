@extends('admin.layouts.dashboard')

@section('title', 'Edit Blog Category')
@section('page-title', 'Edit Blog Category')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<form method="POST" action="{{ route('admin.blog-categories.update', $blogCategory) }}" id="categoryEditForm">
    @csrf
    @method('PUT')
    
    <div class="row">
        <div class="col-lg-8">
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-info-circle"></i>
                        Category Information
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name', $blogCategory->name) }}" 
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Current slug: <code>{{ $blogCategory->slug }}</code></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="color" class="form-label">Category Color <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="color" 
                                           class="form-control form-control-color @error('color') is-invalid @enderror" 
                                           id="color" 
                                           name="color" 
                                           value="{{ old('color', $blogCategory->color) }}" 
                                           required>
                                    <input type="text" 
                                           class="form-control" 
                                           id="colorText" 
                                           value="{{ old('color', $blogCategory->color) }}" 
                                           readonly>
                                </div>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3" 
                                  placeholder="Brief description of this category...">{{ old('description', $blogCategory->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Preview -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-eye"></i>
                        Preview
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="category-preview p-3 border rounded">
                        <div class="d-flex align-items-center mb-2">
                            <div class="category-color-preview me-3" 
                                 style="width: 20px; height: 20px; background-color: {{ $blogCategory->color }}; border-radius: 50%; border: 2px solid #dee2e6;"></div>
                            <div>
                                <h6 class="mb-0" id="previewName">{{ $blogCategory->name }}</h6>
                                <small class="text-muted" id="previewSlug">{{ $blogCategory->slug }}</small>
                            </div>
                        </div>
                        <p class="text-muted mb-0" id="previewDescription">{{ $blogCategory->description ?: 'No description provided' }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-chart-bar"></i>
                        Statistics
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <strong>Total Blog Posts:</strong><br>
                        <span class="badge bg-info">{{ $blogCategory->blogs_count }} posts</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small class="text-muted">{{ $blogCategory->created_at->format('M d, Y H:i') }}</small>
                    </div>
                    
                    <div class="mb-0">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ $blogCategory->updated_at->format('M d, Y H:i') }}</small>
                    </div>
                </div>
            </div>
            
            <!-- Settings -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-cogs"></i>
                        Settings
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="status" name="status" value="1" {{ old('status', $blogCategory->status) ? 'checked' : '' }}>
                        <label class="form-check-label" for="status">
                            <i class="fas fa-eye text-success me-1"></i>
                            Active Status
                        </label>
                        <small class="form-text text-muted d-block">Active categories will be visible on the website</small>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="content-card">
                <div class="content-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            Update Category
                        </button>
                        <a href="{{ route('admin.blog-categories.show', $blogCategory) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>
                            View Category
                        </a>
                        <a href="{{ route('admin.blog-categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Categories
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
// Generate slug from name
function generateSlug(text) {
    return text.toLowerCase()
        .replace(/[^\w ]+/g, '')
        .replace(/ +/g, '-');
}

// Update preview
function updatePreview() {
    const name = document.getElementById('name').value || '{{ $blogCategory->name }}';
    const description = document.getElementById('description').value || 'No description provided';
    const color = document.getElementById('color').value;
    
    document.getElementById('previewName').textContent = name;
    document.getElementById('previewDescription').textContent = description;
    document.querySelector('.category-color-preview').style.backgroundColor = color;
}

// Color picker sync
document.getElementById('color').addEventListener('input', function() {
    document.getElementById('colorText').value = this.value;
    updatePreview();
});

// Name input
document.getElementById('name').addEventListener('input', updatePreview);

// Description input
document.getElementById('description').addEventListener('input', updatePreview);

// Form submission
document.getElementById('categoryEditForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Category...';
    submitBtn.disabled = true;
    
    // Re-enable button after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
@endpush

@push('styles')
<style>
.category-preview {
    background-color: #f8f9fa;
}

.form-control-color {
    width: 60px;
    height: 38px;
    padding: 0.375rem 0.5rem;
}

.category-color-preview {
    transition: background-color 0.3s ease;
}
</style>
@endpush
