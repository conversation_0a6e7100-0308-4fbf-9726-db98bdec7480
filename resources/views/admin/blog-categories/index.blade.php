@extends('admin.layouts.dashboard')

@section('title', 'Blog Categories')
@section('page-title', 'Blog Categories')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0">Manage Blog Categories</h4>
        <p class="text-muted mb-0">Organize your blog posts with categories</p>
    </div>
    <a href="{{ route('admin.blog-categories.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Add New Category
    </a>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error') || $errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') ?: $errors->first() }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif
<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogCategories->total() }}</h3>
                    <p class="stats-label">Total Categories</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogCategories->where('status', true)->count() }}</h3>
                    <p class="stats-label">Active Categories</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-info">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogCategories->sum('blogs_count') }}</h3>
                    <p class="stats-label">Total Posts</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogCategories->where('created_at', '>=', now()->subDays(30))->count() }}</h3>
                    <p class="stats-label">This Month</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content-card">
    <div class="content-card-body">
        @if($blogCategories->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Description</th>
                            <th>Color</th>
                            <th>Posts</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($blogCategories as $category)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="category-color me-3" style="width: 20px; height: 20px; background-color: {{ $category->color }}; border-radius: 50%;"></div>
                                    <div>
                                        <h6 class="mb-0">{{ $category->name }}</h6>
                                        <small class="text-muted">{{ $category->slug }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-muted">{{ Str::limit($category->description, 50) ?: 'No description' }}</span>
                            </td>
                            <td>
                                <span class="badge" style="background-color: {{ $category->color }}; color: white;">
                                    {{ $category->color }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $category->blogs_count }} posts</span>
                            </td>
                            <td>
                                @if($category->status)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                            <td>
                                <small class="text-muted">{{ $category->created_at->format('M d, Y') }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.blog-categories.show', $category) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.blog-categories.edit', $category) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" 
                                          action="{{ route('admin.blog-categories.destroy', $category) }}" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this category?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-danger" 
                                                title="Delete"
                                                {{ $category->blogs_count > 0 ? 'disabled' : '' }}>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $blogCategories->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Blog Categories Found</h5>
                <p class="text-muted">Create your first blog category to organize your posts.</p>
                <a href="{{ route('admin.blog-categories.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create First Category
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@push('styles')
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card-body {
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.5rem;
}

.stats-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
}

.stats-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.category-color {
    border: 2px solid #dee2e6;
}
</style>
@endpush
