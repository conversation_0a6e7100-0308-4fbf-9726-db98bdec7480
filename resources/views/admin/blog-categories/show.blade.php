@extends('admin.layouts.dashboard')

@section('title', 'View Blog Category')
@section('page-title', 'Blog Category Details')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="content-card mb-4">
            <div class="content-card-header d-flex justify-content-between align-items-center">
                <h5 class="content-card-title">
                    <div class="d-flex align-items-center">
                        <div class="category-color me-3" style="width: 30px; height: 30px; background-color: {{ $blogCategory->color }}; border-radius: 50%; border: 2px solid #dee2e6;"></div>
                        {{ $blogCategory->name }}
                    </div>
                </h5>
                <div>
                    @if($blogCategory->status)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-danger">Inactive</span>
                    @endif
                </div>
            </div>
            <div class="content-card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Category Name</h6>
                        <p class="mb-3">{{ $blogCategory->name }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">URL Slug</h6>
                        <p class="mb-3"><code>{{ $blogCategory->slug }}</code></p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Category Color</h6>
                        <div class="d-flex align-items-center">
                            <div class="category-color me-3" style="width: 25px; height: 25px; background-color: {{ $blogCategory->color }}; border-radius: 50%; border: 2px solid #dee2e6;"></div>
                            <span class="badge" style="background-color: {{ $blogCategory->color }}; color: white;">
                                {{ $blogCategory->color }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Total Blog Posts</h6>
                        <p class="mb-3">
                            <span class="badge bg-info fs-6">{{ $blogCategory->blogs_count }} posts</span>
                        </p>
                    </div>
                </div>
                
                @if($blogCategory->description)
                <div class="mb-4">
                    <h6 class="text-muted">Description</h6>
                    <p>{{ $blogCategory->description }}</p>
                </div>
                @endif
                
                <!-- Recent Blog Posts -->
                @if($blogCategory->blogs->count() > 0)
                <div class="mb-4">
                    <h6 class="text-muted">Recent Blog Posts</h6>
                    <div class="list-group">
                        @foreach($blogCategory->blogs as $blog)
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $blog->title }}</h6>
                                    <p class="mb-1 text-muted">{{ Str::limit($blog->excerpt, 100) }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $blog->formatted_published_at }}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-eye me-1"></i>
                                        {{ $blog->views }} views
                                        @if($blog->featured)
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            Featured
                                        @endif
                                    </small>
                                </div>
                                <div class="ms-3">
                                    <a href="{{ route('admin.blogs.show', $blog) }}" class="btn btn-sm btn-outline-primary">
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    @if($blogCategory->blogs_count > 10)
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.blogs.index') }}?category={{ $blogCategory->slug }}" class="btn btn-outline-primary">
                            View All {{ $blogCategory->blogs_count }} Posts
                        </a>
                    </div>
                    @endif
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No Blog Posts Yet</h6>
                    <p class="text-muted">This category doesn't have any blog posts yet.</p>
                    <a href="{{ route('admin.blogs.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Post
                    </a>
                </div>
                @endif
            </div>
            <div class="card-footer bg-transparent">
                <a href="{{ route('admin.blog-categories.edit', $blogCategory) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    Edit Category
                </a>
                <a href="{{ route('admin.blog-categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Categories
                </a>
                <form method="POST" 
                      action="{{ route('admin.blog-categories.destroy', $blogCategory) }}" 
                      class="d-inline"
                      onsubmit="return confirm('Are you sure you want to delete this category?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="btn btn-outline-danger"
                            {{ $blogCategory->blogs_count > 0 ? 'disabled' : '' }}>
                        <i class="fas fa-trash me-2"></i>
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-info-circle"></i>
                    Category Info
                </h5>
            </div>
            <div class="content-card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $blogCategory->created_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $blogCategory->updated_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    @if($blogCategory->status)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-danger">Inactive</span>
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>URL Slug:</strong><br>
                    <code>{{ $blogCategory->slug }}</code>
                </div>
                
                <div class="mb-0">
                    <strong>Total Posts:</strong><br>
                    <span class="badge bg-info">{{ $blogCategory->blogs_count }} posts</span>
                </div>
            </div>
        </div>
        
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-cogs"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.blog-categories.edit', $blogCategory) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit Category
                    </a>
                    <a href="{{ route('admin.blogs.create') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus me-2"></i>
                        Create New Post
                    </a>
                    <a href="{{ route('admin.blog-categories.create') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-folder-plus me-2"></i>
                        Create New Category
                    </a>
                    @if($blogCategory->status)
                    <a href="{{ url('/blog?category=' . $blogCategory->slug) }}" 
                       target="_blank" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View on Website
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
