@extends('admin.layouts.dashboard')

@section('title', 'View Project')
@section('page-title', 'Project Details')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="content-card mb-4">
            <div class="content-card-header d-flex justify-content-between align-items-center">
                <h5 class="content-card-title">
                    <i class="fas fa-building"></i>
                    {{ $project->title }}
                </h5>
                <div>
                    @if($project->featured)
                        <span class="badge bg-warning me-2">
                            <i class="fas fa-star"></i> Featured
                        </span>
                    @endif
                    <span class="badge bg-{{ $project->is_active ? 'success' : 'danger' }}">
                        {{ $project->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
            <div class="content-card-body">
                <!-- Project Images -->
                @if($project->images && count($project->images) > 0)
                <div class="mb-4">
                    <div id="projectCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            @foreach($project->images as $index => $image)
                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                <img src="{{ asset('storage/' . $image) }}" 
                                     class="d-block w-100 rounded" 
                                     alt="{{ $project->title }}"
                                     style="height: 400px; object-fit: cover;">
                            </div>
                            @endforeach
                        </div>
                        @if(count($project->images) > 1)
                        <button class="carousel-control-prev" type="button" data-bs-target="#projectCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#projectCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                        @endif
                    </div>
                </div>
                @endif
                
                <!-- Project Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Location</h6>
                        <p class="mb-3">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            {{ $project->location }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Category</h6>
                        <p class="mb-3">
                            <span class="badge bg-primary fs-6">{{ $project->projectCategory->name }}</span>
                        </p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4">
                        <h6 class="text-muted">Type</h6>
                        <p class="mb-3">{{ $project->type }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Status</h6>
                        <p class="mb-3">
                            <span class="badge bg-info">{{ $project->status }}</span>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Possession</h6>
                        <p class="mb-3">{{ $project->possession }}</p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Price Range</h6>
                        <p class="mb-3 fw-bold text-success fs-5">{{ $project->price_range }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Area Range</h6>
                        <p class="mb-3 fw-bold">{{ $project->area_range }}</p>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted">Description</h6>
                    <p>{{ $project->description }}</p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Configurations</h6>
                        <div class="d-flex flex-wrap gap-2">
                            @foreach($project->configurations as $config)
                                <span class="badge bg-light text-dark border">{{ $config }}</span>
                            @endforeach
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Developer</h6>
                        <p class="mb-0">{{ $project->developer }}</p>
                        @if($project->rera_number)
                            <small class="text-muted">RERA: {{ $project->rera_number }}</small>
                        @endif
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted">Amenities</h6>
                    <div class="row">
                        @foreach($project->amenities as $amenity)
                            <div class="col-md-6 col-lg-4 mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {{ $amenity }}
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <a href="{{ route('admin.projects.edit', $project) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    Edit Project
                </a>
                <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Projects
                </a>
                <form method="POST" 
                      action="{{ route('admin.projects.destroy', $project) }}" 
                      class="d-inline"
                      onsubmit="return confirm('Are you sure you want to delete this project?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-info-circle"></i>
                    Project Info
                </h5>
            </div>
            <div class="content-card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $project->created_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $project->updated_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Slug:</strong><br>
                    <code>{{ $project->slug }}</code>
                </div>
                
                <div class="mb-3">
                    <strong>Total Images:</strong><br>
                    <span class="badge bg-info">{{ count($project->images) }} images</span>
                </div>
            </div>
        </div>
        
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-cogs"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.projects.edit', $project) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit Project
                    </a>
                    <a href="{{ route('admin.projects.create') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus me-2"></i>
                        Create New Project
                    </a>
                    <a href="{{ url('/project-details/' . $project->slug) }}" 
                       target="_blank" 
                       class="btn btn-outline-info btn-sm">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View on Website
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
