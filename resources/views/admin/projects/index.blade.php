@extends('admin.layouts.dashboard')

@section('title', 'Projects Management')
@section('page-title', 'Projects Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="content-card">
            <div class="content-card-header d-flex justify-content-between align-items-center">
                <h5 class="content-card-title">
                    <i class="fas fa-building"></i>
                    All Projects
                </h5>
                <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Add New Project
                </a>
            </div>
            <div class="content-card-body">
                @if($projects->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Project</th>
                                    <th>Category</th>
                                    <th>Location</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Price Range</th>
                                    <th>Featured</th>
                                    <th>Active</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($projects as $project)
                                <tr>
                                    <td>{{ $loop->iteration + ($projects->currentPage() - 1) * $projects->perPage() }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($project->first_image)
                                                <img src="{{ asset('storage/' . $project->first_image) }}" 
                                                     alt="{{ $project->title }}" 
                                                     class="rounded me-3" 
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <strong>{{ $project->title }}</strong><br>
                                                <small class="text-muted">{{ $project->developer }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $project->projectCategory->name }}</span>
                                    </td>
                                    <td>{{ $project->location }}</td>
                                    <td>{{ $project->type }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $project->status }}</span>
                                    </td>
                                    <td>{{ $project->price_range }}</td>
                                    <td>
                                        @if($project->featured)
                                            <span class="badge bg-warning">
                                                <i class="fas fa-star"></i> Featured
                                            </span>
                                        @else
                                            <span class="badge bg-light text-dark">Regular</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $project->is_active ? 'success' : 'danger' }}">
                                            {{ $project->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.projects.show', $project) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.projects.edit', $project) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" 
                                                  action="{{ route('admin.projects.destroy', $project) }}" 
                                                  class="d-inline"
                                                  onsubmit="return confirm('Are you sure you want to delete this project?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn btn-sm btn-outline-danger" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $projects->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Projects Found</h5>
                        <p class="text-muted">Get started by creating your first project.</p>
                        <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Add New Project
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #64748b;
    font-size: 0.875rem;
}
.table td {
    vertical-align: middle;
}
.btn-group .btn {
    border-radius: 6px !important;
    margin-right: 2px;
}
</style>
@endpush
