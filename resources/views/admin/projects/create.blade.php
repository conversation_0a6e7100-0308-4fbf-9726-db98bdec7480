@extends('admin.layouts.dashboard')

@section('title', 'Create Project')
@section('page-title', 'Create New Project')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<form method="POST" action="{{ route('admin.projects.store') }}" enctype="multipart/form-data" id="projectForm">
    @csrf
    
    <div class="row">
        <div class="col-lg-8">
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-info-circle"></i>
                        Basic Information
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Project Title <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('title') is-invalid @enderror" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title') }}" 
                                       required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="project_category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select @error('project_category_id') is-invalid @enderror" 
                                        id="project_category_id" 
                                        name="project_category_id" 
                                        required>
                                    <option value="">Select Category</option>
                                    @foreach($projectCategories as $category)
                                        <option value="{{ $category->id }}" {{ old('project_category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('project_category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('location') is-invalid @enderror" 
                                       id="location" 
                                       name="location" 
                                       value="{{ old('location') }}" 
                                       required>
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" 
                                        name="type" 
                                        required>
                                    <option value="">Select Type</option>
                                    <option value="Residential Apartment" {{ old('type') == 'Residential Apartment' ? 'selected' : '' }}>Residential Apartment</option>
                                    <option value="Villa" {{ old('type') == 'Villa' ? 'selected' : '' }}>Villa</option>
                                    <option value="Commercial" {{ old('type') == 'Commercial' ? 'selected' : '' }}>Commercial</option>
                                    <option value="Plot" {{ old('type') == 'Plot' ? 'selected' : '' }}>Plot</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" 
                                        name="status" 
                                        required>
                                    <option value="">Select Status</option>
                                    <option value="Ready to Move" {{ old('status') == 'Ready to Move' ? 'selected' : '' }}>Ready to Move</option>
                                    <option value="Under Construction" {{ old('status') == 'Under Construction' ? 'selected' : '' }}>Under Construction</option>
                                    <option value="Pre-Launch" {{ old('status') == 'Pre-Launch' ? 'selected' : '' }}>Pre-Launch</option>
                                    <option value="Booking Open" {{ old('status') == 'Booking Open' ? 'selected' : '' }}>Booking Open</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="price_range" class="form-label">Price Range <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('price_range') is-invalid @enderror" 
                                       id="price_range" 
                                       name="price_range" 
                                       value="{{ old('price_range') }}" 
                                       placeholder="₹85 Lakhs - ₹1.2 Cr"
                                       required>
                                @error('price_range')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="area_range" class="form-label">Area Range <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('area_range') is-invalid @enderror" 
                                       id="area_range" 
                                       name="area_range" 
                                       value="{{ old('area_range') }}" 
                                       placeholder="1200-1800 sq ft"
                                       required>
                                @error('area_range')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="possession" class="form-label">Possession <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('possession') is-invalid @enderror" 
                                       id="possession" 
                                       name="possession" 
                                       value="{{ old('possession') }}" 
                                       placeholder="Immediate / December 2025"
                                       required>
                                @error('possession')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="4" 
                                  required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Configurations -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-home"></i>
                        Configurations & Amenities
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label class="form-label">Configurations <span class="text-danger">*</span></label>
                        <div id="configurations-container">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" name="configurations[]" placeholder="e.g., 2 BHK" value="{{ old('configurations.0') }}" required>
                                <button type="button" class="btn btn-outline-success" onclick="addConfiguration()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            @if(old('configurations'))
                                @foreach(array_slice(old('configurations'), 1) as $index => $config)
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" name="configurations[]" value="{{ $config }}" required>
                                    <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                                @endforeach
                            @endif
                        </div>
                        @error('configurations')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Amenities <span class="text-danger">*</span></label>
                        <div id="amenities-container">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" name="amenities[]" placeholder="e.g., Swimming Pool" value="{{ old('amenities.0') }}" required>
                                <button type="button" class="btn btn-outline-success" onclick="addAmenity()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            @if(old('amenities'))
                                @foreach(array_slice(old('amenities'), 1) as $index => $amenity)
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" name="amenities[]" value="{{ $amenity }}" required>
                                    <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                                @endforeach
                            @endif
                        </div>
                        @error('amenities')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Images -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-images"></i>
                        Project Images
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="images" class="form-label">Upload Images <span class="text-danger">*</span></label>
                        <input type="file"
                               class="form-control @error('images') @error('images.*') is-invalid @enderror @enderror"
                               id="images"
                               name="images[]"
                               multiple
                               accept="image/jpeg,image/png,image/jpg,image/gif,image/webp"
                               required>
                        @error('images')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        @error('images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">
                            Select multiple images (JPEG, PNG, JPG, GIF, WebP). Max 2MB each. At least 1 image required.
                        </small>
                    </div>
                    <div id="image-preview" class="row"></div>
                    <div id="upload-status" class="mt-2"></div>
                </div>
            </div>
            
            <!-- Developer Info -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-building"></i>
                        Developer Information
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="developer" class="form-label">Developer Name <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('developer') is-invalid @enderror" 
                               id="developer" 
                               name="developer" 
                               value="{{ old('developer') }}" 
                               required>
                        @error('developer')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="rera_number" class="form-label">RERA Number</label>
                        <input type="text" 
                               class="form-control @error('rera_number') is-invalid @enderror" 
                               id="rera_number" 
                               name="rera_number" 
                               value="{{ old('rera_number') }}">
                        @error('rera_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Settings -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-cogs"></i>
                        Settings
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" {{ old('featured') ? 'checked' : '' }}>
                        <label class="form-check-label" for="featured">
                            <i class="fas fa-star text-warning me-1"></i>
                            Featured Project
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', '1') ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            <i class="fas fa-eye text-success me-1"></i>
                            Active Status
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="content-card">
                <div class="content-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            Create Project
                        </button>
                        <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Projects
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
// Debug information
console.log('Form loaded');
console.log('Max file size from PHP:', '{{ ini_get("upload_max_filesize") }}');
console.log('Max post size from PHP:', '{{ ini_get("post_max_size") }}');
function addConfiguration() {
    const container = document.getElementById('configurations-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="configurations[]" placeholder="e.g., 3 BHK" required>
        <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function addAmenity() {
    const container = document.getElementById('amenities-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="amenities[]" placeholder="e.g., Gym" required>
        <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeField(button) {
    button.parentElement.remove();
}

// Image preview and validation
document.getElementById('images').addEventListener('change', function(e) {
    const preview = document.getElementById('image-preview');
    const status = document.getElementById('upload-status');
    const files = Array.from(e.target.files);

    preview.innerHTML = '';
    status.innerHTML = '';

    if (files.length === 0) {
        status.innerHTML = '<div class="alert alert-warning">Please select at least one image.</div>';
        return;
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    const maxSize = 2 * 1024 * 1024; // 2MB
    let validFiles = 0;
    let errors = [];

    files.forEach((file, index) => {
        if (!allowedTypes.includes(file.type)) {
            errors.push(`File "${file.name}" is not a valid image type.`);
            return;
        }

        if (file.size > maxSize) {
            errors.push(`File "${file.name}" is too large (max 2MB).`);
            return;
        }

        validFiles++;

        const reader = new FileReader();
        reader.onload = function(e) {
            const div = document.createElement('div');
            div.className = 'col-6 mb-2';
            div.innerHTML = `
                <div class="position-relative">
                    <img src="${e.target.result}" class="img-fluid rounded" style="height: 80px; object-fit: cover; width: 100%;">
                    <div class="position-absolute top-0 end-0 p-1">
                        <span class="badge bg-success">${Math.round(file.size / 1024)}KB</span>
                    </div>
                </div>
                <small class="text-muted">${file.name}</small>
            `;
            preview.appendChild(div);
        };
        reader.readAsDataURL(file);
    });

    if (errors.length > 0) {
        status.innerHTML = '<div class="alert alert-danger"><ul class="mb-0">' +
            errors.map(error => `<li>${error}</li>`).join('') + '</ul></div>';
    } else if (validFiles > 0) {
        status.innerHTML = `<div class="alert alert-success">${validFiles} valid image(s) selected.</div>`;
    }
});

// Form submission validation
document.getElementById('projectForm').addEventListener('submit', function(e) {
    const images = document.getElementById('images').files;
    const configurations = document.querySelectorAll('input[name="configurations[]"]');
    const amenities = document.querySelectorAll('input[name="amenities[]"]');

    let errors = [];

    if (images.length === 0) {
        errors.push('At least one image is required.');
    }

    let validConfigs = 0;
    configurations.forEach(input => {
        if (input.value.trim()) validConfigs++;
    });
    if (validConfigs === 0) {
        errors.push('At least one configuration is required.');
    }

    let validAmenities = 0;
    amenities.forEach(input => {
        if (input.value.trim()) validAmenities++;
    });
    if (validAmenities === 0) {
        errors.push('At least one amenity is required.');
    }

    if (errors.length > 0) {
        e.preventDefault();
        alert('Please fix the following errors:\n\n' + errors.join('\n'));
        return false;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Project...';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
@endpush
