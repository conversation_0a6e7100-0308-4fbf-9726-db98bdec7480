@extends('admin.layouts.dashboard')

@section('title', 'Edit Project')
@section('page-title', 'Edit Project')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<form method="POST" action="{{ route('admin.projects.update', $project) }}" enctype="multipart/form-data" id="projectEditForm">
    @csrf
    @method('PUT')
    
    <div class="row">
        <div class="col-lg-8">
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-info-circle"></i>
                        Basic Information
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Project Title <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('title') is-invalid @enderror" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $project->title) }}" 
                                       required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Current slug: <code>{{ $project->slug }}</code></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="project_category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select @error('project_category_id') is-invalid @enderror" 
                                        id="project_category_id" 
                                        name="project_category_id" 
                                        required>
                                    <option value="">Select Category</option>
                                    @foreach($projectCategories as $category)
                                        <option value="{{ $category->id }}" {{ old('project_category_id', $project->project_category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('project_category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('location') is-invalid @enderror" 
                                       id="location" 
                                       name="location" 
                                       value="{{ old('location', $project->location) }}" 
                                       required>
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" 
                                        name="type" 
                                        required>
                                    <option value="">Select Type</option>
                                    <option value="Residential Apartment" {{ old('type', $project->type) == 'Residential Apartment' ? 'selected' : '' }}>Residential Apartment</option>
                                    <option value="Villa" {{ old('type', $project->type) == 'Villa' ? 'selected' : '' }}>Villa</option>
                                    <option value="Commercial" {{ old('type', $project->type) == 'Commercial' ? 'selected' : '' }}>Commercial</option>
                                    <option value="Plot" {{ old('type', $project->type) == 'Plot' ? 'selected' : '' }}>Plot</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" 
                                        name="status" 
                                        required>
                                    <option value="">Select Status</option>
                                    <option value="Ready to Move" {{ old('status', $project->status) == 'Ready to Move' ? 'selected' : '' }}>Ready to Move</option>
                                    <option value="Under Construction" {{ old('status', $project->status) == 'Under Construction' ? 'selected' : '' }}>Under Construction</option>
                                    <option value="Pre-Launch" {{ old('status', $project->status) == 'Pre-Launch' ? 'selected' : '' }}>Pre-Launch</option>
                                    <option value="Booking Open" {{ old('status', $project->status) == 'Booking Open' ? 'selected' : '' }}>Booking Open</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="price_range" class="form-label">Price Range <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('price_range') is-invalid @enderror" 
                                       id="price_range" 
                                       name="price_range" 
                                       value="{{ old('price_range', $project->price_range) }}" 
                                       required>
                                @error('price_range')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="area_range" class="form-label">Area Range <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('area_range') is-invalid @enderror" 
                                       id="area_range" 
                                       name="area_range" 
                                       value="{{ old('area_range', $project->area_range) }}" 
                                       required>
                                @error('area_range')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="possession" class="form-label">Possession <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('possession') is-invalid @enderror" 
                                       id="possession" 
                                       name="possession" 
                                       value="{{ old('possession', $project->possession) }}" 
                                       required>
                                @error('possession')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="4" 
                                  required>{{ old('description', $project->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Configurations -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-home"></i>
                        Configurations & Amenities
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label class="form-label">Configurations <span class="text-danger">*</span></label>
                        <div id="configurations-container">
                            @foreach(old('configurations', $project->configurations) as $index => $config)
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" name="configurations[]" value="{{ $config }}" required>
                                @if($index === 0)
                                <button type="button" class="btn btn-outline-success" onclick="addConfiguration()">
                                    <i class="fas fa-plus"></i>
                                </button>
                                @else
                                <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        @error('configurations')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Amenities <span class="text-danger">*</span></label>
                        <div id="amenities-container">
                            @foreach(old('amenities', $project->amenities) as $index => $amenity)
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" name="amenities[]" value="{{ $amenity }}" required>
                                @if($index === 0)
                                <button type="button" class="btn btn-outline-success" onclick="addAmenity()">
                                    <i class="fas fa-plus"></i>
                                </button>
                                @else
                                <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        @error('amenities')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Current Images -->
            @if($project->images && count($project->images) > 0)
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-images"></i>
                        Current Images
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="row">
                        @foreach($project->images as $image)
                        <div class="col-6 mb-2">
                            <img src="{{ asset('storage/' . $image) }}" class="img-fluid rounded" style="height: 80px; object-fit: cover;">
                        </div>
                        @endforeach
                    </div>
                    <small class="text-muted">Upload new images to replace current ones</small>
                </div>
            </div>
            @endif
            
            <!-- New Images -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-upload"></i>
                        Update Images
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="images" class="form-label">Upload New Images</label>
                        <input type="file"
                               class="form-control @error('images') @error('images.*') is-invalid @enderror @enderror"
                               id="images"
                               name="images[]"
                               multiple
                               accept="image/jpeg,image/png,image/jpg,image/gif,image/webp">
                        @error('images')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        @error('images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">
                            Leave empty to keep current images. Select new images to replace all current images.
                        </small>
                    </div>
                    <div id="image-preview" class="row"></div>
                    <div id="upload-status" class="mt-2"></div>
                </div>
            </div>
            
            <!-- Developer Info -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-building"></i>
                        Developer Information
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="developer" class="form-label">Developer Name <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('developer') is-invalid @enderror" 
                               id="developer" 
                               name="developer" 
                               value="{{ old('developer', $project->developer) }}" 
                               required>
                        @error('developer')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="rera_number" class="form-label">RERA Number</label>
                        <input type="text" 
                               class="form-control @error('rera_number') is-invalid @enderror" 
                               id="rera_number" 
                               name="rera_number" 
                               value="{{ old('rera_number', $project->rera_number) }}">
                        @error('rera_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Settings -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-cogs"></i>
                        Settings
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" {{ old('featured', $project->featured) ? 'checked' : '' }}>
                        <label class="form-check-label" for="featured">
                            <i class="fas fa-star text-warning me-1"></i>
                            Featured Project
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $project->is_active) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            <i class="fas fa-eye text-success me-1"></i>
                            Active Status
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="content-card">
                <div class="content-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update Project
                        </button>
                        <a href="{{ route('admin.projects.show', $project) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>
                            View Project
                        </a>
                        <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Projects
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
function addConfiguration() {
    const container = document.getElementById('configurations-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="configurations[]" placeholder="e.g., 3 BHK" required>
        <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function addAmenity() {
    const container = document.getElementById('amenities-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="amenities[]" placeholder="e.g., Gym" required>
        <button type="button" class="btn btn-outline-danger" onclick="removeField(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeField(button) {
    button.parentElement.remove();
}

// Image preview and validation
document.getElementById('images').addEventListener('change', function(e) {
    const preview = document.getElementById('image-preview');
    const status = document.getElementById('upload-status');
    const files = Array.from(e.target.files);

    preview.innerHTML = '';
    status.innerHTML = '';

    if (files.length === 0) {
        return; // No files selected, keep current images
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    const maxSize = 2 * 1024 * 1024; // 2MB
    let validFiles = 0;
    let errors = [];

    files.forEach((file, index) => {
        if (!allowedTypes.includes(file.type)) {
            errors.push(`File "${file.name}" is not a valid image type.`);
            return;
        }

        if (file.size > maxSize) {
            errors.push(`File "${file.name}" is too large (max 2MB).`);
            return;
        }

        validFiles++;

        const reader = new FileReader();
        reader.onload = function(e) {
            const div = document.createElement('div');
            div.className = 'col-6 mb-2';
            div.innerHTML = `
                <div class="position-relative">
                    <img src="${e.target.result}" class="img-fluid rounded" style="height: 80px; object-fit: cover; width: 100%;">
                    <div class="position-absolute top-0 end-0 p-1">
                        <span class="badge bg-success">${Math.round(file.size / 1024)}KB</span>
                    </div>
                </div>
                <small class="text-muted">${file.name}</small>
            `;
            preview.appendChild(div);
        };
        reader.readAsDataURL(file);
    });

    if (errors.length > 0) {
        status.innerHTML = '<div class="alert alert-danger"><ul class="mb-0">' +
            errors.map(error => `<li>${error}</li>`).join('') + '</ul></div>';
    } else if (validFiles > 0) {
        status.innerHTML = `<div class="alert alert-success">${validFiles} valid image(s) selected. These will replace current images.</div>`;
    }
});

// Form submission validation
document.getElementById('projectEditForm').addEventListener('submit', function(e) {
    const configurations = document.querySelectorAll('input[name="configurations[]"]');
    const amenities = document.querySelectorAll('input[name="amenities[]"]');

    let errors = [];

    let validConfigs = 0;
    configurations.forEach(input => {
        if (input.value.trim()) validConfigs++;
    });
    if (validConfigs === 0) {
        errors.push('At least one configuration is required.');
    }

    let validAmenities = 0;
    amenities.forEach(input => {
        if (input.value.trim()) validAmenities++;
    });
    if (validAmenities === 0) {
        errors.push('At least one amenity is required.');
    }

    if (errors.length > 0) {
        e.preventDefault();
        alert('Please fix the following errors:\n\n' + errors.join('\n'));
        return false;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Project...';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
@endpush
