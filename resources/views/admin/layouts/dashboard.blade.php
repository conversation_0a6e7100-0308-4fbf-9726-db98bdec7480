<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Dashboard') - Admin Panel</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('favicon.png') }}">
    <link rel="shortcut icon" type="image/png" href="{{ asset('favicon.png') }}">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* Modern Dashboard Styles */
        body {
            background: #f8fafc;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            z-index: 1000;
            overflow-y: auto;
            box-shadow: 4px 0 24px rgba(0, 0, 0, 0.1);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .navbar-brand {
            color: #fff !important;
            font-weight: 700;
            font-size: 1.5rem;
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .navbar-brand i {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .sidebar .nav {
            padding: 1rem 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 0.875rem 1.5rem;
            border-radius: 0;
            margin: 0.125rem 1rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            position: relative;
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            opacity: 0.8;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .sidebar .nav-link.active::before {
            content: '';
            position: absolute;
            left: -1rem;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: #3b82f6;
            border-radius: 0 2px 2px 0;
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
        }

        .top-navbar {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .user-dropdown .dropdown-toggle {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            color: #475569;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .user-dropdown .dropdown-toggle:hover {
            background: #e2e8f0;
            border-color: #cbd5e1;
        }

        .user-dropdown .dropdown-toggle::after {
            margin-left: 8px;
        }

        .dropdown-menu {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: 8px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-item:hover {
            background: #f1f5f9;
        }

        .content-area {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border: 1px solid #f1f5f9;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color, #3b82f6);
        }

        .stats-card.primary::before { background: linear-gradient(90deg, #3b82f6, #1d4ed8); }
        .stats-card.success::before { background: linear-gradient(90deg, #10b981, #059669); }
        .stats-card.warning::before { background: linear-gradient(90deg, #f59e0b, #d97706); }
        .stats-card.danger::before { background: linear-gradient(90deg, #ef4444, #dc2626); }

        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .stats-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0;
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stats-icon.primary { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .stats-icon.success { background: linear-gradient(135deg, #10b981, #059669); }
        .stats-icon.warning { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .stats-icon.danger { background: linear-gradient(135deg, #ef4444, #dc2626); }

        .stats-value {
            font-size: 2.25rem;
            font-weight: 800;
            color: #1e293b;
            margin: 0;
            line-height: 1;
        }

        .stats-change {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.5rem;
        }

        .content-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border: 1px solid #f1f5f9;
            overflow: hidden;
        }

        .content-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            background: #fafbfc;
        }

        .content-card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .content-card-body {
            padding: 1.5rem;
        }

        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 12px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(180deg, #3b82f6, #e2e8f0);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
            padding-left: 1rem;
        }

        .timeline-marker {
            position: absolute;
            left: -2rem;
            top: 0.25rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #3b82f6;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #3b82f6;
        }

        .timeline-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .timeline-text {
            color: #64748b;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .timeline-time {
            font-size: 0.875rem;
            color: #94a3b8;
        }

        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .quick-action-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0.875rem 1rem;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            color: #475569;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            color: #334155;
            transform: translateY(-1px);
        }

        .quick-action-btn i {
            width: 20px;
            text-align: center;
        }

        /* Badge Styles */
        .badge {
            display: inline-block;
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            transition: all 0.3s ease;
        }

        .badge-lg {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 0.75rem;
        }

        .badge-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.625rem;
            border-radius: 0.375rem;
        }

        /* Badge Color Variants */
        .badge-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .badge-secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
        }

        .badge-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .badge-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        .badge-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .badge-info {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
        }

        .badge-light {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: #475569;
            box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
        }

        .badge-dark {
            background: linear-gradient(135deg, #1e293b, #0f172a);
            color: white;
            box-shadow: 0 2px 8px rgba(30, 41, 59, 0.3);
        }

        /* Status Badge Classes */
        .badge-new {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .badge-contacted {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .badge-interested {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }

        .badge-not-interested {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
        }

        .badge-converted {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        /* Badge Counter for Navigation */
        .badge-counter {
            position: absolute;
            top: 0.5rem;
            right: 0.75rem;
            min-width: 1.25rem;
            height: 1.25rem;
            padding: 0.125rem 0.375rem;
            font-size: 0.625rem;
            font-weight: 700;
            line-height: 1;
            border-radius: 0.625rem;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* Badge Hover Effects */
        .badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Additional Stats Card Colors */
        .stats-card.info .stats-icon {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .stats-card.purple .stats-icon {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .stats-card.dark .stats-icon {
            background: linear-gradient(135deg, #1e293b, #0f172a);
        }

        .stats-card.secondary .stats-icon {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        /* Text Color Utilities */
        .text-purple {
            color: #8b5cf6 !important;
        }

        /* Enhanced Alert Styles */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
            border-left: 4px solid #f59e0b;
        }

        .alert-info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border-left: 4px solid #3b82f6;
        }

        /* Enhanced Quick Action Buttons */
        .quick-action-btn {
            position: relative;
            overflow: hidden;
        }

        .quick-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .quick-action-btn:hover::before {
            left: 100%;
        }

        /* Table Enhancements */
        .table-hover tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .table th {
            border-top: none;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            background: #f9fafb;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 260px;
            }
            .main-content {
                margin-left: 260px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
            .content-area {
                padding: 1rem;
            }
            .stats-value {
                font-size: 1.875rem;
            }
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <a href="{{ route('admin.dashboard') }}" class="navbar-brand">
            <i class="fas fa-building"></i>
            Hestia Abodes
        </a>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                   href="{{ route('admin.dashboard') }}">
                    <i class="fas fa-chart-pie"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li class="nav-item d-none">
                <a class="nav-link {{ request()->routeIs('admin.profile*') ? 'active' : '' }}"
                   href="{{ route('admin.profile') }}">
                    <i class="fas fa-user-circle"></i>
                    <span>Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.project-categories*') ? 'active' : '' }}"
                   href="{{ route('admin.project-categories.index') }}">
                    <i class="fas fa-folder-open"></i>
                    <span>Project Categories</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.projects*') ? 'active' : '' }}"
                   href="{{ route('admin.projects.index') }}">
                    <i class="fas fa-building"></i>
                    <span>Projects</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.project-enquiries*') ? 'active' : '' }}"
                   href="{{ route('admin.project-enquiries.index') }}">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Project Enquiries</span>
                    @php
                        $newEnquiries = \App\Models\ProjectEnquiry::new()->count();
                    @endphp
                    @if($newEnquiries > 0)
                        <span class="badge badge-warning badge-counter">{{ $newEnquiries }}</span>
                    @endif
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.career-applications*') ? 'active' : '' }}"
                   href="{{ route('admin.career-applications.index') }}">
                    <i class="fas fa-briefcase"></i>
                    <span>Career Applications</span>
                    @php
                        $newApplications = \App\Models\CareerApplication::new()->count();
                    @endphp
                    @if($newApplications > 0)
                        <span class="badge badge-info badge-counter">{{ $newApplications }}</span>
                    @endif
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.galleries*') ? 'active' : '' }}"
                   href="{{ route('admin.galleries.index') }}">
                    <i class="fas fa-images"></i>
                    <span>Gallery</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.blog-categories*') ? 'active' : '' }}"
                   href="{{ route('admin.blog-categories.index') }}">
                    <i class="fas fa-folder-open"></i>
                    <span>Blog Categories</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.blogs*') ? 'active' : '' }}"
                   href="{{ route('admin.blogs.index') }}">
                    <i class="fas fa-blog"></i>
                    <span>Blog Posts</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.hero-sliders*') ? 'active' : '' }}"
                   href="{{ route('admin.hero-sliders.index') }}">
                    <i class="fas fa-images"></i>
                    <span>Hero Sliders</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.page-headers*') ? 'active' : '' }}"
                   href="{{ route('admin.page-headers.index') }}">
                    <i class="fas fa-heading"></i>
                    <span>Page Headers</span>
                </a>
            </li>

            <!-- Contact Submissions -->
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.contact-submissions*') ? 'active' : '' }}"
                   href="{{ route('admin.contact-submissions.index') }}">
                    <i class="fas fa-envelope"></i>
                    <span>Contact Submissions</span>
                    @php
                        $newSubmissions = \App\Models\ContactSubmission::new()->count();
                    @endphp
                    @if($newSubmissions > 0)
                        <span class="badge badge-danger badge-counter">{{ $newSubmissions }}</span>
                    @endif
                </a>
            </li>

            <!-- Newsletter Subscriptions -->
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.newsletter-subscriptions*') ? 'active' : '' }}"
                   href="{{ route('admin.newsletter-subscriptions.index') }}">
                    <i class="fas fa-newspaper"></i>
                    <span>Newsletter Subscriptions</span>
                    @php
                        $newSubscriptions = \App\Models\NewsletterSubscription::active()->thisWeek()->count();
                    @endphp
                    @if($newSubscriptions > 0)
                        <span class="badge badge-success badge-counter">{{ $newSubscriptions }}</span>
                    @endif
                </a>
            </li>
            <li class="nav-item d-none">
                <a class="nav-link {{ request()->routeIs('admin.password*') ? 'active' : '' }}"
                   href="{{ route('admin.password.reset') }}">
                    <i class="fas fa-shield-alt"></i>
                    <span>Security</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-navbar">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">@yield('page-title', 'Dashboard')</h1>

                <div class="user-dropdown dropdown">
                    <button class="dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i>
                        <span>{{ auth('admin')->user()->name }}</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="{{ route('admin.profile') }}">
                                <i class="fas fa-user"></i>
                                <span>Profile Settings</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{{ route('admin.password.reset') }}">
                                <i class="fas fa-shield-alt"></i>
                                <span>Security</span>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('admin.logout') }}" class="d-inline w-100">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>Sign Out</span>
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="content-area">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert" style="border-radius: 12px; border: none; background: #f0fdf4; color: #166534; border-left: 4px solid #22c55e;">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert" style="border-radius: 12px; border: none; background: #fef2f2; color: #dc2626; border-left: 4px solid #ef4444;">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @yield('content')
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @stack('scripts')
</body>
</html>
