@extends('admin.layouts.dashboard')

@section('title', 'Newsletter Subscription Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Newsletter Subscription Details</h1>
            <p class="mb-0 text-muted">View detailed information about this subscription</p>
        </div>
        <div>
            <a href="{{ route('admin.newsletter-subscriptions.edit', $newsletterSubscription) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.newsletter-subscriptions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Subscription Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Email Address:</label>
                                <p class="form-control-plaintext">{{ $newsletterSubscription->email }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Status:</label>
                                <p class="form-control-plaintext">{!! $newsletterSubscription->status_badge !!}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Source:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge badge-info">{{ $newsletterSubscription->source_display }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">IP Address:</label>
                                <p class="form-control-plaintext">{{ $newsletterSubscription->ip_address ?: 'Not recorded' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Subscribed At:</label>
                                <p class="form-control-plaintext">
                                    @if($newsletterSubscription->subscribed_at)
                                        {{ $newsletterSubscription->subscribed_at->format('F j, Y \a\t g:i A') }}<br>
                                        <small class="text-muted">{{ $newsletterSubscription->subscribed_at->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">Not recorded</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Created At:</label>
                                <p class="form-control-plaintext">
                                    {{ $newsletterSubscription->created_at->format('F j, Y \a\t g:i A') }}<br>
                                    <small class="text-muted">{{ $newsletterSubscription->created_at->diffForHumans() }}</small>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($newsletterSubscription->unsubscribed_at)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Unsubscribed At:</label>
                                    <p class="form-control-plaintext">
                                        {{ $newsletterSubscription->unsubscribed_at->format('F j, Y \a\t g:i A') }}<br>
                                        <small class="text-muted">{{ $newsletterSubscription->unsubscribed_at->diffForHumans() }}</small>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Unsubscribe Reason:</label>
                                    <p class="form-control-plaintext">
                                        {{ $newsletterSubscription->unsubscribe_reason ?: 'No reason provided' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($newsletterSubscription->user_agent)
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="font-weight-bold">User Agent:</label>
                                    <p class="form-control-plaintext">
                                        <small class="text-muted">{{ $newsletterSubscription->user_agent }}</small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    @if($newsletterSubscription->isActive())
                        <form method="POST" action="{{ route('admin.newsletter-subscriptions.update', $newsletterSubscription) }}" class="mb-3">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="email" value="{{ $newsletterSubscription->email }}">
                            <input type="hidden" name="status" value="unsubscribed">
                            <input type="hidden" name="unsubscribe_reason" value="Unsubscribed by admin">
                            <button type="submit" class="btn btn-warning btn-block" onclick="return confirm('Are you sure you want to unsubscribe this user?')">
                                <i class="fas fa-user-times"></i> Unsubscribe
                            </button>
                        </form>
                    @else
                        <form method="POST" action="{{ route('admin.newsletter-subscriptions.update', $newsletterSubscription) }}" class="mb-3">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="email" value="{{ $newsletterSubscription->email }}">
                            <input type="hidden" name="status" value="active">
                            <button type="submit" class="btn btn-success btn-block" onclick="return confirm('Are you sure you want to reactivate this subscription?')">
                                <i class="fas fa-user-check"></i> Reactivate
                            </button>
                        </form>
                    @endif

                    <a href="{{ route('admin.newsletter-subscriptions.edit', $newsletterSubscription) }}" class="btn btn-primary btn-block mb-3">
                        <i class="fas fa-edit"></i> Edit Details
                    </a>

                    <form method="POST" action="{{ route('admin.newsletter-subscriptions.destroy', $newsletterSubscription) }}" onsubmit="return confirm('Are you sure you want to permanently delete this subscription? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-block">
                            <i class="fas fa-trash"></i> Delete Subscription
                        </button>
                    </form>
                </div>
            </div>

            <!-- Subscription Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Subscription Created</h6>
                                <p class="timeline-text">
                                    {{ $newsletterSubscription->created_at->format('M j, Y g:i A') }}<br>
                                    <small class="text-muted">{{ $newsletterSubscription->created_at->diffForHumans() }}</small>
                                </p>
                            </div>
                        </div>

                        @if($newsletterSubscription->subscribed_at && $newsletterSubscription->subscribed_at != $newsletterSubscription->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Subscription Activated</h6>
                                    <p class="timeline-text">
                                        {{ $newsletterSubscription->subscribed_at->format('M j, Y g:i A') }}<br>
                                        <small class="text-muted">{{ $newsletterSubscription->subscribed_at->diffForHumans() }}</small>
                                    </p>
                                </div>
                            </div>
                        @endif

                        @if($newsletterSubscription->unsubscribed_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-secondary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Unsubscribed</h6>
                                    <p class="timeline-text">
                                        {{ $newsletterSubscription->unsubscribed_at->format('M j, Y g:i A') }}<br>
                                        <small class="text-muted">{{ $newsletterSubscription->unsubscribed_at->diffForHumans() }}</small>
                                        @if($newsletterSubscription->unsubscribe_reason)
                                            <br><small class="text-muted">Reason: {{ $newsletterSubscription->unsubscribe_reason }}</small>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #4e73df;
}

.timeline-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin: 0;
    font-size: 13px;
    color: #5a5c69;
}
</style>
@endsection
