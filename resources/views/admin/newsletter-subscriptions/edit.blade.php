@extends('admin.layouts.dashboard')

@section('title', 'Edit Newsletter Subscription')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Newsletter Subscription</h1>
            <p class="mb-0 text-muted">Update subscription details and status</p>
        </div>
        <div>
            <a href="{{ route('admin.newsletter-subscriptions.show', $newsletterSubscription) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Details
            </a>
            <a href="{{ route('admin.newsletter-subscriptions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.newsletter-subscriptions.update', $newsletterSubscription) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="font-weight-bold">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email', $newsletterSubscription->email) }}" 
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="font-weight-bold">Status <span class="text-danger">*</span></label>
                                    <select class="form-control @error('status') is-invalid @enderror" 
                                            id="status" 
                                            name="status" 
                                            required>
                                        @foreach(\App\Models\NewsletterSubscription::getStatusOptions() as $value => $label)
                                            <option value="{{ $value }}" 
                                                    {{ old('status', $newsletterSubscription->status) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row" id="unsubscribeReasonRow" style="{{ old('status', $newsletterSubscription->status) == 'unsubscribed' ? '' : 'display: none;' }}">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="unsubscribe_reason" class="font-weight-bold">Unsubscribe Reason</label>
                                    <textarea class="form-control @error('unsubscribe_reason') is-invalid @enderror" 
                                              id="unsubscribe_reason" 
                                              name="unsubscribe_reason" 
                                              rows="3" 
                                              placeholder="Optional reason for unsubscribing...">{{ old('unsubscribe_reason', $newsletterSubscription->unsubscribe_reason) }}</textarea>
                                    @error('unsubscribe_reason')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">This field is only relevant when status is set to "Unsubscribed"</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Subscription
                            </button>
                            <a href="{{ route('admin.newsletter-subscriptions.show', $newsletterSubscription) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Email:</strong><br>
                        <span class="text-muted">{{ $newsletterSubscription->email }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Current Status:</strong><br>
                        {!! $newsletterSubscription->status_badge !!}
                    </div>
                    
                    <div class="mb-3">
                        <strong>Source:</strong><br>
                        <span class="badge badge-info">{{ $newsletterSubscription->source_display }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Subscribed:</strong><br>
                        <span class="text-muted">
                            @if($newsletterSubscription->subscribed_at)
                                {{ $newsletterSubscription->subscribed_at->format('M j, Y') }}
                            @else
                                Not recorded
                            @endif
                        </span>
                    </div>
                    
                    @if($newsletterSubscription->unsubscribed_at)
                        <div class="mb-3">
                            <strong>Unsubscribed:</strong><br>
                            <span class="text-muted">{{ $newsletterSubscription->unsubscribed_at->format('M j, Y') }}</span>
                        </div>
                    @endif
                    
                    @if($newsletterSubscription->unsubscribe_reason)
                        <div class="mb-3">
                            <strong>Current Reason:</strong><br>
                            <span class="text-muted">{{ $newsletterSubscription->unsubscribe_reason }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Help Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Help</h6>
                </div>
                <div class="card-body">
                    <h6 class="font-weight-bold">Status Options:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-success">Active</span> - User will receive newsletters</li>
                        <li><span class="badge bg-secondary">Unsubscribed</span> - User will not receive newsletters</li>
                    </ul>
                    
                    <hr>
                    
                    <h6 class="font-weight-bold">Notes:</h6>
                    <ul class="small text-muted">
                        <li>Changing status will automatically update timestamps</li>
                        <li>Unsubscribe reason is optional but recommended</li>
                        <li>Email changes will be validated for uniqueness</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const unsubscribeReasonRow = document.getElementById('unsubscribeReasonRow');
    
    function toggleUnsubscribeReason() {
        if (statusSelect.value === 'unsubscribed') {
            unsubscribeReasonRow.style.display = '';
        } else {
            unsubscribeReasonRow.style.display = 'none';
        }
    }
    
    statusSelect.addEventListener('change', toggleUnsubscribeReason);
    
    // Initial check
    toggleUnsubscribeReason();
});
</script>
@endsection
