@extends('admin.layouts.dashboard')

@section('title', 'Add Newsletter Subscriber')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Add Newsletter Subscriber</h1>
            <p class="mb-0 text-muted">Manually add a new newsletter subscription</p>
        </div>
        <div>
            <a href="{{ route('admin.newsletter-subscriptions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.newsletter-subscriptions.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="font-weight-bold">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email') }}" 
                                           placeholder="<EMAIL>"
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Must be a valid and unique email address</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="font-weight-bold">Status <span class="text-danger">*</span></label>
                                    <select class="form-control @error('status') is-invalid @enderror" 
                                            id="status" 
                                            name="status" 
                                            required>
                                        @foreach(\App\Models\NewsletterSubscription::getStatusOptions() as $value => $label)
                                            <option value="{{ $value }}" 
                                                    {{ old('status', 'active') == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="source" class="font-weight-bold">Source <span class="text-danger">*</span></label>
                                    <select class="form-control @error('source') is-invalid @enderror" 
                                            id="source" 
                                            name="source" 
                                            required>
                                        @foreach(\App\Models\NewsletterSubscription::getSourceOptions() as $value => $label)
                                            <option value="{{ $value }}" 
                                                    {{ old('source', 'admin') == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('source')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">How this subscription was created</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Subscriber
                            </button>
                            <a href="{{ route('admin.newsletter-subscriptions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Help Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Help</h6>
                </div>
                <div class="card-body">
                    <h6 class="font-weight-bold">Status Options:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-success">Active</span> - User will receive newsletters</li>
                        <li><span class="badge bg-secondary">Unsubscribed</span> - User will not receive newsletters</li>
                    </ul>
                    
                    <hr>
                    
                    <h6 class="font-weight-bold">Source Options:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-info">Website</span> - Subscribed via website form</li>
                        <li><span class="badge bg-info">Admin Panel</span> - Added manually by admin</li>
                        <li><span class="badge bg-info">API</span> - Added via API integration</li>
                    </ul>
                    
                    <hr>
                    
                    <h6 class="font-weight-bold">Notes:</h6>
                    <ul class="small text-muted">
                        <li>Email addresses must be unique</li>
                        <li>Active subscriptions will automatically set subscription timestamp</li>
                        <li>You can change status and details after creation</li>
                    </ul>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ \App\Models\NewsletterSubscription::active()->count() }}</h4>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-secondary">{{ \App\Models\NewsletterSubscription::unsubscribed()->count() }}</h4>
                            <small class="text-muted">Unsubscribed</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h5 class="text-info">{{ \App\Models\NewsletterSubscription::count() }}</h5>
                        <small class="text-muted">Total Subscribers</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
