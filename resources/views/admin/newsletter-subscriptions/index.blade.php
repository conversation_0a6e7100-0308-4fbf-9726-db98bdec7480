@extends('admin.layouts.dashboard')

@section('title', 'Newsletter Subscriptions')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Newsletter Subscriptions</h1>
            <p class="mb-0 text-muted">Manage email newsletter subscribers and send updates</p>
        </div>
        <div>
            <a href="{{ route('admin.newsletter-subscriptions.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Subscriber
            </a>
            <a href="{{ route('admin.newsletter-subscriptions.export', request()->query()) }}" class="btn btn-success">
                <i class="fas fa-download"></i> Export CSV
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Subscribers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Unsubscribed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['unsubscribed'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">This Month</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['this_month'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.newsletter-subscriptions.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                @foreach(\App\Models\NewsletterSubscription::getStatusOptions() as $value => $label)
                                    <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="source">Source</label>
                            <select name="source" id="source" class="form-control">
                                <option value="">All Sources</option>
                                @foreach(\App\Models\NewsletterSubscription::getSourceOptions() as $value => $label)
                                    <option value="{{ $value }}" {{ request('source') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="search">Search Email</label>
                            <input type="text" name="search" id="search" class="form-control" placeholder="Email address..." value="{{ request('search') }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('admin.newsletter-subscriptions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Subscriptions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Newsletter Subscriptions</h6>
            <div>
                <button type="button" class="btn btn-sm btn-info" onclick="toggleBulkActions()">
                    <i class="fas fa-tasks"></i> Bulk Actions
                </button>
            </div>
        </div>
        <div class="card-body">
            @if($subscriptions->count() > 0)
                <!-- Bulk Actions Form (Hidden by default) -->
                <div id="bulkActionsForm" style="display: none;" class="mb-3 p-3 bg-light rounded">
                    <form method="POST" action="{{ route('admin.newsletter-subscriptions.bulk-update') }}">
                        @csrf
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <label for="bulk_status">Update Status To:</label>
                                <select name="bulk_status" id="bulk_status" class="form-control" required>
                                    @foreach(\App\Models\NewsletterSubscription::getStatusOptions() as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Update Selected
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">
                                    Cancel
                                </button>
                            </div>
                        </div>
                        <div id="selectedSubscriptions"></div>
                    </form>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>Email Address</th>
                                <th>Status</th>
                                <th>Source</th>
                                <th>Subscribed</th>
                                <th>Unsubscribed</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($subscriptions as $subscription)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="subscription-checkbox" value="{{ $subscription->id }}">
                                    </td>
                                    <td>
                                        <strong>{{ $subscription->email }}</strong>
                                        @if($subscription->status === 'active' && $subscription->created_at->isToday())
                                            <span class="badge badge-primary badge-sm ml-1">NEW</span>
                                        @endif
                                    </td>
                                    <td>{!! $subscription->status_badge !!}</td>
                                    <td>
                                        <span class="badge badge-info">{{ $subscription->source_display }}</span>
                                    </td>
                                    <td>
                                        <div class="small">
                                            {{ $subscription->subscribed_at ? $subscription->subscribed_at->format('M d, Y') : '-' }}<br>
                                            <span class="text-muted">{{ $subscription->time_ago }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if($subscription->unsubscribed_at)
                                            <div class="small">
                                                {{ $subscription->unsubscribed_at->format('M d, Y') }}<br>
                                                @if($subscription->unsubscribe_reason)
                                                    <span class="text-muted">{{ Str::limit($subscription->unsubscribe_reason, 30) }}</span>
                                                @endif
                                            </div>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.newsletter-subscriptions.show', $subscription) }}" 
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.newsletter-subscriptions.edit', $subscription) }}" 
                                               class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="{{ route('admin.newsletter-subscriptions.destroy', $subscription) }}" 
                                                  style="display: inline;" 
                                                  onsubmit="return confirm('Are you sure you want to delete this subscription?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $subscriptions->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-envelope fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No newsletter subscriptions found</h5>
                    <p class="text-muted">Newsletter subscriptions will appear here when users subscribe.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function toggleBulkActions() {
    const form = document.getElementById('bulkActionsForm');
    const isVisible = form.style.display !== 'none';
    form.style.display = isVisible ? 'none' : 'block';
    
    if (!isVisible) {
        updateSelectedSubscriptions();
    }
}

function updateSelectedSubscriptions() {
    const checkboxes = document.querySelectorAll('.subscription-checkbox:checked');
    const container = document.getElementById('selectedSubscriptions');
    
    container.innerHTML = '';
    checkboxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'subscriptions[]';
        input.value = checkbox.value;
        container.appendChild(input);
    });
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.subscription-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedSubscriptions();
});

// Update selected subscriptions when individual checkboxes change
document.querySelectorAll('.subscription-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedSubscriptions);
});
</script>
@endsection
