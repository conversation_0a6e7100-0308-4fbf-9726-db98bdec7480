@extends('admin.layouts.dashboard')

@section('title', 'View Project Category')
@section('page-title', 'Project Category Details')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tag me-2"></i>
                    {{ $projectCategory->name }}
                </h5>
                <span class="badge bg-{{ $projectCategory->status ? 'success' : 'danger' }} fs-6">
                    {{ $projectCategory->status ? 'Active' : 'Inactive' }}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Category Name</h6>
                        <p class="fs-5">{{ $projectCategory->name }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Slug</h6>
                        <p><code class="fs-6">{{ $projectCategory->slug }}</code></p>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted">Description</h6>
                    @if($projectCategory->description)
                        <p>{{ $projectCategory->description }}</p>
                    @else
                        <p class="text-muted fst-italic">No description provided.</p>
                    @endif
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Created Date</h6>
                        <p>{{ $projectCategory->created_at->format('F d, Y') }}</p>
                        <small class="text-muted">{{ $projectCategory->created_at->format('g:i A') }}</small>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Last Updated</h6>
                        <p>{{ $projectCategory->updated_at->format('F d, Y') }}</p>
                        <small class="text-muted">{{ $projectCategory->updated_at->format('g:i A') }}</small>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ route('admin.project-categories.edit', $projectCategory) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    Edit Category
                </a>
                <a href="{{ route('admin.project-categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to List
                </a>
                <form method="POST" 
                      action="{{ route('admin.project-categories.destroy', $projectCategory) }}" 
                      class="d-inline"
                      onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="mb-1">0</h4>
                            <small class="text-muted">Projects</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="mb-1">0</h4>
                        <small class="text-muted">Gallery Items</small>
                    </div>
                </div>
                <hr>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Statistics will be updated when projects and gallery items are associated with this category.
                </small>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.project-categories.edit', $projectCategory) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit Category
                    </a>
                    <a href="{{ route('admin.project-categories.create') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus me-2"></i>
                        Create New Category
                    </a>
                    @if($projectCategory->status)
                        <button class="btn btn-outline-warning btn-sm" disabled>
                            <i class="fas fa-eye-slash me-2"></i>
                            Deactivate
                        </button>
                    @else
                        <button class="btn btn-outline-success btn-sm" disabled>
                            <i class="fas fa-eye me-2"></i>
                            Activate
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
