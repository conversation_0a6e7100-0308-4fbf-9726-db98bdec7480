@extends('admin.layouts.dashboard')

@section('title', 'Edit Project Category')
@section('page-title', 'Edit Project Category')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit Category: {{ $projectCategory->name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.project-categories.update', $projectCategory) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('name') is-invalid @enderror" 
                               id="name" 
                               name="name" 
                               value="{{ old('name', $projectCategory->name) }}" 
                               required 
                               placeholder="Enter category name">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Current slug: <code>{{ $projectCategory->slug }}</code></small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="4" 
                                  placeholder="Enter category description">{{ old('description', $projectCategory->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="status" 
                                   name="status" 
                                   value="1" 
                                   {{ old('status', $projectCategory->status) ? 'checked' : '' }}>
                            <label class="form-check-label" for="status">
                                Active Status
                            </label>
                        </div>
                        <small class="form-text text-muted">Check to make this category active and visible.</small>
                    </div>
                    
                    <div class="mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update Category
                        </button>
                        <a href="{{ route('admin.project-categories.show', $projectCategory) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>
                            View Category
                        </a>
                        <a href="{{ route('admin.project-categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Category Info
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Current Status:</strong><br>
                    <span class="badge bg-{{ $projectCategory->status ? 'success' : 'danger' }}">
                        {{ $projectCategory->status ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $projectCategory->created_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $projectCategory->updated_at->format('M d, Y H:i') }}</small>
                </div>
                
                <hr>
                
                <div class="d-grid">
                    <form method="POST" 
                          action="{{ route('admin.project-categories.destroy', $projectCategory) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
