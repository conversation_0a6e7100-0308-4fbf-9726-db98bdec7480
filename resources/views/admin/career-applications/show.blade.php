@extends('admin.layouts.dashboard')

@section('title', 'Career Application Details')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Career Application Details</h1>
    <div>
        <a href="{{ route('admin.career-applications.edit', $careerApplication) }}" class="btn btn-warning">
            <i class="fas fa-edit"></i> Edit Status
        </a>
        <a href="{{ route('admin.career-applications.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <!-- Main Application Details -->
    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-user"></i> Personal Information
                </h5>
                <div class="ms-auto">
                    {!! $careerApplication->status_badge !!}
                </div>
            </div>
            <div class="content-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item mb-3">
                            <label class="info-label">Full Name</label>
                            <div class="info-value">{{ $careerApplication->name }}</div>
                        </div>
                        <div class="info-item mb-3">
                            <label class="info-label">Email Address</label>
                            <div class="info-value">
                                <a href="mailto:{{ $careerApplication->email }}">{{ $careerApplication->email }}</a>
                            </div>
                        </div>
                        <div class="info-item mb-3">
                            <label class="info-label">Phone Number</label>
                            <div class="info-value">
                                <a href="tel:{{ $careerApplication->phone }}">{{ $careerApplication->phone }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item mb-3">
                            <label class="info-label">Position Applied</label>
                            <div class="info-value">
                                <span class="badge bg-primary">{{ $careerApplication->position_applied }}</span>
                            </div>
                        </div>

                        <div class="info-item mb-3">
                            <label class="info-label">Application Date</label>
                            <div class="info-value">{{ $careerApplication->created_at->format('F j, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location & Employment Details -->
        @if($careerApplication->current_location || $careerApplication->preferred_location || $careerApplication->current_salary || $careerApplication->expected_salary || $careerApplication->notice_period)
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-briefcase"></i> Employment Details
                </h5>
            </div>
            <div class="content-card-body">
                <div class="row">
                    <div class="col-md-6">
                        @if($careerApplication->current_location)
                        <div class="info-item mb-3">
                            <label class="info-label">Current Location</label>
                            <div class="info-value">{{ $careerApplication->current_location }}</div>
                        </div>
                        @endif
                        @if($careerApplication->preferred_location)
                        <div class="info-item mb-3">
                            <label class="info-label">Preferred Location</label>
                            <div class="info-value">{{ $careerApplication->preferred_location }}</div>
                        </div>
                        @endif
                        @if($careerApplication->notice_period)
                        <div class="info-item mb-3">
                            <label class="info-label">Notice Period</label>
                            <div class="info-value">{{ ucfirst(str_replace('-', ' ', $careerApplication->notice_period)) }}</div>
                        </div>
                        @endif
                    </div>
                    <div class="col-md-6">
                        @if($careerApplication->current_salary)
                        <div class="info-item mb-3">
                            <label class="info-label">Current Salary</label>
                            <div class="info-value">{{ $careerApplication->current_salary_display }}</div>
                        </div>
                        @endif
                        @if($careerApplication->expected_salary)
                        <div class="info-item mb-3">
                            <label class="info-label">Expected Salary</label>
                            <div class="info-value">{{ $careerApplication->expected_salary_display }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Message -->
        @if($careerApplication->cover_letter)
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-comment"></i> Message
                </h5>
            </div>
            <div class="content-card-body">
                <div class="cover-letter-content">
                    {{ $careerApplication->cover_letter }}
                </div>
            </div>
        </div>
        @endif

        <!-- Additional Information -->
        @if($careerApplication->additional_info)
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-info-circle"></i> Additional Information
                </h5>
            </div>
            <div class="content-card-body">
                <div class="additional-info-content">
                    {{ $careerApplication->additional_info }}
                </div>
            </div>
        </div>
        @endif

        <!-- Admin Notes -->
        @if($careerApplication->admin_notes)
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-sticky-note"></i> Admin Notes
                </h5>
            </div>
            <div class="content-card-body">
                <div class="admin-notes-content">
                    {{ $careerApplication->admin_notes }}
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="d-grid gap-2">
                    @if($careerApplication->resume_path)
                    <a href="{{ route('admin.career-applications.download-resume', $careerApplication) }}" 
                       class="btn btn-success">
                        <i class="fas fa-download"></i> Download Resume
                    </a>
                    @endif
                    <a href="mailto:{{ $careerApplication->email }}" class="btn btn-primary d-none">
                        <i class="fas fa-envelope"></i> Send Email
                    </a>
                    <a href="tel:{{ $careerApplication->phone }}" class="btn btn-info">
                        <i class="fas fa-phone"></i> Call Applicant
                    </a>
                    <a href="{{ route('admin.career-applications.edit', $careerApplication) }}" 
                       class="btn btn-warning">
                        <i class="fas fa-edit"></i> Update Status
                    </a>
                </div>
            </div>
        </div>

        <!-- Application Timeline -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-clock"></i> Timeline
                </h5>
            </div>
            <div class="content-card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>Application Submitted</h6>
                            <small class="text-muted">{{ $careerApplication->created_at->format('M j, Y \a\t g:i A') }}</small>
                        </div>
                    </div>
                    @if($careerApplication->reviewed_at)
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6>Application Reviewed</h6>
                            <small class="text-muted">{{ $careerApplication->reviewed_at->format('M j, Y \a\t g:i A') }}</small>
                        </div>
                    </div>
                    @endif
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{{ $careerApplication->status === 'new' ? 'primary' : ($careerApplication->status === 'selected' ? 'success' : ($careerApplication->status === 'rejected' ? 'danger' : 'warning')) }}"></div>
                        <div class="timeline-content">
                            <h6>Current Status: {{ ucfirst($careerApplication->status) }}</h6>
                            <small class="text-muted">{{ $careerApplication->updated_at->format('M j, Y \a\t g:i A') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-cog"></i> Technical Details
                </h5>
            </div>
            <div class="content-card-body">
                <div class="info-item mb-2">
                    <label class="info-label">Application ID</label>
                    <div class="info-value">#{{ $careerApplication->id }}</div>
                </div>
                <div class="info-item mb-2">
                    <label class="info-label">IP Address</label>
                    <div class="info-value">{{ $careerApplication->ip_address }}</div>
                </div>
                <div class="info-item mb-2">
                    <label class="info-label">Last Updated</label>
                    <div class="info-value">{{ $careerApplication->updated_at->format('M j, Y \a\t g:i A') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .info-item {
        border-bottom: 1px solid #eee;
        padding-bottom: 0.5rem;
    }
    
    .info-label {
        font-weight: 600;
        color: #666;
        font-size: 0.875rem;
        display: block;
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        color: #333;
    }
    
    .cover-letter-content,
    .additional-info-content,
    .admin-notes-content {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        border-left: 4px solid #007bff;
        white-space: pre-wrap;
        line-height: 1.6;
    }
    
    .timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
    }
    
    .timeline-marker {
        position: absolute;
        left: -2rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content h6 {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }
</style>
@endpush
