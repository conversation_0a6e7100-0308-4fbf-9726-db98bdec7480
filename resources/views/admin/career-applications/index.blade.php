@extends('admin.layouts.dashboard')

@section('title', 'Career Applications')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Career Applications</h1>
</div>

<!-- Filters -->
<div class="content-card mb-4">
    <div class="content-card-body">
        <form method="GET" action="{{ route('admin.career-applications.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Name or email...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    @foreach($statusOptions as $key => $label)
                        <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label for="position" class="form-label">Position</label>
                <select class="form-select" id="position" name="position">
                    <option value="">All Positions</option>
                    @foreach($positionOptions as $key => $label)
                        <option value="{{ $key }}" {{ request('position') == $key ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Filter
                </button>
                <a href="{{ route('admin.career-applications.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Actions -->
@if($applications->count() > 0)
<div class="content-card mb-4">
    <div class="content-card-body">
        <form id="bulkActionForm" method="POST" action="{{ route('admin.career-applications.bulk-update') }}">
            @csrf
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="bulk_status" class="form-label">Bulk Status Update</label>
                    <select class="form-select" id="bulk_status" name="status" required>
                        <option value="">Select Status</option>
                        @foreach($statusOptions as $key => $label)
                            <option value="{{ $key }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-warning" id="bulkUpdateBtn" disabled>
                        <i class="fas fa-edit"></i> Update Selected
                    </button>
                    <span class="text-muted ms-2">
                        <span id="selectedCount">0</span> selected
                    </span>
                </div>
            </div>
        </form>
    </div>
</div>
@endif

<!-- Applications Table -->
<div class="content-card">
    <div class="content-card-body">
        @if($applications->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Applicant</th>
                            <th>Position</th>
                            <th>Status</th>
                            <th>Applied Date</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($applications as $application)
                        <tr>
                            <td>
                                <input type="checkbox" name="selected_applications[]" 
                                       value="{{ $application->id }}" class="form-check-input application-checkbox">
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $application->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $application->email }}</small>
                                    <br>
                                    <small class="text-muted">{{ $application->phone }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $positionOptions[$application->position_applied] ?? $application->position_applied }}</span>
                            </td>
                            <td>{!! $application->status_badge !!}</td>
                            <td>
                                <small>{{ $application->created_at->format('M j, Y') }}</small>
                                <br>
                                <small class="text-muted">{{ $application->time_ago }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.career-applications.show', $application) }}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.career-applications.edit', $application) }}" 
                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($application->resume_path)
                                    <a href="{{ route('admin.career-applications.download-resume', $application) }}" 
                                       class="btn btn-sm btn-outline-success" title="Download Resume">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Showing {{ $applications->firstItem() }} to {{ $applications->lastItem() }} 
                    of {{ $applications->total() }} results
                </div>
                {{ $applications->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No career applications found</h5>
                <p class="text-muted">Applications will appear here when candidates apply for positions.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const applicationCheckboxes = document.querySelectorAll('.application-checkbox');
    const bulkUpdateBtn = document.getElementById('bulkUpdateBtn');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkActionForm = document.getElementById('bulkActionForm');
    
    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            applicationCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }
    
    // Individual checkbox change
    applicationCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function updateBulkActions() {
        const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        selectedCountSpan.textContent = count;
        bulkUpdateBtn.disabled = count === 0;
        
        // Update select all checkbox state
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = count === applicationCheckboxes.length;
            selectAllCheckbox.indeterminate = count > 0 && count < applicationCheckboxes.length;
        }
    }
    
    // Bulk action form submission
    if (bulkActionForm) {
        bulkActionForm.addEventListener('submit', function(e) {
            const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
            
            if (selectedCheckboxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one application.');
                return;
            }
            
            // Add selected application IDs to form
            selectedCheckboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'applications[]';
                input.value = checkbox.value;
                this.appendChild(input);
            });
            
            if (!confirm(`Are you sure you want to update ${selectedCheckboxes.length} applications?`)) {
                e.preventDefault();
            }
        });
    }
});
</script>
@endpush
