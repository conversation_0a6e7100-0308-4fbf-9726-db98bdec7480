@extends('admin.layouts.dashboard')

@section('title', 'Edit Career Application')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Edit Career Application</h1>
    <div>
        <a href="{{ route('admin.career-applications.show', $careerApplication) }}" class="btn btn-info">
            <i class="fas fa-eye"></i> View Details
        </a>
        <a href="{{ route('admin.career-applications.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Edit Form -->
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-edit"></i> Update Application Status
                </h5>
            </div>
            <div class="content-card-body">
                <form method="POST" action="{{ route('admin.career-applications.update', $careerApplication) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" name="status" required>
                                @foreach($statusOptions as $key => $label)
                                    <option value="{{ $key }}" 
                                            {{ old('status', $careerApplication->status) == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Current Status</label>
                            <div class="form-control-plaintext">
                                {!! $careerApplication->status_badge !!}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control @error('admin_notes') is-invalid @enderror" 
                                  id="admin_notes" name="admin_notes" rows="5" 
                                  placeholder="Add any notes about this application...">{{ old('admin_notes', $careerApplication->admin_notes) }}</textarea>
                        @error('admin_notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">These notes are for internal use only and will not be visible to the applicant.</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Application
                        </button>
                        
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash"></i> Delete Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Application Summary -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-user"></i> Application Summary
                </h5>
            </div>
            <div class="content-card-body">
                <div class="info-item mb-3">
                    <label class="info-label">Applicant Name</label>
                    <div class="info-value">{{ $careerApplication->name }}</div>
                </div>
                <div class="info-item mb-3">
                    <label class="info-label">Email</label>
                    <div class="info-value">{{ $careerApplication->email }}</div>
                </div>
                <div class="info-item mb-3">
                    <label class="info-label">Phone</label>
                    <div class="info-value">{{ $careerApplication->phone }}</div>
                </div>
                <div class="info-item mb-3">
                    <label class="info-label">Position</label>
                    <div class="info-value">
                        <span class="badge bg-primary">{{ $careerApplication->position_applied }}</span>
                    </div>
                </div>

                <div class="info-item">
                    <label class="info-label">Applied On</label>
                    <div class="info-value">{{ $careerApplication->created_at->format('M j, Y') }}</div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="d-grid gap-2">
                    @if($careerApplication->resume_path)
                    <a href="{{ route('admin.career-applications.download-resume', $careerApplication) }}" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> Download Resume
                    </a>
                    @endif
                    <a href="mailto:{{ $careerApplication->email }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-envelope"></i> Send Email
                    </a>
                    <a href="tel:{{ $careerApplication->phone }}" class="btn btn-info btn-sm">
                        <i class="fas fa-phone"></i> Call Applicant
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this career application?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. The application and associated resume file will be permanently deleted.
                </div>
                <div class="bg-light p-3 rounded">
                    <strong>Applicant:</strong> {{ $careerApplication->name }}<br>
                    <strong>Position:</strong> {{ $careerApplication->position_applied }}<br>
                    <strong>Applied:</strong> {{ $careerApplication->created_at->format('M j, Y') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ route('admin.career-applications.destroy', $careerApplication) }}" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Application
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .info-item {
        border-bottom: 1px solid #eee;
        padding-bottom: 0.5rem;
    }
    
    .info-label {
        font-weight: 600;
        color: #666;
        font-size: 0.875rem;
        display: block;
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        color: #333;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const currentStatus = '{{ $careerApplication->status }}';
    
    // Add change event listener to status select
    statusSelect.addEventListener('change', function() {
        const selectedStatus = this.value;
        
        // Show confirmation for certain status changes
        if (currentStatus === 'new' && selectedStatus !== 'new') {
            if (!confirm('This will mark the application as reviewed. Continue?')) {
                this.value = currentStatus;
                return;
            }
        }
        
        if (selectedStatus === 'rejected') {
            if (!confirm('Are you sure you want to reject this application?')) {
                this.value = currentStatus;
                return;
            }
        }
        
        if (selectedStatus === 'selected') {
            if (!confirm('Are you sure you want to mark this application as selected?')) {
                this.value = currentStatus;
                return;
            }
        }
    });
});
</script>
@endpush
