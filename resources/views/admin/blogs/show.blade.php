@extends('admin.layouts.dashboard')

@section('title', 'View Blog Post')
@section('page-title', 'Blog Post Details')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="content-card mb-4">
            <div class="content-card-header d-flex justify-content-between align-items-center">
                <h5 class="content-card-title">
                    {{ $blog->title }}
                </h5>
                <div>
                    @if($blog->featured)
                        <span class="badge bg-warning me-2">
                            <i class="fas fa-star"></i> Featured
                        </span>
                    @endif
                    @if($blog->is_published)
                        <span class="badge bg-success">Published</span>
                    @else
                        <span class="badge bg-warning">Draft</span>
                    @endif
                </div>
            </div>
            <div class="content-card-body">
                <!-- Featured Image -->
                @if($blog->featured_image)
                <div class="mb-4">
                    <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                         class="img-fluid rounded" 
                         alt="{{ $blog->title }}"
                         style="max-height: 400px; width: 100%; object-fit: cover;">
                </div>
                @endif
                
                <!-- Blog Meta -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Category</h6>
                        <p class="mb-3">
                            <span class="badge" style="background-color: {{ $blog->blogCategory->color }};">
                                {{ $blog->blogCategory->name }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Author</h6>
                        <p class="mb-3">{{ $blog->author }}</p>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4">
                        <h6 class="text-muted">Reading Time</h6>
                        <p class="mb-3">{{ $blog->read_time_text }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Views</h6>
                        <p class="mb-3">
                            <span class="badge bg-info">{{ number_format($blog->views) }}</span>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Published</h6>
                        <p class="mb-3">
                            @if($blog->published_at)
                                {{ $blog->formatted_published_at }}
                            @else
                                <span class="text-muted">Not published</span>
                            @endif
                        </p>
                    </div>
                </div>
                
                <!-- Excerpt -->
                <div class="mb-4">
                    <h6 class="text-muted">Excerpt</h6>
                    <div class="p-3 bg-light rounded">
                        {{ $blog->excerpt }}
                    </div>
                </div>
                
                <!-- Content -->
                <div class="mb-4">
                    <h6 class="text-muted">Content</h6>
                    <div class="content-preview">
                        {!! $blog->content !!}
                    </div>
                </div>
                
                <!-- Tags -->
                @if($blog->tags && count($blog->tags) > 0)
                <div class="mb-4">
                    <h6 class="text-muted">Tags</h6>
                    <div class="d-flex flex-wrap gap-2">
                        @foreach($blog->tags as $tag)
                            <span class="badge bg-light text-dark border">
                                <i class="fas fa-tag me-1"></i>{{ $tag }}
                            </span>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Gallery Images -->
                @if($blog->gallery_images && count($blog->gallery_images) > 0)
                <div class="mb-4">
                    <h6 class="text-muted">Gallery Images</h6>
                    <div class="row">
                        @foreach($blog->gallery_images as $image)
                        <div class="col-md-4 col-sm-6 mb-3">
                            <img src="{{ asset('storage/' . $image) }}" 
                                 class="img-fluid rounded" 
                                 alt="{{ $blog->title }}"
                                 style="height: 150px; width: 100%; object-fit: cover;">
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- SEO Information -->
                @if($blog->meta_title || $blog->meta_description || $blog->meta_keywords)
                <div class="mb-4">
                    <h6 class="text-muted">SEO Information</h6>
                    <div class="bg-light p-3 rounded">
                        @if($blog->meta_title)
                        <div class="mb-2">
                            <strong>Meta Title:</strong><br>
                            <small>{{ $blog->meta_title }}</small>
                        </div>
                        @endif
                        
                        @if($blog->meta_description)
                        <div class="mb-2">
                            <strong>Meta Description:</strong><br>
                            <small>{{ $blog->meta_description }}</small>
                        </div>
                        @endif
                        
                        @if($blog->meta_keywords)
                        <div class="mb-0">
                            <strong>Meta Keywords:</strong><br>
                            <small>{{ $blog->meta_keywords }}</small>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
            <div class="card-footer bg-transparent">
                <a href="{{ route('admin.blogs.edit', $blog) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    Edit Blog Post
                </a>
                <a href="{{ route('admin.blogs.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Blog Posts
                </a>
                @if($blog->is_published)
                <a href="{{ route('blog-details', $blog->slug) }}" 
                   target="_blank" 
                   class="btn btn-outline-info">
                    <i class="fas fa-external-link-alt me-2"></i>
                    View on Website
                </a>
                @endif
                <form method="POST" 
                      action="{{ route('admin.blogs.destroy', $blog) }}" 
                      class="d-inline"
                      onsubmit="return confirm('Are you sure you want to delete this blog post?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-info-circle"></i>
                    Blog Information
                </h5>
            </div>
            <div class="content-card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $blog->created_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $blog->updated_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    @if($blog->is_published)
                        <span class="badge bg-success">Published</span>
                    @else
                        <span class="badge bg-warning">Draft</span>
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>URL Slug:</strong><br>
                    <code>{{ $blog->slug }}</code>
                </div>
                
                <div class="mb-3">
                    <strong>Views:</strong><br>
                    <span class="badge bg-info">{{ number_format($blog->views) }}</span>
                </div>
                
                <div class="mb-0">
                    <strong>Featured:</strong><br>
                    @if($blog->featured)
                        <span class="badge bg-warning">Yes</span>
                    @else
                        <span class="badge bg-light text-dark">No</span>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-chart-bar"></i>
                    Statistics
                </h5>
            </div>
            <div class="content-card-body">
                <div class="mb-3">
                    <strong>Category:</strong><br>
                    <span class="badge" style="background-color: {{ $blog->blogCategory->color }};">
                        {{ $blog->blogCategory->name }}
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Reading Time:</strong><br>
                    <span class="badge bg-info">{{ $blog->read_time_text }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Total Images:</strong><br>
                    <span class="badge bg-secondary">
                        {{ ($blog->featured_image ? 1 : 0) + ($blog->gallery_images ? count($blog->gallery_images) : 0) }} images
                    </span>
                </div>
                
                <div class="mb-0">
                    <strong>Total Tags:</strong><br>
                    <span class="badge bg-primary">{{ $blog->tags ? count($blog->tags) : 0 }} tags</span>
                </div>
            </div>
        </div>
        
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-cogs"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.blogs.edit', $blog) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit Blog Post
                    </a>
                    <a href="{{ route('admin.blogs.create') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus me-2"></i>
                        Create New Post
                    </a>
                    @if($blog->is_published)
                    <a href="{{ route('blog-details', $blog->slug) }}" 
                       target="_blank" 
                       class="btn btn-outline-info btn-sm">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View on Website
                    </a>
                    @endif
                    <a href="{{ route('admin.blog-categories.show', $blog->blogCategory) }}" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-folder me-2"></i>
                        View Category
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.content-preview {
    font-size: 1.1rem;
    line-height: 1.7;
}

.content-preview h2 {
    color: #2c3e50;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.content-preview h3 {
    color: #34495e;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
}

.content-preview ul, 
.content-preview ol {
    margin-bottom: 1rem;
}

.content-preview li {
    margin-bottom: 0.25rem;
}
</style>
@endpush
