@extends('admin.layouts.dashboard')

@section('title', 'Blog Posts')
@section('page-title', 'Blog Posts')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0">Manage Blog Posts</h4>
        <p class="text-muted mb-0">Create and manage your blog content</p>
    </div>
    <a href="{{ route('admin.blogs.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Add New Post
    </a>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error') || $errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') ?: $errors->first() }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogs->total() }}</h3>
                    <p class="stats-label">Total Posts</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogs->where('is_published', true)->count() }}</h3>
                    <p class="stats-label">Published</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $blogs->where('featured', true)->count() }}</h3>
                    <p class="stats-label">Featured</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-card-body">
                <div class="stats-icon bg-info">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ number_format($blogs->sum('views')) }}</h3>
                    <p class="stats-label">Total Views</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content-card">
    <div class="content-card-body">
        @if($blogs->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Post</th>
                            <th>Category</th>
                            <th>Author</th>
                            <th>Status</th>
                            <th>Views</th>
                            <th>Published</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($blogs as $blog)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($blog->first_image)
                                    <img src="{{ asset('storage/' . $blog->first_image) }}" 
                                         alt="{{ $blog->title }}" 
                                         class="me-3 rounded"
                                         style="width: 60px; height: 40px; object-fit: cover;">
                                    @else
                                    <div class="me-3 bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 40px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    @endif
                                    <div>
                                        <h6 class="mb-0">{{ Str::limit($blog->title, 40) }}</h6>
                                        <small class="text-muted">{{ Str::limit($blog->excerpt, 60) }}</small>
                                        @if($blog->featured)
                                            <span class="badge bg-warning ms-1">
                                                <i class="fas fa-star"></i>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge" style="background-color: {{ $blog->blogCategory->color }}; color: white;">
                                    {{ $blog->blogCategory->name }}
                                </span>
                            </td>
                            <td>
                                <div>
                                    <span>{{ $blog->author }}</span><br>
                                    <small class="text-muted">{{ $blog->read_time_text }}</small>
                                </div>
                            </td>
                            <td>
                                @if($blog->is_published)
                                    <span class="badge bg-success">Published</span>
                                @else
                                    <span class="badge bg-warning">Draft</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ number_format($blog->views) }}</span>
                            </td>
                            <td>
                                @if($blog->published_at)
                                    <small class="text-muted">{{ $blog->formatted_published_at }}</small>
                                @else
                                    <small class="text-muted">Not published</small>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.blogs.show', $blog) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.blogs.edit', $blog) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($blog->is_published)
                                    <a href="{{ route('blog-details', $blog->slug) }}" 
                                       target="_blank"
                                       class="btn btn-sm btn-outline-secondary" 
                                       title="View on Website">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    @endif
                                    <form method="POST" 
                                          action="{{ route('admin.blogs.destroy', $blog) }}" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this blog post?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-danger" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $blogs->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Blog Posts Found</h5>
                <p class="text-muted">Create your first blog post to get started.</p>
                <a href="{{ route('admin.blogs.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create First Post
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@push('styles')
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card-body {
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.5rem;
}

.stats-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
}

.stats-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
@endpush
