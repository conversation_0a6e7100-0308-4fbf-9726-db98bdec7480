@extends('admin.layouts.dashboard')

@section('title', 'Edit Blog Post')
@section('page-title', 'Edit Blog Post')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<form method="POST" action="{{ route('admin.blogs.update', $blog) }}" enctype="multipart/form-data" id="blogEditForm">
    @csrf
    @method('PUT')
    
    <div class="row">
        <div class="col-lg-8">
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-info-circle"></i>
                        Blog Information
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Blog Title <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('title') is-invalid @enderror" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $blog->title) }}" 
                                       required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Current slug: <code>{{ $blog->slug }}</code></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="blog_category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select @error('blog_category_id') is-invalid @enderror" 
                                        id="blog_category_id" 
                                        name="blog_category_id" 
                                        required>
                                    <option value="">Select Category</option>
                                    @foreach($blogCategories as $category)
                                        <option value="{{ $category->id }}" {{ old('blog_category_id', $blog->blog_category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('blog_category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="excerpt" class="form-label">Excerpt <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                  id="excerpt" 
                                  name="excerpt" 
                                  rows="3" 
                                  required>{{ old('excerpt', $blog->excerpt) }}</textarea>
                        @error('excerpt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content"
                                  name="content"
                                  rows="15"
                                  required>{{ old('content', $blog->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Use the rich text editor to format your blog content</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="author" class="form-label">Author <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('author') is-invalid @enderror" 
                                       id="author" 
                                       name="author" 
                                       value="{{ old('author', $blog->author) }}" 
                                       required>
                                @error('author')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="read_time" class="form-label">Reading Time (minutes) <span class="text-danger">*</span></label>
                                <input type="number" 
                                       class="form-control @error('read_time') is-invalid @enderror" 
                                       id="read_time" 
                                       name="read_time" 
                                       value="{{ old('read_time', $blog->read_time) }}" 
                                       min="1" 
                                       max="60" 
                                       required>
                                @error('read_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="tags" class="form-label">Tags</label>
                        <input type="text" 
                               class="form-control @error('tags') is-invalid @enderror" 
                               id="tags" 
                               name="tags_input" 
                               value="{{ old('tags_input', $blog->tags ? implode(', ', $blog->tags) : '') }}" 
                               placeholder="Enter tags separated by commas">
                        @error('tags')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div id="tags-preview" class="mt-2">
                            @if($blog->tags)
                                @foreach($blog->tags as $tag)
                                    <span class="badge bg-primary me-1 mb-1">{{ $tag }}</span>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SEO Section -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-search"></i>
                        SEO Settings
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" 
                               class="form-control @error('meta_title') is-invalid @enderror" 
                               id="meta_title" 
                               name="meta_title" 
                               value="{{ old('meta_title', $blog->meta_title) }}" 
                               maxlength="255">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                  id="meta_description" 
                                  name="meta_description" 
                                  rows="3" 
                                  maxlength="500">{{ old('meta_description', $blog->meta_description) }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" 
                               class="form-control @error('meta_keywords') is-invalid @enderror" 
                               id="meta_keywords" 
                               name="meta_keywords" 
                               value="{{ old('meta_keywords', $blog->meta_keywords) }}">
                        @error('meta_keywords')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Current Featured Image -->
            @if($blog->featured_image)
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-image"></i>
                        Current Featured Image
                    </h5>
                </div>
                <div class="content-card-body">
                    <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                         class="img-fluid rounded" 
                         alt="{{ $blog->title }}">
                    <small class="text-muted d-block mt-2">Upload new image to replace current one</small>
                </div>
            </div>
            @endif
            
            <!-- Featured Image -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-upload"></i>
                        Update Featured Image
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="featured_image" class="form-label">Upload New Featured Image</label>
                        <input type="file"
                               class="form-control @error('featured_image') is-invalid @enderror"
                               id="featured_image"
                               name="featured_image"
                               accept="image/jpeg,image/png,image/jpg,image/gif,image/webp">
                        @error('featured_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Leave empty to keep current image</small>
                    </div>
                    <div id="featured-image-preview"></div>
                </div>
            </div>
            
            <!-- Current Gallery Images -->
            @if($blog->gallery_images && count($blog->gallery_images) > 0)
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-images"></i>
                        Current Gallery Images
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="row">
                        @foreach($blog->gallery_images as $image)
                        <div class="col-6 mb-2">
                            <img src="{{ asset('storage/' . $image) }}" 
                                 class="img-fluid rounded" 
                                 style="height: 80px; object-fit: cover;">
                        </div>
                        @endforeach
                    </div>
                    <small class="text-muted">Upload new images to replace current gallery</small>
                </div>
            </div>
            @endif
            
            <!-- Gallery Images -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-upload"></i>
                        Update Gallery Images
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">Upload New Gallery Images</label>
                        <input type="file"
                               class="form-control @error('gallery_images') is-invalid @enderror"
                               id="gallery_images"
                               name="gallery_images[]"
                               multiple
                               accept="image/jpeg,image/png,image/jpg,image/gif,image/webp">
                        @error('gallery_images')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Leave empty to keep current gallery</small>
                    </div>
                    <div id="gallery-images-preview" class="row"></div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-chart-bar"></i>
                        Statistics
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="mb-3">
                        <strong>Views:</strong><br>
                        <span class="badge bg-info">{{ number_format($blog->views) }} views</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small class="text-muted">{{ $blog->created_at->format('M d, Y H:i') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ $blog->updated_at->format('M d, Y H:i') }}</small>
                    </div>
                    
                    @if($blog->published_at)
                    <div class="mb-0">
                        <strong>Published:</strong><br>
                        <small class="text-muted">{{ $blog->published_at->format('M d, Y H:i') }}</small>
                    </div>
                    @endif
                </div>
            </div>
            
            <!-- Settings -->
            <div class="content-card mb-4">
                <div class="content-card-header">
                    <h5 class="content-card-title">
                        <i class="fas fa-cogs"></i>
                        Settings
                    </h5>
                </div>
                <div class="content-card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" {{ old('featured', $blog->featured) ? 'checked' : '' }}>
                        <label class="form-check-label" for="featured">
                            <i class="fas fa-star text-warning me-1"></i>
                            Featured Post
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1" {{ old('is_published', $blog->is_published) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_published">
                            <i class="fas fa-eye text-success me-1"></i>
                            Published Status
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="content-card">
                <div class="content-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            Update Blog Post
                        </button>
                        <a href="{{ route('admin.blogs.show', $blog) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>
                            View Blog Post
                        </a>
                        <a href="{{ route('admin.blogs.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Blog Posts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('css/admin-ckeditor.css') }}">
@endpush

@push('scripts')
<!-- CKEditor 5 -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
// Initialize CKEditor
let contentEditor;
ClassicEditor
    .create(document.querySelector('#content'), {
        toolbar: {
            items: [
                'heading', '|',
                'bold', 'italic', 'underline', 'strikethrough', '|',
                'fontSize', 'fontColor', 'fontBackgroundColor', '|',
                'alignment', '|',
                'numberedList', 'bulletedList', '|',
                'outdent', 'indent', '|',
                'link', 'blockQuote', 'insertTable', '|',
                'imageUpload', 'mediaEmbed', '|',
                'horizontalLine', '|',
                'undo', 'redo', '|',
                'sourceEditing'
            ]
        },
        language: 'en',
        image: {
            toolbar: [
                'imageTextAlternative',
                'imageStyle:inline',
                'imageStyle:block',
                'imageStyle:side',
                '|',
                'toggleImageCaption',
                'imageResize'
            ]
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells',
                'tableCellProperties',
                'tableProperties'
            ]
        },
        licenseKey: '',
        height: 400
    })
    .then(editor => {
        contentEditor = editor;
        console.log('CKEditor initialized successfully');
    })
    .catch(error => {
        console.error('CKEditor initialization error:', error);
    });

// Tags handling
document.getElementById('tags').addEventListener('input', function() {
    const tagsInput = this.value;
    const tagsPreview = document.getElementById('tags-preview');

    if (tagsInput.trim()) {
        const tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag);
        let html = '';
        tags.forEach(tag => {
            html += `<span class="badge bg-primary me-1 mb-1">${tag}</span>`;
        });
        tagsPreview.innerHTML = html;
    } else {
        tagsPreview.innerHTML = '';
    }
});

// Featured image preview
document.getElementById('featured_image').addEventListener('change', function(e) {
    const preview = document.getElementById('featured-image-preview');
    const file = e.target.files[0];

    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `
                <div class="mt-2">
                    <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 200px;">
                    <div class="mt-1">
                        <small class="text-muted">${file.name} (${Math.round(file.size / 1024)}KB)</small>
                    </div>
                </div>
            `;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});

// Gallery images preview
document.getElementById('gallery_images').addEventListener('change', function(e) {
    const preview = document.getElementById('gallery-images-preview');
    const files = Array.from(e.target.files);

    preview.innerHTML = '';

    files.forEach((file, index) => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'col-6 mb-2';
                div.innerHTML = `
                    <img src="${e.target.result}" class="img-fluid rounded" style="height: 80px; object-fit: cover;">
                    <small class="text-muted d-block">${Math.round(file.size / 1024)}KB</small>
                `;
                preview.appendChild(div);
            };
            reader.readAsDataURL(file);
        }
    });
});

// Form submission
document.getElementById('blogEditForm').addEventListener('submit', function(e) {
    // Update content from CKEditor
    if (contentEditor) {
        document.getElementById('content').value = contentEditor.getData();
    }

    // Convert tags input to array
    const tagsInput = document.getElementById('tags').value;
    if (tagsInput.trim()) {
        const tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag);

        // Create hidden inputs for tags
        tags.forEach(tag => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'tags[]';
            input.value = tag;
            this.appendChild(input);
        });
    }

    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Blog Post...';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
@endpush
