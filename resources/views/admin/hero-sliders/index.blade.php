@extends('admin.layouts.dashboard')

@section('title', 'Hero Sliders Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>Hero Sliders Management
                    </h3>
                    <a href="{{ route('admin.hero-sliders.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Slider
                    </a>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($sliders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Order</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Subtitle</th>
                                        <th>Button</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sliders as $slider)
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">{{ $slider->order }}</span>
                                            </td>
                                            <td>
                                                @if($slider->background_image)
                                                    <img src="{{ $slider->background_image_url }}" 
                                                         alt="Slider Image" 
                                                         class="img-thumbnail" 
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                @else
                                                    <span class="text-muted">No Image</span>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $slider->title }}</strong>
                                            </td>
                                            <td>
                                                {{ Str::limit($slider->subtitle, 30) }}
                                            </td>
                                            <td>
                                                @if($slider->button_text)
                                                    <span class="badge bg-info">{{ $slider->button_text }}</span>
                                                @else
                                                    <span class="text-muted">No Button</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($slider->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.hero-sliders.show', $slider) }}" 
                                                       class="btn btn-sm btn-outline-info" 
                                                       title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.hero-sliders.edit', $slider) }}" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.hero-sliders.destroy', $slider) }}" 
                                                          method="POST" 
                                                          class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this slider?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Hero Sliders Found</h5>
                            <p class="text-muted">Create your first hero slider to get started.</p>
                            <a href="{{ route('admin.hero-sliders.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add New Slider
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-hide alerts after 5 seconds
setTimeout(function() {
    $('.alert').fadeOut('slow');
}, 5000);
</script>
@endpush
