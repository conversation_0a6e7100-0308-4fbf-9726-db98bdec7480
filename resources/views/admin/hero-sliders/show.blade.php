@extends('admin.layouts.dashboard')

@section('title', 'View Hero Slider')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>View Hero Slider
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.hero-sliders.edit', $heroSlider) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit
                        </a>
                        <a href="{{ route('admin.hero-sliders.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Title -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Title</h5>
                                <h3 class="mb-0">{{ $heroSlider->title }}</h3>
                            </div>

                            <!-- Subtitle -->
                            @if($heroSlider->subtitle)
                                <div class="mb-4">
                                    <h5 class="text-muted mb-2">Subtitle</h5>
                                    <p class="lead mb-0">{{ $heroSlider->subtitle }}</p>
                                </div>
                            @endif

                            <!-- Description -->
                            @if($heroSlider->description)
                                <div class="mb-4">
                                    <h5 class="text-muted mb-2">Description</h5>
                                    <div class="border rounded p-3 bg-light">
                                        {!! $heroSlider->description !!}
                                    </div>
                                </div>
                            @endif

                            <!-- Button Information -->
                            @if($heroSlider->button_text || $heroSlider->button_link)
                                <div class="mb-4">
                                    <h5 class="text-muted mb-2">Button Information</h5>
                                    <div class="row">
                                        @if($heroSlider->button_text)
                                            <div class="col-md-6">
                                                <strong>Button Text:</strong>
                                                <span class="badge bg-info ms-2">{{ $heroSlider->button_text }}</span>
                                            </div>
                                        @endif
                                        @if($heroSlider->button_link)
                                            <div class="col-md-6">
                                                <strong>Button Link:</strong>
                                                <a href="{{ $heroSlider->button_link }}" target="_blank" class="ms-2">
                                                    {{ Str::limit($heroSlider->button_link, 30) }}
                                                    <i class="fas fa-external-link-alt ms-1"></i>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <!-- Metadata -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Metadata</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Display Order:</strong>
                                        <span class="badge bg-secondary ms-2">{{ $heroSlider->order }}</span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Status:</strong>
                                        @if($heroSlider->is_active)
                                            <span class="badge bg-success ms-2">Active</span>
                                        @else
                                            <span class="badge bg-danger ms-2">Inactive</span>
                                        @endif
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Created:</strong>
                                        <span class="ms-2">{{ $heroSlider->created_at->format('M d, Y') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Background Image -->
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Background Image</h5>
                                @if($heroSlider->background_image)
                                    <div class="text-center">
                                        <img src="{{ $heroSlider->background_image_url }}" 
                                             alt="{{ $heroSlider->title }}" 
                                             class="img-fluid rounded shadow-sm" 
                                             style="max-height: 300px;">
                                        <div class="mt-2">
                                            <a href="{{ $heroSlider->background_image_url }}" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-expand me-2"></i>View Full Size
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-image fa-3x mb-3"></i>
                                        <p>No background image uploaded</p>
                                    </div>
                                @endif
                            </div>

                            <!-- Preview Button -->
                            @if($heroSlider->button_text && $heroSlider->button_link)
                                <div class="mb-4">
                                    <h5 class="text-muted mb-2">Button Preview</h5>
                                    <div class="text-center">
                                        <a href="{{ $heroSlider->button_link }}" 
                                           target="_blank" 
                                           class="btn btn-primary">
                                            {{ $heroSlider->button_text }}
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Last updated: {{ $heroSlider->updated_at->format('M d, Y \a\t g:i A') }}
                            </small>
                        </div>
                        <div>
                            <a href="{{ route('admin.hero-sliders.edit', $heroSlider) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-2"></i>Edit Slider
                            </a>
                            <form action="{{ route('admin.hero-sliders.destroy', $heroSlider) }}" 
                                  method="POST" 
                                  class="d-inline ms-2"
                                  onsubmit="return confirm('Are you sure you want to delete this slider?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
