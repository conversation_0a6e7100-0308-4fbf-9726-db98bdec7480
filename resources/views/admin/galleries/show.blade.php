@extends('admin.layouts.dashboard')

@section('title', 'View Gallery Item')
@section('page-title', 'Gallery Item Details')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-image me-2"></i>
                    {{ $gallery->title }}
                </h5>
                <span class="badge bg-{{ $gallery->status ? 'success' : 'danger' }} fs-6">
                    {{ $gallery->status ? 'Active' : 'Inactive' }}
                </span>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <img src="{{ asset('storage/' . $gallery->image) }}" 
                         alt="{{ $gallery->title }}" 
                         class="img-fluid rounded shadow"
                         style="max-height: 400px;">
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Title</h6>
                        <p class="fs-5">{{ $gallery->title }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Project Category</h6>
                        <p>
                            <span class="badge bg-primary fs-6">{{ $gallery->projectCategory->name }}</span>
                        </p>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted">Description</h6>
                    @if($gallery->description)
                        <p>{{ $gallery->description }}</p>
                    @else
                        <p class="text-muted fst-italic">No description provided.</p>
                    @endif
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Created Date</h6>
                        <p>{{ $gallery->created_at->format('F d, Y') }}</p>
                        <small class="text-muted">{{ $gallery->created_at->format('g:i A') }}</small>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Last Updated</h6>
                        <p>{{ $gallery->updated_at->format('F d, Y') }}</p>
                        <small class="text-muted">{{ $gallery->updated_at->format('g:i A') }}</small>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    Edit Item
                </a>
                <a href="{{ route('admin.galleries.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Gallery
                </a>
                <form method="POST" 
                      action="{{ route('admin.galleries.destroy', $gallery) }}" 
                      class="d-inline"
                      onsubmit="return confirm('Are you sure you want to delete this gallery item? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Item Details
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-{{ $gallery->status ? 'success' : 'danger' }}">
                        {{ $gallery->status ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Category:</strong><br>
                    <a href="{{ route('admin.project-categories.show', $gallery->projectCategory) }}" 
                       class="text-decoration-none">
                        {{ $gallery->projectCategory->name }}
                    </a>
                </div>
                
                <div class="mb-3">
                    <strong>Image File:</strong><br>
                    <small class="text-muted">{{ basename($gallery->image) }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $gallery->created_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $gallery->updated_at->format('M d, Y H:i') }}</small>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit Item
                    </a>
                    <a href="{{ route('admin.galleries.create') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus me-2"></i>
                        Add New Item
                    </a>
                    <a href="{{ asset('storage/' . $gallery->image) }}" 
                       target="_blank" 
                       class="btn btn-outline-info btn-sm">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View Full Image
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images me-2"></i>
                    Related Items
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-0">
                    <small>Other gallery items in the same category will be shown here in future updates.</small>
                </p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card img {
    transition: transform 0.3s ease;
}
.card img:hover {
    transform: scale(1.02);
}
</style>
@endpush
