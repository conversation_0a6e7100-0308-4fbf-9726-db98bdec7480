@extends('admin.layouts.dashboard')

@section('title', 'Gallery Management')
@section('page-title', 'Gallery Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images me-2"></i>
                    All Gallery Items
                </h5>
                <a href="{{ route('admin.galleries.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Add New Item
                </a>
            </div>
            <div class="card-body">
                @if($galleries->count() > 0)
                    <div class="row">
                        @foreach($galleries as $gallery)
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <img src="{{ asset('storage/' . $gallery->image) }}" 
                                         class="card-img-top" 
                                         alt="{{ $gallery->title }}"
                                         style="height: 200px; object-fit: cover;">
                                    <div class="position-absolute top-0 end-0 p-2">
                                        <span class="badge bg-{{ $gallery->status ? 'success' : 'danger' }}">
                                            {{ $gallery->status ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">{{ $gallery->title }}</h6>
                                    <p class="card-text text-muted small mb-2">
                                        <i class="fas fa-tag me-1"></i>
                                        {{ $gallery->projectCategory->name }}
                                    </p>
                                    <p class="card-text flex-grow-1">
                                        {{ Str::limit($gallery->description, 80) }}
                                    </p>
                                    <small class="text-muted mb-3">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $gallery->created_at->format('M d, Y') }}
                                    </small>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ route('admin.galleries.show', $gallery) }}" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.galleries.edit', $gallery) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" 
                                              action="{{ route('admin.galleries.destroy', $gallery) }}" 
                                              class="d-inline"
                                              onsubmit="return confirm('Are you sure you want to delete this gallery item?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $galleries->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Gallery Items Found</h5>
                        <p class="text-muted">Get started by adding your first gallery item.</p>
                        <a href="{{ route('admin.galleries.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Add New Item
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card-img-top {
    transition: transform 0.3s ease;
}
.card:hover .card-img-top {
    transform: scale(1.05);
}
.card {
    transition: box-shadow 0.3s ease;
}
.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
@endpush
