@extends('admin.layouts.dashboard')

@section('title', 'Edit Gallery Item')
@section('page-title', 'Edit Gallery Item')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit: {{ $gallery->title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.galleries.update', $gallery) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('title') is-invalid @enderror" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $gallery->title) }}" 
                               required 
                               placeholder="Enter gallery item title">
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="project_category_id" class="form-label">Project Category <span class="text-danger">*</span></label>
                        <select class="form-select @error('project_category_id') is-invalid @enderror" 
                                id="project_category_id" 
                                name="project_category_id" 
                                required>
                            <option value="">Select a project category</option>
                            @foreach($projectCategories as $category)
                                <option value="{{ $category->id }}" 
                                        {{ old('project_category_id', $gallery->project_category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('project_category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">Image</label>
                        <input type="file" 
                               class="form-control @error('image') is-invalid @enderror" 
                               id="image" 
                               name="image" 
                               accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Leave empty to keep current image. Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="4" 
                                  placeholder="Enter description for this gallery item">{{ old('description', $gallery->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="status" 
                                   name="status" 
                                   value="1" 
                                   {{ old('status', $gallery->status) ? 'checked' : '' }}>
                            <label class="form-check-label" for="status">
                                Active Status
                            </label>
                        </div>
                        <small class="form-text text-muted">Check to make this gallery item active and visible.</small>
                    </div>
                    
                    <div class="mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update Gallery Item
                        </button>
                        <a href="{{ route('admin.galleries.show', $gallery) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>
                            View Item
                        </a>
                        <a href="{{ route('admin.galleries.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Gallery
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-image me-2"></i>
                    Current Image
                </h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ asset('storage/' . $gallery->image) }}" 
                     alt="{{ $gallery->title }}" 
                     class="img-fluid rounded mb-3"
                     style="max-height: 250px;">
                <p class="text-muted mb-0">{{ $gallery->title }}</p>
                <small class="text-muted">{{ $gallery->projectCategory->name }}</small>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Item Info
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-{{ $gallery->status ? 'success' : 'danger' }}">
                        {{ $gallery->status ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $gallery->created_at->format('M d, Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $gallery->updated_at->format('M d, Y H:i') }}</small>
                </div>
                
                <hr>
                
                <div class="d-grid">
                    <form method="POST" 
                          action="{{ route('admin.galleries.destroy', $gallery) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this gallery item? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Item
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    New Image Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="image-preview" class="text-center" style="display: none;">
                    <img id="preview-img" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                    <p class="mt-2 mb-0"><small class="text-muted">New Image Preview</small></p>
                </div>
                <p class="text-muted mb-0"><small>Select a new image to see preview</small></p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });
});
</script>
@endpush
