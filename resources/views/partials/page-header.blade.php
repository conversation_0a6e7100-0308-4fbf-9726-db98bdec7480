{{-- Dynamic Page Header Component --}}
@if(isset($pageHeader) && $pageHeader)
    <section class="{{ $sectionClass ?? 'about-hero-section' }}">
        <div class="hero-background"
             @if($pageHeader->background_image)
                 style="background-image: url('{{ $pageHeader->background_image_url }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"
             @endif>
            <div class="hero-overlay"></div>
        </div>
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="hero-title">{{ $pageHeader->title }}</h1>
                @if($pageHeader->subtitle)
                    <p class="hero-subtitle">{{ $pageHeader->subtitle }}</p>
                @endif
            </div>
        </div>
    </section>
@else
    {{-- Fallback content when no page header is available --}}
    <section class="{{ $sectionClass ?? 'about-hero-section' }}">
        <div class="hero-background">
            <div class="hero-overlay"></div>
        </div>
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="hero-title">{{ $fallbackTitle ?? 'Page Title' }}</h1>
                @if(isset($fallbackSubtitle))
                    <p class="hero-subtitle">{{ $fallbackSubtitle }}</p>
                @endif
            </div>
        </div>
    </section>
@endif
