@extends('layouts.app')

@section('title', 'Privacy Policy - Hestia Abodes Real Estate Consultancy')
@section('meta_description', 'Read our comprehensive privacy policy to understand how <PERSON><PERSON><PERSON> Abodes collects, uses, and protects your personal information in accordance with privacy laws.')
@section('meta_keywords', 'privacy policy, data protection, personal information, Hestia Abodes, real estate privacy')
@section('canonical_url', 'https://hestiaabodes.in/privacy-policy')

@section('body')
    <!-- Page Header -->
    @if($pageHeader)
        <section class="page-header" style="background-image: url('{{ $pageHeader->background_image ? asset('storage/' . $pageHeader->background_image) : asset('images/default-header.jpg') }}');">
            <div class="page-header-overlay"></div>
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-header-content text-center">
                            <h1 class="page-title">{{ $pageHeader->title }}</h1>
                            <p class="page-subtitle">{{ $pageHeader->subtitle }}</p>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb justify-content-center">
                                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Privacy Policy</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @else
        <section class="page-header" style="background-image: url('{{ asset('images/default-header.jpg') }}');">
            <div class="page-header-overlay"></div>
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-header-content text-center">
                            <h1 class="page-title">Privacy Policy</h1>
                            <p class="page-subtitle">Your privacy is important to us</p>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb justify-content-center">
                                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Privacy Policy</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Privacy Policy Content -->
    <section class="privacy-policy-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="privacy-content">
                        <!-- Introduction -->
                        <div class="privacy-block mb-5">
                            <h2 class="section-title">Privacy Policy</h2>
                            <h3 class="company-name">Hestia Abodes Real Estate Consultancy</h3>
                            <p class="lead">At Hestia Abodes, your privacy is important to us. This Privacy Policy outlines how we collect, use, and safeguard your personal information. Our commitment is to maintain transparency and trust, ensuring that all data collected is handled in accordance with applicable privacy laws and best practices in the real estate industry.</p>
                        </div>

                        <!-- Changes to Privacy Policy -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-sync-alt me-2"></i>Changes to Our Privacy Policy
                            </h3>
                            <p>To keep you informed, our privacy policy is accessible from every page of our website via the footer. Any changes to this policy will be reflected with an updated date at the bottom of the page. We encourage you to periodically review this page to stay informed about how we protect your information.</p>
                        </div>

                        <!-- Website Usage Information -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-chart-line me-2"></i>Website Usage Information
                            </h3>
                            <p>Our website uses tracking technologies to analyze traffic patterns and usage behavior. We monitor anonymous data such as:</p>
                            <ul class="privacy-list">
                                <li>Pages visited</li>
                                <li>Time spent on pages</li>
                                <li>Referring websites</li>
                                <li>Click behavior</li>
                            </ul>
                            <p>Hestia Abodes does not personally identify individual users unless they voluntarily submit their information via a form. The information collected helps us enhance your website experience and deliver relevant property listings and services.</p>
                            
                            <div class="third-party-services mt-4">
                                <h4 class="sub-title">We use third-party services, including:</h4>
                                <div class="service-item">
                                    <strong>Google Analytics</strong> (for anonymous traffic and behavioral analysis)<br>
                                    <a href="https://policies.google.com/privacy?hl=en" target="_blank" class="privacy-link">Google Privacy Policy</a>
                                </div>
                                <div class="service-item mt-3">
                                    <strong>Meta Platforms (Facebook/Instagram), LinkedIn, and Twitter Widgets</strong><br>
                                    <p class="mb-2">These platforms may collect data if you engage with our content via social sharing buttons or embedded widgets. These services are governed by their respective privacy policies:</p>
                                    <ul class="social-links">
                                        <li><a href="http://www.facebook.com/about/privacy/" target="_blank" class="privacy-link">Facebook Privacy Policy</a></li>
                                        <li><a href="https://twitter.com/en/privacy" target="_blank" class="privacy-link">Twitter Privacy Policy</a></li>
                                        <li><a href="https://www.linkedin.com/legal/privacy-policy" target="_blank" class="privacy-link">LinkedIn Privacy Policy</a></li>
                                        <li><a href="https://www.youtube.com/yt/about/policies/#community-guidelines" target="_blank" class="privacy-link">YouTube Community Guidelines</a></li>
                                        <li><a href="https://www.instagram.com/about/legal/terms/api/" target="_blank" class="privacy-link">Instagram API Terms</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Information We Collect -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-database me-2"></i>The Information We Collect
                            </h3>
                            <p>When you interact with our website, we may collect the following types of personal information via contact forms, newsletter sign-ups, property inquiry forms, and chat widgets:</p>
                            <ul class="privacy-list">
                                <li>Name</li>
                                <li>Email Address</li>
                                <li>Phone Number</li>
                                <li>Preferred Property Type or Budget</li>
                                <li>Location Preferences</li>
                                <li>Message or Inquiry Details</li>
                            </ul>
                        </div>

                        <!-- Use of Collected Information -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-cogs me-2"></i>Use of Collected Information
                            </h3>
                            <p>The information is used to:</p>
                            <ul class="privacy-list">
                                <li>Respond to your inquiries or service requests</li>
                                <li>Share property listings, offers, or updates that match your preferences</li>
                                <li>Contact you by phone, email, SMS, or WhatsApp</li>
                                <li>Provide follow-up support or schedule viewings</li>
                            </ul>
                            <div class="consent-notice mt-3">
                                <p><strong>Important:</strong> By submitting your details, you consent to receiving communications from Hestia Abodes, even if your number is registered under DND (Do Not Disturb).</p>
                            </div>
                        </div>

                        <!-- Third-Party Links -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-external-link-alt me-2"></i>Third-Party Links
                            </h3>
                            <p>Our website may contain links to third-party sites, including property listing platforms, legal service providers, or partner agents. Hestia Abodes is not responsible for the privacy practices or content of these external websites. We advise users to review their respective privacy policies before sharing personal information.</p>
                        </div>

                        <!-- Security -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-shield-alt me-2"></i>Security of Your Information
                            </h3>
                            <p>We take appropriate security measures to protect your personal information from unauthorized access, alteration, or disclosure. However, please note that no method of transmission over the Internet or method of electronic storage is 100% secure.</p>
                        </div>

                        <!-- Policy Modifications -->
                        <div class="privacy-block mb-5">
                            <h3 class="block-title">
                                <i class="fas fa-edit me-2"></i>Policy Modifications
                            </h3>
                            <p>Hestia Abodes reserves the right to update or modify this policy at any time without prior notice. If future changes affect how we use your personal information in a materially different way, we will obtain your consent before implementing those changes.</p>
                        </div>

                        <!-- Contact Information -->
                        <div class="privacy-block">
                            <h3 class="block-title">
                                <i class="fas fa-envelope me-2"></i>Contact Us
                            </h3>
                            <p>For any questions or concerns regarding our privacy practices, please contact us at:</p>
                            <div class="contact-info">
                                <a href="mailto:<EMAIL>" class="contact-email">
                                    <i class="fas fa-envelope me-2"></i><EMAIL>
                                </a>
                            </div>
                        </div>

                        <!-- Last Updated -->
                        <div class="last-updated mt-5 pt-4 border-top">
                            <p class="text-muted"><small>Last updated: {{ date('F d, Y') }}</small></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
.privacy-policy-section {
    background: #f8f9fa;
    min-height: 100vh;
}

.privacy-content {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.section-title {
    color: #000;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.company-name {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #000;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.privacy-block {
    border-left: 4px solid #000;
    padding-left: 1.5rem;
    margin-bottom: 2rem;
}

.block-title {
    color: #000;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.block-title i {
    color: #333;
    margin-right: 0.5rem;
}

.sub-title {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.privacy-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 1.5rem;
}

.privacy-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 2rem;
    color: #333;
}

.privacy-list li:before {
    content: "•";
    color: #000;
    font-weight: bold;
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

.privacy-list li:last-child {
    border-bottom: none;
}

.third-party-services {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #000;
    border: 1px solid #dee2e6;
}

.service-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 2px solid #000;
    margin-bottom: 1rem;
}

.social-links {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
}

.social-links li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.social-links li:last-child {
    border-bottom: none;
}

.privacy-link {
    color: #000;
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid transparent;
    transition: all 0.3s ease;
}

.privacy-link:hover {
    color: #333;
    border-bottom-color: #000;
    text-decoration: none;
}

.consent-notice {
    background: #f8f9fa;
    border: 2px solid #000;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.contact-info {
    background: #f8f9fa;
    border: 2px solid #000;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    margin-top: 1rem;
}

.contact-email {
    color: #000;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    display: inline-block;
    padding-bottom: 0.25rem;
}

.contact-email:hover {
    color: #333;
    border-bottom-color: #000;
}

.last-updated {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #000;
}

/* Page Header Fixes */
.page-header {
    background-color: #000;
    color: white;
    padding: 6rem 0 4rem;
    margin-top: 0;
    position: relative;
    z-index: 1;
}

.page-header-overlay {
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  
}

/* Ensure navbar is visible above page header */
.navbar {
    z-index: 1050 !important;
}

/* Add proper spacing for fixed navbar */
body {
    padding-top: 0;
}

.page-header-content {
    position: relative;
    z-index: 3;
}

.page-title {
    color: white;
    font-weight: 700;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.9);
}

.breadcrumb {
    background: transparent;
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .privacy-content {
        padding: 2rem 1.5rem;
        margin-top: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .company-name {
        font-size: 1.3rem;
    }

    .block-title {
        font-size: 1.2rem;
    }

    .privacy-block {
        padding-left: 1rem;
        border-left-width: 3px;
    }

    .page-header {
        padding: 5rem 0 3rem;
    }

    .contact-info {
        padding: 1.5rem;
    }

    .contact-email {
        font-size: 1.1rem;
    }
}
</style>
@endpush
