@extends('layouts.app')

@section('title', 'Gallery - Premium Properties & Projects | Hestia Abodes')
@section('meta_description', 'Explore our gallery of premium properties, completed projects, and luxury developments in Pune. See the quality and craftsmanship of Hestia Abodes projects.')
@section('meta_keywords', 'property gallery, real estate gallery, Pune properties, luxury homes, completed projects, property photos, Hestia Abodes gallery')
@section('canonical_url', 'https://hestiaabodes.in/gallery')

@section('body')

    <!-- Gallery Hero Section -->
    @include('partials.page-header', [
        'sectionClass' => 'gallery-hero-section',
        'fallbackTitle' => 'Our Premium Properties',
        'fallbackSubtitle' => 'Discover the beauty and elegance of our carefully curated properties and completed projects'
    ])

    <!-- Gallery Search Section -->
    <section class="gallery-search-section py-4">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <form method="GET" action="{{ route('gallery') }}" class="gallery-search-form">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control"
                                   placeholder="Search gallery items..."
                                   value="{{ request('search') }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Filter Section -->
    <section class="gallery-filter-section">
        <div class="container">
            <div class="filter-tabs text-center">
                <button class="filter-btn active" data-filter="all">All Projects</button>
                @foreach($categories as $category)
                    <button class="filter-btn" data-filter="{{ $category->slug }}">{{ $category->name }}</button>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Gallery Grid Section -->
    <section class="gallery-grid-section">
        <div class="container">
            <div class="gallery-grid">
                @forelse($galleries as $gallery)
                    <div class="gallery-item animate-in" data-category="{{ $gallery->projectCategory->slug ?? 'all' }}">
                        <div class="gallery-card">
                            <div class="gallery-image">
                                <img src="{{ asset('storage/' . $gallery->image) }}" alt="{{ $gallery->title }}" class="img-fluid">
                                <div class="gallery-overlay">
                                    <div class="gallery-content">
                                        <h4>{{ $gallery->title }}</h4>
                                        <p>{{ $gallery->description ?? $gallery->projectCategory->name ?? 'Premium Property' }}</p>
                                        <div class="gallery-actions">
                                            <a href="#" class="gallery-btn" data-bs-toggle="modal" data-bs-target="#galleryModal"
                                               data-image="{{ asset('storage/' . $gallery->image) }}"
                                               data-title="{{ $gallery->title }}">
                                                <i class="fas fa-expand"></i>
                                            </a>
                                            @if($gallery->projectCategory)
                                                <a href="{{ route('projects') }}?category={{ $gallery->projectCategory->slug }}" class="gallery-btn">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center py-5">
                        <div class="no-gallery-items">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Gallery Items Found</h4>
                            <p class="text-muted">Gallery items will appear here once they are added by the administrator.</p>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($galleries->hasPages())
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="pagination-wrapper text-center">
                            {{ $galleries->links() }}
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Gallery Stats Section -->
    <section class="gallery-stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card text-center animate-in">
                        <div class="stat-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="stat-number" data-target="500">500</div>
                        <div class="stat-label">Properties Delivered</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card text-center animate-in">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-number" data-target="50">50</div>
                        <div class="stat-label">Projects Completed</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card text-center animate-in">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number" data-target="1000">1000</div>
                        <div class="stat-label">Happy Families</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card text-center animate-in">
                        <div class="stat-icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <div class="stat-number" data-target="21">21</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Modal -->
    <div class="modal fade" id="galleryModal" tabindex="-1" aria-labelledby="galleryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="galleryModalLabel">Property Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="" alt="" class="img-fluid" id="modalImage">
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <section class="gallery-cta-section">
        <div class="container">
            <div class="cta-content text-center animate-in">
                <h2>Ready to See These Properties?</h2>
                <p>Schedule a visit to experience the quality and luxury of our properties firsthand</p>
                <div class="cta-buttons">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        Schedule Visit
                    </a>
                    <a href="{{ route('projects') }}" class="btn btn-outline-light btn-lg">
                        View All Projects
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Gallery Modal Image Handler
        document.addEventListener('DOMContentLoaded', function() {
            const galleryModal = document.getElementById('galleryModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('galleryModalLabel');

            if (galleryModal) {
                galleryModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const imageUrl = button.getAttribute('data-image');
                    const imageTitle = button.getAttribute('data-title');

                    if (modalImage && imageUrl) {
                        modalImage.src = imageUrl;
                        modalImage.alt = imageTitle || 'Gallery Image';
                    }

                    if (modalTitle && imageTitle) {
                        modalTitle.textContent = imageTitle;
                    }
                });

                // Clear image when modal is hidden to prevent flashing
                galleryModal.addEventListener('hidden.bs.modal', function() {
                    if (modalImage) {
                        modalImage.src = '';
                        modalImage.alt = '';
                    }
                });
            }
        });
    </script>

@endsection