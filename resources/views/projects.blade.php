@extends('layouts.app')
@section('body')
<?php 
// SEO Meta Tags for Featured Projects Page
$page_title = "Featured Projects - Premium Properties in Pune | Hestia Abodes";
$page_description = "Explore our handpicked featured projects in Pune. Premium residential and commercial properties in Baner, Wakad, Hinjewadi with verified developers and best prices.";
$page_keywords = "featured projects Pune, premium properties, residential projects, commercial properties, Baner projects, Wakad properties, Hinjewadi developments";
$canonical_url = "https://hestiaabodes.in/projects";

?>

    <!-- Featured Projects Hero Section -->
    @include('partials.page-header', [
        'sectionClass' => 'featured-projects-hero-section',
        'fallbackTitle' => 'Handpicked Premium Properties',
        'fallbackSubtitle' => 'Discover our carefully curated selection of the finest residential and commercial projects in Pune'
    ])

    <!-- Search & Filter Section -->
    <section class="projects-filter-section">
        <div class="container">
            <!-- Search Bar -->
            <div class="search-section mb-4">
                <form method="GET" action="{{ route('projects') }}" id="searchForm">
                    <div class="row align-items-center">
                        <div class="col-lg-6 col-md-8 mb-3">
                            <div class="search-input-wrapper">
                                <input type="text"
                                       class="form-control search-input"
                                       name="search"
                                       value="{{ request('search') }}"
                                       placeholder="Search projects by name, location, developer..."
                                       id="searchInput">
                                <i class="fas fa-search search-icon"></i>
                                @if(request('search'))
                                    <button type="button" class="clear-search" onclick="clearSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                @endif
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 mb-3">
                            <button type="submit" class="btn btn-primary search-btn w-100">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-12 mb-3">
                            <div class="results-info">
                                <span class="results-count">{{ $projects->total() }} Projects Found</span>
                                @if(request()->hasAny(['search', 'category', 'location', 'type', 'status', 'price_range', 'configuration']))
                                    <a href="{{ route('projects') }}" class="clear-filters">
                                        <i class="fas fa-times me-1"></i>Clear All Filters
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Filters -->
                    <div class="advanced-filters">
                        <div class="row">
                            <!-- Category Filter -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <select class="form-select filter-select" name="category" onchange="this.form.submit()">
                                    <option value="all">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->slug }}"
                                                {{ request('category') == $category->slug ? 'selected' : '' }}>
                                            {{ $category->name }} ({{ $categoryCounts[$category->slug] ?? 0 }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Location Filter -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <select class="form-select filter-select" name="location" onchange="this.form.submit()">
                                    <option value="all">All Locations</option>
                                    @foreach($locations as $location)
                                        <option value="{{ $location }}"
                                                {{ request('location') == $location ? 'selected' : '' }}>
                                            {{ $location }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Type Filter -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <select class="form-select filter-select" name="type" onchange="this.form.submit()">
                                    <option value="all">All Types</option>
                                    @foreach($types as $type)
                                        <option value="{{ $type }}"
                                                {{ request('type') == $type ? 'selected' : '' }}>
                                            {{ $type }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <select class="form-select filter-select" name="status" onchange="this.form.submit()">
                                    <option value="all">All Status</option>
                                    @foreach($statuses as $status)
                                        <option value="{{ $status }}"
                                                {{ request('status') == $status ? 'selected' : '' }}>
                                            {{ $status }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Price Range Filter -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <select class="form-select filter-select" name="price_range" onchange="this.form.submit()">
                                    <option value="all">All Prices</option>
                                    <option value="under-50" {{ request('price_range') == 'under-50' ? 'selected' : '' }}>Under ₹50 Lakhs</option>
                                    <option value="50-100" {{ request('price_range') == '50-100' ? 'selected' : '' }}>₹50L - ₹1Cr</option>
                                    <option value="100-200" {{ request('price_range') == '100-200' ? 'selected' : '' }}>₹1Cr - ₹2Cr</option>
                                    <option value="above-200" {{ request('price_range') == 'above-200' ? 'selected' : '' }}>Above ₹2Cr</option>
                                </select>
                            </div>

                            <!-- Configuration Filter -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <select class="form-select filter-select" name="configuration" onchange="this.form.submit()">
                                    <option value="all">All BHK</option>
                                    @foreach($configurations as $config)
                                        <option value="{{ $config }}"
                                                {{ request('configuration') == $config ? 'selected' : '' }}>
                                            {{ $config }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Sort Options -->
                        <div class="row align-items-center">
                            <div class="col-lg-6 col-md-8">
                                <div class="sort-options">
                                    <label class="sort-label">Sort by:</label>
                                    <select class="form-select sort-select" name="sort_by" onchange="this.form.submit()">
                                        <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest First</option>
                                        <option value="featured" {{ request('sort_by') == 'featured' ? 'selected' : '' }}>Featured First</option>
                                        <option value="price_low" {{ request('sort_by') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                        <option value="price_high" {{ request('sort_by') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                        <option value="name_asc" {{ request('sort_by') == 'name_asc' ? 'selected' : '' }}>Name: A to Z</option>
                                        <option value="name_desc" {{ request('sort_by') == 'name_desc' ? 'selected' : '' }}>Name: Z to A</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-4">
                                <div class="view-toggle">
                                    <button type="button" class="view-btn active" data-view="grid" title="Grid View">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button type="button" class="view-btn" data-view="list" title="List View">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Active Filters Display -->
            @if(request()->hasAny(['search', 'category', 'location', 'type', 'status', 'price_range', 'configuration']))
                <div class="active-filters mb-4">
                    <div class="d-flex flex-wrap align-items-center">
                        <span class="filter-label me-3">Active Filters:</span>

                        @if(request('search'))
                            <span class="filter-tag">
                                Search: "{{ request('search') }}"
                                <a href="{{ request()->fullUrlWithQuery(['search' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif

                        @if(request('category') && request('category') !== 'all')
                            <span class="filter-tag">
                                Category: {{ $categories->where('slug', request('category'))->first()->name ?? request('category') }}
                                <a href="{{ request()->fullUrlWithQuery(['category' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif

                        @if(request('location') && request('location') !== 'all')
                            <span class="filter-tag">
                                Location: {{ request('location') }}
                                <a href="{{ request()->fullUrlWithQuery(['location' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif

                        @if(request('type') && request('type') !== 'all')
                            <span class="filter-tag">
                                Type: {{ request('type') }}
                                <a href="{{ request()->fullUrlWithQuery(['type' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif

                        @if(request('status') && request('status') !== 'all')
                            <span class="filter-tag">
                                Status: {{ request('status') }}
                                <a href="{{ request()->fullUrlWithQuery(['status' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif

                        @if(request('price_range') && request('price_range') !== 'all')
                            <span class="filter-tag">
                                Price: {{ request('price_range') }}
                                <a href="{{ request()->fullUrlWithQuery(['price_range' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif

                        @if(request('configuration') && request('configuration') !== 'all')
                            <span class="filter-tag">
                                BHK: {{ request('configuration') }}
                                <a href="{{ request()->fullUrlWithQuery(['configuration' => null]) }}" class="remove-filter">×</a>
                            </span>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="featured-projects-grid-section">
        <div class="container">
            @if($projects->count() > 0)
                <div class="projects-grid" id="projectsGrid">
                    <div class="row" id="projectsRow">
                        @foreach($projects as $project)
                        <div class="col-lg-4 col-md-6 mb-4 project-item"
                             data-category="{{ $project->projectCategory->slug }}"
                             data-location="{{ strtolower(str_replace(' ', '-', $project->location)) }}"
                             data-type="{{ strtolower(str_replace(' ', '-', $project->type)) }}"
                             data-status="{{ strtolower(str_replace(' ', '-', $project->status)) }}"
                             data-price="{{ $project->price_range }}"
                             data-title="{{ strtolower($project->title) }}">
                            <div class="project-card">
                                <div class="project-image">
                                    @if($project->first_image)
                                    <img src="{{ asset('storage/' . $project->first_image) }}" alt="{{ $project->title }}" class="img-fluid">
                                    @else
                                    <div class="no-image-placeholder">
                                        <i class="fas fa-building fa-3x text-muted"></i>
                                        <p class="text-muted mt-2">No Image Available</p>
                                    </div>
                                    @endif

                                    <!-- Badges Container -->
                                    <div class="project-badges-container">
                                        <div class="project-badges-left">
                                            @if($project->featured)
                                                <div class="featured-badge">
                                                    <i class="fas fa-star"></i> Featured
                                                </div>
                                            @endif
                                        </div>
                                        <div class="project-badges-right">
                                            <div class="project-badge">{{ $project->status }}</div>
                                        </div>
                                    </div>

                                    <!-- Possession Status -->
                                    <div class="project-status">{{ $project->possession }}</div>
                                </div>
                                
                                <div class="project-content">
                                     <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-primary">{{ $project->projectCategory->name }}</span>
                                        <small class="text-muted">{{ $project->type }}</small>
                                    </div>
                                    <div class="project-header">
                                        <h3 class="project-title">{{ $project->title }}</h3>
                                        <div class="project-location">
                                            <i class="fas fa-map-marker-alt"></i>
                                            {{ $project->location }}
                                        </div>
                                    </div>
                                    
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label"> By :</span>
                                                <span class="detail-value">{{ $project->developer }}</span>
                                            </div>
                                            
                                        
                                        <div class="detail-item">
                                            <span class="detail-label">Area:</span>
                                            <span class="detail-value">{{ $project->area_range }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Price:</span>
                                            <span class="detail-value">{{ $project->price_range }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="project-amenities">
                                        @foreach(array_slice($project->configurations, 0, 3) as $config)
                                                <span class="amenity-tag">{{ $config }}</span>
                                        @endforeach
                                       
                                    </div>
                                    <p class="card-text">
                                        {{ Str::limit($project->description, 100) }}
                                    </p>
                                    <div class="project-actions">
                                        <a href="{{ route('project-details', $project->slug) }}" class="btn btn-primary">View Details</a>
                                        <button type="button" class="btn btn-outline-primary"
                                                data-bs-toggle="modal"
                                                data-bs-target="#enquiryModal"
                                                data-project-id="{{ $project->id }}"
                                                data-project-name="{{ $project->title }}">
                                            Enquiry Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-wrapper mt-5">
                    <div class="d-flex justify-content-center">
                        <nav aria-label="Projects pagination">
                            {{ $projects->links('pagination::bootstrap-4') }}
                        </nav>
                    </div>
                </div>
             @else
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h3 class="text-muted">No Projects Found</h3>
                <p class="text-muted">We're working on adding new projects. Please check back soon!</p>
            </div>
            @endif
        </div>
    </section>

    <!-- Why Choose Our Featured Projects -->
    <section class="why-featured-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>Why Our Featured Projects?</h2>
                <p>Every project in our portfolio is carefully vetted and verified</p>
            </div>
            
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <h4>RERA Verified</h4>
                        <p>All projects are RERA registered and compliant</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h4>Trusted Developers</h4>
                        <p>Partnerships with reputed and reliable developers</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <h4>Prime Locations</h4>
                        <p>Strategic locations with excellent connectivity</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>Best Value</h4>
                        <p>Competitive pricing with maximum value</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="projects-cta-section">
        <div class="container">
            <div class="cta-content text-center">
                <h2>Find Your Perfect Property</h2>
                <p>Let our experts help you choose the right project from our featured collection</p>
                <div class="cta-buttons">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        Schedule Site Visit
                    </a>
                    <a href="tel:+919067881848" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone"></i> Call Expert
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Enquiry Modal -->
    <div class="modal fade" id="enquiryModal" tabindex="-1" aria-labelledby="enquiryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content enquiry-modal-content">
                <div class="modal-header enquiry-modal-header">
                    <div class="modal-title-wrapper">
                        <div class="modal-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div>
                            <h5 class="modal-title" id="enquiryModalLabel">Project Enquiry</h5>
                            <p class="modal-subtitle">Get in touch with us for more details</p>
                        </div>
                    </div>
                    <button type="button" class="btn-close enquiry-btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body enquiry-modal-body">
                    <div class="enquiry-form-container">
                        <form id="enquiryForm" action="{{ route('project-enquiry.store') }}" method="POST" class="enquiry-form">
                            @csrf
                            <input type="hidden" name="project_id" id="modal_project_id">
                            <input type="hidden" name="project_name" id="modal_project_name">

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" name="name" id="enquiry_name" class="form-control enquiry-input"
                                               placeholder="Your Name" required>
                                        <label for="enquiry_name">
                                            <i class="fas fa-user me-2"></i>Your Name <span class="text-danger">*</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" name="email" id="enquiry_email" class="form-control enquiry-input"
                                               placeholder="Email Address" required>
                                        <label for="enquiry_email">
                                            <i class="fas fa-envelope me-2"></i>Email Address <span class="text-danger">*</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" name="phone" id="enquiry_phone" class="form-control enquiry-input"
                                               placeholder="Phone Number" required>
                                        <label for="enquiry_phone">
                                            <i class="fas fa-phone me-2"></i>Phone Number <span class="text-danger">*</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select name="interest" id="enquiry_interest" class="form-select enquiry-select" required>
                                            <option value="">Select your interest</option>
                                            <option value="site_visit">🏗️ Site Visit</option>
                                            <option value="price_details">💰 Price Details</option>
                                            <option value="floor_plans">📋 Floor Plans</option>
                                            <option value="loan_assistance">🏦 Loan Assistance</option>
                                        </select>
                                        <label for="enquiry_interest">
                                            <i class="fas fa-heart me-2"></i>I'm Interested In <span class="text-danger">*</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3">
                                <div class="form-floating">
                                    <textarea name="message" id="enquiry_message" class="form-control enquiry-textarea"
                                              placeholder="Any specific requirements or questions..." rows="4"></textarea>
                                    <label for="enquiry_message">
                                        <i class="fas fa-comment me-2"></i>Message (Optional)
                                    </label>
                                </div>
                            </div>


                        </form>
                    </div>
                </div>
                <div class="modal-footer enquiry-modal-footer">
                    <button type="button" class="btn btn-outline-secondary enquiry-btn-cancel" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" form="enquiryForm" class="btn btn-primary enquiry-btn-submit">
                        <i class="fas fa-paper-plane me-2"></i>Send Enquiry
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Handle enquiry modal
        document.addEventListener('DOMContentLoaded', function() {
            const enquiryModal = document.getElementById('enquiryModal');
            const enquiryForm = document.getElementById('enquiryForm');
            const submitBtn = document.querySelector('.enquiry-btn-submit');

            if (enquiryModal) {
                // Show modal event
                enquiryModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const projectId = button.getAttribute('data-project-id');
                    const projectName = button.getAttribute('data-project-name');

                    // Update modal title and hidden fields
                    document.getElementById('enquiryModalLabel').textContent = `Enquiry for ${projectName}`;
                    document.getElementById('modal_project_id').value = projectId;
                    document.getElementById('modal_project_name').value = projectName;

                    // Add entrance animation
                    setTimeout(() => {
                        enquiryModal.querySelector('.modal-content').style.transform = 'scale(1)';
                        enquiryModal.querySelector('.modal-content').style.opacity = '1';
                    }, 100);
                });

                // Clear form when modal is hidden
                enquiryModal.addEventListener('hidden.bs.modal', function() {
                    enquiryForm.reset();
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;

                    // Remove any validation classes
                    const inputs = enquiryForm.querySelectorAll('.form-control, .form-select');
                    inputs.forEach(input => {
                        input.classList.remove('is-valid', 'is-invalid');
                    });
                });
            }

            // Form submission handling
            if (enquiryForm) {
                enquiryForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Validate form
                    if (!validateEnquiryForm()) {
                        return;
                    }

                    // Show loading state
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;

                    // Submit form
                    const formData = new FormData(enquiryForm);

                    fetch(enquiryForm.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            showSuccessMessage('Thank you! Your enquiry has been submitted successfully. We will contact you soon.');

                            // Close modal after delay
                            setTimeout(() => {
                                bootstrap.Modal.getInstance(enquiryModal).hide();
                            }, 2000);
                        } else {
                            throw new Error(data.message || 'Something went wrong');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showErrorMessage('Sorry, there was an error submitting your enquiry. Please try again.');
                    })
                    .finally(() => {
                        submitBtn.classList.remove('loading');
                        submitBtn.disabled = false;
                    });
                });
            }

            // Form validation function
            function validateEnquiryForm() {
                let isValid = true;
                const requiredFields = ['enquiry_name', 'enquiry_email', 'enquiry_phone', 'enquiry_interest'];

                // Validate required fields
                requiredFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    const value = field.value.trim();

                    if (!value) {
                        field.classList.add('is-invalid');
                        field.classList.remove('is-valid');
                        isValid = false;
                    } else {
                        field.classList.add('is-valid');
                        field.classList.remove('is-invalid');
                    }
                });

                // Validate email format
                const emailField = document.getElementById('enquiry_email');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (emailField.value && !emailRegex.test(emailField.value)) {
                    emailField.classList.add('is-invalid');
                    emailField.classList.remove('is-valid');
                    isValid = false;
                }

                // Validate phone format
                const phoneField = document.getElementById('enquiry_phone');
                const phoneRegex = /^[0-9]{10}$/;
                if (phoneField.value && !phoneRegex.test(phoneField.value.replace(/\D/g, ''))) {
                    phoneField.classList.add('is-invalid');
                    phoneField.classList.remove('is-valid');
                    isValid = false;
                }

                return isValid;
            }

            // Success message function
            function showSuccessMessage(message) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }

            // Error message function
            function showErrorMessage(message) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        });
    </script>

@endsection

@push('styles')
<style>
/* Custom Pagination Styling */
.pagination-wrapper {
    background: #f8f9fa;
    padding: 30px 20px;
    border-radius: 15px;
    margin: 40px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: #6c757d;
    background-color: #fff;
    border: 1px solid #dee2e6;
    padding: 12px 16px;
    margin: 0 3px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.pagination .page-link:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.pagination .page-item.active .page-link {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
    border-radius: 8px;
}

/* Responsive pagination */
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 8px 12px;
        font-size: 14px;
        margin: 0 1px;
    }

    .pagination-wrapper {
        padding: 20px 15px;
        margin: 30px 0;
    }
}
</style>

<!-- Projects Filter CSS -->
<link rel="stylesheet" href="{{ asset('css/projects-filter.css') }}">
@endpush

@push('scripts')
<!-- Projects Filter JavaScript -->
<script src="{{ asset('js/projects-filter.js') }}"></script>
@endpush