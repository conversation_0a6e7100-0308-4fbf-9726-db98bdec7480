<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Gallery extends Model
{
    protected $fillable = [
        'title',
        'project_category_id',
        'image',
        'description',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // Relationship with ProjectCategory
    public function projectCategory()
    {
        return $this->belongsTo(ProjectCategory::class);
    }
}
