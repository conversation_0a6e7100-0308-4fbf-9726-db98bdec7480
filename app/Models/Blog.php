<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Blog extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'gallery_images',
        'blog_category_id',
        'tags',
        'author',
        'read_time',
        'views',
        'featured',
        'is_published',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords'
    ];

    protected $casts = [
        'gallery_images' => 'array',
        'tags' => 'array',
        'featured' => 'boolean',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
        'views' => 'integer',
        'read_time' => 'integer'
    ];

    // Automatically generate slug and set published_at
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($blog) {
            if (empty($blog->slug)) {
                $blog->slug = Str::slug($blog->title);
            }
            if ($blog->is_published && !$blog->published_at) {
                $blog->published_at = now();
            }
        });

        static::updating(function ($blog) {
            if ($blog->isDirty('title') && empty($blog->slug)) {
                $blog->slug = Str::slug($blog->title);
            }
            if ($blog->isDirty('is_published') && $blog->is_published && !$blog->published_at) {
                $blog->published_at = now();
            }
        });
    }

    // Relationships
    public function blogCategory()
    {
        return $this->belongsTo(BlogCategory::class);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true)->whereNotNull('published_at');
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeByCategory($query, $categorySlug)
    {
        return $query->whereHas('blogCategory', function ($q) use ($categorySlug) {
            $q->where('slug', $categorySlug);
        });
    }

    // Accessors
    public function getFirstImageAttribute()
    {
        if ($this->featured_image) {
            return $this->featured_image;
        }

        if ($this->gallery_images && count($this->gallery_images) > 0) {
            return $this->gallery_images[0];
        }

        return null;
    }

    public function getFormattedPublishedAtAttribute()
    {
        return $this->published_at ? $this->published_at->format('M d, Y') : null;
    }

    public function getReadTimeTextAttribute()
    {
        return $this->read_time . ' min read';
    }

    public function getExcerptLimitedAttribute()
    {
        return Str::limit($this->excerpt, 150);
    }

    // Mutators
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        if (empty($this->attributes['slug'])) {
            $this->attributes['slug'] = Str::slug($value);
        }
    }

    // Helper methods
    public function incrementViews()
    {
        $this->increment('views');
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
}
