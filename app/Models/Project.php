<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Project extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'project_category_id',
        'location',
        'type',
        'status',
        'price_range',
        'configurations',
        'area_range',
        'description',
        'amenities',
        'images',
        'developer',
        'rera_number',
        'possession',
        'featured',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'is_active',
    ];

    protected $casts = [
        'configurations' => 'array',
        'amenities' => 'array',
        'images' => 'array',
        'featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationship with ProjectCategory
    public function projectCategory()
    {
        return $this->belongsTo(ProjectCategory::class);
    }

    // Generate slug from title
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = Str::slug($value);
    }

    // Scope for active projects
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope for featured projects
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    // Scope for search functionality
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('location', 'like', "%{$search}%")
              ->orWhere('developer', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    // Scope for filtering by category
    public function scopeByCategory($query, $categorySlug)
    {
        return $query->whereHas('projectCategory', function($q) use ($categorySlug) {
            $q->where('slug', $categorySlug);
        });
    }

    // Scope for filtering by location
    public function scopeByLocation($query, $location)
    {
        return $query->where('location', 'like', "%{$location}%");
    }

    // Scope for filtering by type
    public function scopeByType($query, $type)
    {
        return $query->where('type', 'like', "%{$type}%");
    }

    // Scope for filtering by status
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope for filtering by configuration
    public function scopeByConfiguration($query, $configuration)
    {
        return $query->whereJsonContains('configurations', $configuration);
    }

    // Get first image
    public function getFirstImageAttribute()
    {
        return $this->images && count($this->images) > 0 ? $this->images[0] : null;
    }

    // Get configurations as string
    public function getConfigurationsStringAttribute()
    {
        return $this->configurations ? implode(', ', $this->configurations) : '';
    }

    // Get amenities as string
    public function getAmenitiesStringAttribute()
    {
        return $this->amenities ? implode(', ', $this->amenities) : '';
    }
}
