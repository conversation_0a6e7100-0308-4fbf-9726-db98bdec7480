<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ContactSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'mobile',
        'property_type',
        'budget_range',
        'location',
        'message',
        'status',
        'admin_notes',
        'contacted_at',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'contacted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Status constants
    const STATUS_NEW = 'new';
    const STATUS_CONTACTED = 'contacted';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_CLOSED = 'closed';

    // Scopes
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    public function scopeContacted($query)
    {
        return $query->where('status', self::STATUS_CONTACTED);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    public function scopeClosed($query)
    {
        return $query->where('status', self::STATUS_CLOSED);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            self::STATUS_NEW => '<span class="badge bg-primary">New</span>',
            self::STATUS_CONTACTED => '<span class="badge bg-info">Contacted</span>',
            self::STATUS_IN_PROGRESS => '<span class="badge bg-warning">In Progress</span>',
            self::STATUS_CLOSED => '<span class="badge bg-success">Closed</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    public function getPropertyTypeDisplayAttribute()
    {
        $types = [
            'residential' => 'Residential',
            'commercial' => 'Commercial',
            'investment' => 'Investment Property',
            'rental' => 'Rental Property'
        ];

        return $types[$this->property_type] ?? ucfirst($this->property_type);
    }

    public function getBudgetRangeDisplayAttribute()
    {
        $ranges = [
            'under-50' => 'Under ₹50 Lakhs',
            '50-100' => '₹50 Lakhs - ₹1 Crore',
            '100-200' => '₹1 Crore - ₹2 Crores',
            '200-500' => '₹2 Crores - ₹5 Crores',
            'above-500' => 'Above ₹5 Crores'
        ];

        return $ranges[$this->budget_range] ?? ucfirst(str_replace('-', ' ', $this->budget_range));
    }

    public function getLocationDisplayAttribute()
    {
        $locations = [
            'baner' => 'Baner',
            'wakad' => 'Wakad',
            'hinjewadi' => 'Hinjewadi',
            'kharadi' => 'Kharadi',
            'aundh' => 'Aundh',
            'koregaon-park' => 'Koregaon Park',
            'viman-nagar' => 'Viman Nagar',
            'hadapsar' => 'Hadapsar',
            'pune-city' => 'Pune City',
            'other' => 'Other Location'
        ];

        return $locations[$this->location] ?? ucfirst(str_replace('-', ' ', $this->location));
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    // Static methods
    public static function getStatusOptions()
    {
        return [
            self::STATUS_NEW => 'New',
            self::STATUS_CONTACTED => 'Contacted',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_CLOSED => 'Closed'
        ];
    }

    public static function getPropertyTypeOptions()
    {
        return [
            'residential' => 'Residential',
            'commercial' => 'Commercial',
            'investment' => 'Investment Property',
            'rental' => 'Rental Property'
        ];
    }

    public static function getBudgetRangeOptions()
    {
        return [
            'under-50' => 'Under ₹50 Lakhs',
            '50-100' => '₹50 Lakhs - ₹1 Crore',
            '100-200' => '₹1 Crore - ₹2 Crores',
            '200-500' => '₹2 Crores - ₹5 Crores',
            'above-500' => 'Above ₹5 Crores'
        ];
    }

    public static function getLocationOptions()
    {
        return [
            'baner' => 'Baner',
            'wakad' => 'Wakad',
            'hinjewadi' => 'Hinjewadi',
            'kharadi' => 'Kharadi',
            'aundh' => 'Aundh',
            'koregaon-park' => 'Koregaon Park',
            'viman-nagar' => 'Viman Nagar',
            'hadapsar' => 'Hadapsar',
            'pune-city' => 'Pune City',
            'other' => 'Other Location'
        ];
    }
}
