<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ProjectEnquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'project_name',
        'name',
        'email',
        'phone',
        'interest',
        'message',
        'status',
        'admin_notes',
        'contacted_at'
    ];

    protected $casts = [
        'contacted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    // Scopes
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    public function scopeContacted($query)
    {
        return $query->where('status', 'contacted');
    }

    public function scopeInterested($query)
    {
        return $query->where('status', 'interested');
    }

    public function scopeConverted($query)
    {
        return $query->where('status', 'converted');
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getInterestDisplayAttribute()
    {
        $interests = [
            'site_visit' => 'Site Visit',
            'price_details' => 'Price Details',
            'floor_plans' => 'Floor Plans',
            'loan_assistance' => 'Loan Assistance'
        ];

        return $interests[$this->interest] ?? $this->interest;
    }

    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'new' => 'New',
            'contacted' => 'Contacted',
            'interested' => 'Interested',
            'not_interested' => 'Not Interested',
            'converted' => 'Converted'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getStatusBadgeClassAttribute()
    {
        $classes = [
            'new' => 'badge-primary',
            'contacted' => 'badge-info',
            'interested' => 'badge-warning',
            'not_interested' => 'badge-secondary',
            'converted' => 'badge-success'
        ];

        return $classes[$this->status] ?? 'badge-secondary';
    }

    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y h:i A');
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }
}
