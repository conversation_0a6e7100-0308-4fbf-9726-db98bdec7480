<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CareerApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'position_applied',
        'years_of_experience',
        'current_location',
        'preferred_location',
        'current_salary',
        'expected_salary',
        'notice_period',
        'resume_path',
        'cover_letter',
        'additional_info',
        'status',
        'admin_notes',
        'reviewed_at',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'current_salary' => 'decimal:2',
        'expected_salary' => 'decimal:2',
        'years_of_experience' => 'integer'
    ];

    // Status constants
    const STATUS_NEW = 'new';
    const STATUS_REVIEWED = 'reviewed';
    const STATUS_SHORTLISTED = 'shortlisted';
    const STATUS_INTERVIEWED = 'interviewed';
    const STATUS_SELECTED = 'selected';
    const STATUS_REJECTED = 'rejected';

    // Scopes
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    public function scopeReviewed($query)
    {
        return $query->where('status', self::STATUS_REVIEWED);
    }

    public function scopeShortlisted($query)
    {
        return $query->where('status', self::STATUS_SHORTLISTED);
    }

    public function scopeInterviewed($query)
    {
        return $query->where('status', self::STATUS_INTERVIEWED);
    }

    public function scopeSelected($query)
    {
        return $query->where('status', self::STATUS_SELECTED);
    }

    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            self::STATUS_NEW => '<span class="badge bg-primary">New</span>',
            self::STATUS_REVIEWED => '<span class="badge bg-info">Reviewed</span>',
            self::STATUS_SHORTLISTED => '<span class="badge bg-warning">Shortlisted</span>',
            self::STATUS_INTERVIEWED => '<span class="badge bg-secondary">Interviewed</span>',
            self::STATUS_SELECTED => '<span class="badge bg-success">Selected</span>',
            self::STATUS_REJECTED => '<span class="badge bg-danger">Rejected</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    public function getExperienceDisplayAttribute()
    {
        // For simplified form, we don't have years_of_experience field
        // Return a default message or handle gracefully
        if (isset($this->years_of_experience)) {
            if ($this->years_of_experience == 0) {
                return 'Fresher';
            } elseif ($this->years_of_experience == 1) {
                return '1 Year';
            } else {
                return $this->years_of_experience . ' Years';
            }
        }
        return 'Not specified';
    }

    public function getCurrentSalaryDisplayAttribute()
    {
        if (!$this->current_salary) {
            return 'Not specified';
        }
        return '₹' . number_format($this->current_salary, 0) . ' LPA';
    }

    public function getExpectedSalaryDisplayAttribute()
    {
        if (!$this->expected_salary) {
            return 'Not specified';
        }
        return '₹' . number_format($this->expected_salary, 0) . ' LPA';
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    public function getResumeUrlAttribute()
    {
        if ($this->resume_path) {
            return asset('storage/' . $this->resume_path);
        }
        return null;
    }

    // Static methods
    public static function getStatusOptions()
    {
        return [
            self::STATUS_NEW => 'New',
            self::STATUS_REVIEWED => 'Reviewed',
            self::STATUS_SHORTLISTED => 'Shortlisted',
            self::STATUS_INTERVIEWED => 'Interviewed',
            self::STATUS_SELECTED => 'Selected',
            self::STATUS_REJECTED => 'Rejected'
        ];
    }

    public static function getPositionOptions()
    {
        return [
            'sales-executive' => 'Sales Executive',
            'sales-manager' => 'Sales Manager',
            'marketing-executive' => 'Marketing Executive',
            'marketing-manager' => 'Marketing Manager',
            'business-development' => 'Business Development Executive',
            'customer-relationship' => 'Customer Relationship Manager',
            'project-coordinator' => 'Project Coordinator',
            'admin-executive' => 'Admin Executive',
            'hr-executive' => 'HR Executive',
            'accounts-executive' => 'Accounts Executive',
            'other' => 'Other Position'
        ];
    }

    public static function getNoticePeriodOptions()
    {
        return [
            'immediate' => 'Immediate',
            '15-days' => '15 Days',
            '1-month' => '1 Month',
            '2-months' => '2 Months',
            '3-months' => '3 Months',
            'more-than-3-months' => 'More than 3 Months'
        ];
    }
}
