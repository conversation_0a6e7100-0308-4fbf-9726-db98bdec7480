<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Project;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share featured projects with all views for footer
        View::composer('*', function ($view) {
            $featuredProjects = Project::where('featured', true)
                ->where('is_active', true)
                ->with('projectCategory')
                ->orderBy('created_at', 'desc')
                ->take(3)
                ->get();

            $view->with('footerFeaturedProjects', $featuredProjects);
        });
    }
}
