<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Project;
use App\Models\Blog;
use Carbon\Carbon;

class SitemapController extends Controller
{
    public function index()
    {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Static pages
        $staticPages = [
            [
                'url' => route('home'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'daily',
                'priority' => '1.0'
            ],
            [
                'url' => route('about'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.8'
            ],
            [
                'url' => route('services'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.8'
            ],
            [
                'url' => route('projects'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'weekly',
                'priority' => '0.9'
            ],
            [
                'url' => route('why-choose-us'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.7'
            ],
            [
                'url' => route('how-we-work'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.7'
            ],
            [
                'url' => route('gallery'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'weekly',
                'priority' => '0.6'
            ],
            [
                'url' => route('blog'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'daily',
                'priority' => '0.8'
            ],
            [
                'url' => route('contact'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.7'
            ],
            [
                'url' => route('privacy-policy'),
                'lastmod' => Carbon::now()->format('Y-m-d'),
                'changefreq' => 'yearly',
                'priority' => '0.3'
            ]
        ];

        // Add static pages to sitemap
        foreach ($staticPages as $page) {
            $sitemap .= $this->generateUrlEntry($page);
        }

        // Add dynamic project pages
        $projects = Project::where('is_active', true)->get();
        foreach ($projects as $project) {
            $sitemap .= $this->generateUrlEntry([
                'url' => route('project-details', $project->slug),
                'lastmod' => $project->updated_at->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.8'
            ]);
        }

        // Add dynamic blog pages
        $blogs = Blog::published()->get();
        foreach ($blogs as $blog) {
            $sitemap .= $this->generateUrlEntry([
                'url' => route('blog-details', $blog->slug),
                'lastmod' => $blog->updated_at->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.7'
            ]);
        }

        $sitemap .= '</urlset>';

        return response($sitemap, 200)
            ->header('Content-Type', 'application/xml');
    }

    private function generateUrlEntry($data)
    {
        $entry = "  <url>\n";
        $entry .= "    <loc>" . htmlspecialchars($data['url']) . "</loc>\n";
        $entry .= "    <lastmod>" . $data['lastmod'] . "</lastmod>\n";
        $entry .= "    <changefreq>" . $data['changefreq'] . "</changefreq>\n";
        $entry .= "    <priority>" . $data['priority'] . "</priority>\n";
        $entry .= "  </url>\n";
        
        return $entry;
    }

    public function humanReadable()
    {
        $staticPages = [
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'About Us', 'url' => route('about')],
            ['name' => 'Our Services', 'url' => route('services')],
            ['name' => 'Projects', 'url' => route('projects')],
            ['name' => 'Why Choose Us', 'url' => route('why-choose-us')],
            ['name' => 'How We Work', 'url' => route('how-we-work')],
            ['name' => 'Gallery', 'url' => route('gallery')],
            ['name' => 'Blog', 'url' => route('blog')],
            ['name' => 'Contact', 'url' => route('contact')],
            ['name' => 'Privacy Policy', 'url' => route('privacy-policy')]
        ];

        $projects = Project::where('is_active', true)->get();
        $blogs = Blog::published()->get();

        return view('sitemap', compact('staticPages', 'projects', 'blogs'));
    }
}
