<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CareerApplication;
use App\Models\PageHeader;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use App\Mail\CareerApplicationNotification;

class CareerController extends Controller
{
    public function index()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'careers')->first();

        return view('careers', compact('pageHeader'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'mobile' => 'required|string|max:15|regex:/^[0-9]{10}$/',
            'position' => 'required|string|max:255',
            'resume' => 'required|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'message' => 'nullable|string|max:500'
        ], [
            'name.required' => 'Please enter your full name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'mobile.required' => 'Please enter your mobile number.',
            'mobile.regex' => 'Please enter a valid 10-digit mobile number.',
            'position.required' => 'Please select the position you are applying for.',
            'resume.required' => 'Please upload your resume.',
            'resume.file' => 'Resume must be a valid file.',
            'resume.mimes' => 'Resume must be a PDF, DOC, or DOCX file.',
            'resume.max' => 'Resume file size must not exceed 5MB.',
            'message.max' => 'Message must not exceed 500 characters.'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Handle file upload
            $resumePath = null;
            if ($request->hasFile('resume')) {
                $resumePath = $request->file('resume')->store('career-applications/resumes', 'public');
            }

            // Create career application
            $application = CareerApplication::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->mobile,
                'position_applied' => $request->position,
                'resume_path' => $resumePath,
                'cover_letter' => $request->message,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Send email notification
            try {
                Mail::to('<EMAIL>')->send(new CareerApplicationNotification($application));
            } catch (\Exception $mailException) {
                // Log the mail error but don't fail the application submission
                \Log::error('Failed to send career application email: ' . $mailException->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your application! We will review your profile and get back to you soon.',
                'application_id' => $application->id
            ]);

        } catch (\Exception $e) {
            // If file was uploaded but database save failed, clean up the file
            if ($resumePath && Storage::disk('public')->exists($resumePath)) {
                Storage::disk('public')->delete($resumePath);
            }

            return response()->json([
                'success' => false,
                'message' => 'Sorry, there was an error submitting your application. Please try again or contact us directly.'
            ], 500);
        }
    }
}
