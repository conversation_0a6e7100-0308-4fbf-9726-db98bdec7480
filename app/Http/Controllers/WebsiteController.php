<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\ProjectCategory;
use App\Models\Gallery;
use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\HeroSlider;
use App\Models\PageHeader;
use App\Models\Testimonial;

class WebsiteController extends Controller
{
    public function index()
    {
        $heroSliders = HeroSlider::active()->ordered()->get();
        $featuredProjects = Project::active()->featured()->with('projectCategory')->take(3)->get();
        $recentProjects = Project::active()->with('projectCategory')->latest()->take(8)->get();
        $recentBlogs = Blog::published()->with('blogCategory')->latest('published_at')->take(3)->get();
        $galleryItems = Gallery::where('status', true)->with('projectCategory')->take(6)->get();
        $projectCategories = ProjectCategory::where('status', true)->get();
        return view('index', compact('heroSliders', 'featuredProjects', 'recentProjects', 'recentBlogs', 'galleryItems', 'projectCategories'));
    }

    public function services()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'services')->first();
        return view('services', compact('pageHeader'));
    }

    public function contact()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'contact')->first();
        return view('contact', compact('pageHeader'));
    }

    public function about()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'about')->first();
        return view('about', compact('pageHeader'));
    }

    public function howWeWork()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'how-we-work')->first();
        $testimonials = Testimonial::active()->ordered()->get();
        return view('how-we-work', compact('pageHeader', 'testimonials'));
    }

    public function whyChooseUs()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'why-choose-us')->first();
        $testimonials = Testimonial::active()->ordered()->get();
        return view('why-choose-us', compact('pageHeader', 'testimonials'));
    }

    public function projects(Request $request)
    {
        $pageHeader = PageHeader::active()->where('page_name', 'projects')->first();

        // Start with base query
        $query = Project::active()->with('projectCategory');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->filled('category') && $request->category !== 'all') {
            $query->byCategory($request->category);
        }

        // Location filter
        if ($request->filled('location') && $request->location !== 'all') {
            $query->byLocation($request->location);
        }

        // Type filter
        if ($request->filled('type') && $request->type !== 'all') {
            $query->byType($request->type);
        }

        // Status filter
        if ($request->filled('status') && $request->status !== 'all') {
            $query->byStatus($request->status);
        }

        // Price range filter
        if ($request->filled('price_range') && $request->price_range !== 'all') {
            $priceRange = $request->price_range;
            switch ($priceRange) {
                case 'under-50':
                    $query->where('price_range', 'like', '%50 Lakhs%')
                          ->where('price_range', 'not like', '%Cr%');
                    break;
                case '50-100':
                    $query->where(function($q) {
                        $q->where('price_range', 'like', '%50 Lakhs%')
                          ->orWhere('price_range', 'like', '%1 Cr%')
                          ->orWhere('price_range', 'like', '%1.%Cr%');
                    });
                    break;
                case '100-200':
                    $query->where('price_range', 'like', '%1%Cr%')
                          ->where('price_range', 'not like', '%5%Cr%');
                    break;
                case 'above-200':
                    $query->where(function($q) {
                        $q->where('price_range', 'like', '%2%Cr%')
                          ->orWhere('price_range', 'like', '%3%Cr%')
                          ->orWhere('price_range', 'like', '%4%Cr%')
                          ->orWhere('price_range', 'like', '%5%Cr%');
                    });
                    break;
            }
        }

        // Configuration filter (BHK)
        if ($request->filled('configuration') && $request->configuration !== 'all') {
            $query->byConfiguration($request->configuration);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'latest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderByRaw("CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(price_range, '₹', 2), ' ', 1) AS UNSIGNED) ASC");
                break;
            case 'price_high':
                $query->orderByRaw("CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(price_range, '₹', 2), ' ', 1) AS UNSIGNED) DESC");
                break;
            case 'name_asc':
                $query->orderBy('title', 'asc');
                break;
            case 'name_desc':
                $query->orderBy('title', 'desc');
                break;
            case 'featured':
                $query->orderBy('featured', 'desc')->orderBy('created_at', 'desc');
                break;
            default:
                $query->latest();
                break;
        }

        $projects = $query->paginate(12)->appends($request->query());
        $categories = ProjectCategory::where('status', true)->get();

        // Get unique locations, types, statuses for filter options
        $locations = Project::active()->distinct()->pluck('location')->filter()->sort()->values();
        $types = Project::active()->distinct()->pluck('type')->filter()->sort()->values();
        $statuses = Project::active()->distinct()->pluck('status')->filter()->sort()->values();
        $configurations = Project::active()->get()->pluck('configurations')->flatten()->unique()->sort()->values();

        // Get counts for each category
        $categoryCounts = [];
        foreach ($categories as $category) {
            $categoryCounts[$category->slug] = Project::active()
                ->whereHas('projectCategory', function($q) use ($category) {
                    $q->where('slug', $category->slug);
                })->count();
        }

        return view('projects', compact(
            'pageHeader',
            'projects',
            'categories',
            'locations',
            'types',
            'statuses',
            'configurations',
            'categoryCounts'
        ));
    }

    public function projectsold()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'projects')->first();
        $projects = Project::active()->with('projectCategory')->paginate(12);
        $categories = ProjectCategory::where('status', true)->get();
        return view('projects-old', compact('pageHeader', 'projects', 'categories'));
    }

    public function projectDetails($slug)
    {
        $project = Project::where('slug', $slug)->where('is_active', true)->with('projectCategory')->firstOrFail();
        $relatedProjects = Project::active()
            ->where('project_category_id', $project->project_category_id)
            ->where('id', '!=', $project->id)
            ->take(4)
            ->get();

        return view('project-details', compact('project', 'relatedProjects'));
    }

    public function projectDetailss($slug)
    {
        $project = Project::where('slug', $slug)->where('is_active', true)->with('projectCategory')->firstOrFail();
        $relatedProjects = Project::active()
            ->where('project_category_id', $project->project_category_id)
            ->where('id', '!=', $project->id)
            ->take(4)
            ->get();

        return view('project-details-old', compact('project', 'relatedProjects'));
    }

    public function blog(Request $request)
    {
        $pageHeader = PageHeader::active()->where('page_name', 'blog')->first();
        $query = Blog::published()->with('blogCategory')->latest('published_at');

        // Filter by category if specified
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        // Search functionality
        if ($request->has('search')) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
        }

        $blogs = $query->paginate(9);
        $featuredBlogs = Blog::published()->featured()->with('blogCategory')->take(3)->get();
        $categories = BlogCategory::active()->withCount('blogs')->get();
        $recentBlogs = Blog::published()->with('blogCategory')->latest('published_at')->take(5)->get();

        return view('blog', compact('pageHeader', 'blogs', 'featuredBlogs', 'categories', 'recentBlogs'));
    }

    public function blogDetails($slug)
    {
        $blog = Blog::where('slug', $slug)->published()->with('blogCategory')->firstOrFail();

        // Increment views
        $blog->incrementViews();

        // Get related blogs from same category
        $relatedBlogs = Blog::published()
            ->where('blog_category_id', $blog->blog_category_id)
            ->where('id', '!=', $blog->id)
            ->with('blogCategory')
            ->take(4)
            ->get();

        // Get recent blogs for sidebar
        $recentBlogs = Blog::published()
            ->where('id', '!=', $blog->id)
            ->with('blogCategory')
            ->latest('published_at')
            ->take(5)
            ->get();

        $categories = BlogCategory::active()->withCount('blogs')->get();

        return view('blog-details', compact('blog', 'relatedBlogs', 'recentBlogs', 'categories'));
    }

    public function gallery(Request $request)
    {
        $pageHeader = PageHeader::active()->where('page_name', 'gallery')->first();

        // Start with base query
        $query = Gallery::where('status', true)->with('projectCategory');

        // Category filter
        if ($request->filled('category') && $request->category !== 'all') {
            $query->whereHas('projectCategory', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        $galleries = $query->latest()->paginate(12);
        $categories = ProjectCategory::where('status', true)->get();

        return view('gallery', compact('pageHeader', 'galleries', 'categories'));
    }

    public function privacyPolicy()
    {
        $pageHeader = PageHeader::active()->where('page_name', 'privacy-policy')->first();
        return view('privacy-policy', compact('pageHeader'));
    }
}
