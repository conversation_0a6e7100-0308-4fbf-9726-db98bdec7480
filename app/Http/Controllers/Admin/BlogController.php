<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class BlogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $blogs = Blog::with('blogCategory')->latest()->paginate(10);
        return view('admin.blogs.index', compact('blogs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $blogCategories = BlogCategory::where('status', true)->get();
        return view('admin.blogs.create', compact('blogCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {

            $request->validate([
                'title' => 'required|string|max:255',
                'blog_category_id' => 'required|exists:blog_categories,id',
                'excerpt' => 'required|string|max:500',
                'content' => 'required|string',
                'featured_image' => 'required|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'gallery_images' => 'nullable|array',
                'gallery_images.*' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50',
                'author' => 'required|string|max:255',
                'read_time' => 'required|integer|min:1|max:60',
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string|max:500',
                'meta_keywords' => 'nullable|string',
            ], [
                'featured_image.required' => 'Please select a featured image.',
                'featured_image.image' => 'The featured image must be a valid image file.',
                'featured_image.mimes' => 'The featured image must be a file of type: jpeg, png, jpg, gif, webp.',
                'featured_image.max' => 'The featured image may not be greater than 2MB.',
                'gallery_images.*.image' => 'All gallery images must be valid image files.',
                'gallery_images.*.mimes' => 'All gallery images must be files of type: jpeg, png, jpg, gif, webp.',
                'gallery_images.*.max' => 'Each gallery image may not be greater than 2MB.',
            ]);

            // Ensure storage directories exist
            if (!Storage::disk('public')->exists('blogs/featured')) {
                Storage::disk('public')->makeDirectory('blogs/featured');
            }
            if (!Storage::disk('public')->exists('blogs/gallery')) {
                Storage::disk('public')->makeDirectory('blogs/gallery');
            }

            $data = $request->except(['featured_image', 'gallery_images']);
            $data['featured'] = $request->has('featured');
            $data['is_published'] = $request->has('is_published');

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                $featuredImage = $request->file('featured_image');
                if ($featuredImage->isValid()) {
                    $data['featured_image'] = $featuredImage->store('blogs/featured', 'public');
                }
            }

            // Handle gallery images upload
            $galleryImages = [];
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $image) {
                    if ($image->isValid()) {
                        $galleryImages[] = $image->store('blogs/gallery', 'public');
                    }
                }
            }
            $data['gallery_images'] = $galleryImages;

            // Set published_at if publishing
            if ($data['is_published']) {
                $data['published_at'] = now();
            }

            Blog::create($data);

            return redirect()->route('admin.blogs.index')
                            ->with('success', 'Blog post created successfully!');
                            
        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Blog creation failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to create blog post. Please try again.'])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Blog $blog)
    {
        $blog->load('blogCategory');
        return view('admin.blogs.show', compact('blog'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Blog $blog)
    {
        $blogCategories = BlogCategory::where('status', true)->get();
        return view('admin.blogs.edit', compact('blog', 'blogCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Blog $blog)
    {
        try {
            // Debug: Check if files are being received
            \Log::info('Update - Featured image file:', [
                'hasFile' => $request->hasFile('featured_image'),
                'file' => $request->file('featured_image') ? get_class($request->file('featured_image')) : 'null'
            ]);

            \Log::info('Update - Gallery images files:', [
                'hasFile' => $request->hasFile('gallery_images'),
                'files' => $request->file('gallery_images') ? count($request->file('gallery_images')) : 0
            ]);

            $request->validate([
                'title' => 'required|string|max:255',
                'blog_category_id' => 'required|exists:blog_categories,id',
                'excerpt' => 'required|string|max:500',
                'content' => 'required|string',
                'featured_image' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'gallery_images' => 'nullable|array',
                'gallery_images.*' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50',
                'author' => 'required|string|max:255',
                'read_time' => 'required|integer|min:1|max:60',
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string|max:500',
                'meta_keywords' => 'nullable|string',
            ], [
                'featured_image.image' => 'The featured image must be a valid image file.',
                'featured_image.mimes' => 'The featured image must be a file of type: jpeg, png, jpg, gif, webp.',
                'featured_image.max' => 'The featured image may not be greater than 2MB.',
                'gallery_images.*.image' => 'All gallery images must be valid image files.',
                'gallery_images.*.mimes' => 'All gallery images must be files of type: jpeg, png, jpg, gif, webp.',
                'gallery_images.*.max' => 'Each gallery image may not be greater than 2MB.',
            ]);

            $data = $request->except(['featured_image', 'gallery_images']);
            $data['featured'] = $request->has('featured');
            $data['is_published'] = $request->has('is_published');

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                // Delete old featured image
                if ($blog->featured_image && Storage::disk('public')->exists($blog->featured_image)) {
                    Storage::disk('public')->delete($blog->featured_image);
                }

                $featuredImage = $request->file('featured_image');
                if ($featuredImage->isValid()) {
                    $data['featured_image'] = $featuredImage->store('blogs/featured', 'public');
                }
            }

            // Handle gallery images upload
            if ($request->hasFile('gallery_images')) {
                // Delete old gallery images
                if ($blog->gallery_images) {
                    foreach ($blog->gallery_images as $oldImage) {
                        if (Storage::disk('public')->exists($oldImage)) {
                            Storage::disk('public')->delete($oldImage);
                        }
                    }
                }

                $galleryImages = [];
                foreach ($request->file('gallery_images') as $image) {
                    if ($image->isValid()) {
                        $galleryImages[] = $image->store('blogs/gallery', 'public');
                    }
                }
                $data['gallery_images'] = $galleryImages;
            }

            // Set published_at if publishing for the first time
            if ($data['is_published'] && !$blog->published_at) {
                $data['published_at'] = now();
            } elseif (!$data['is_published']) {
                $data['published_at'] = null;
            }

            $blog->update($data);

            return redirect()->route('admin.blogs.index')
                            ->with('success', 'Blog post updated successfully!');
                            
        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Blog update failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to update blog post. Please try again.'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Blog $blog)
    {
        try {
            // Delete associated images
            if ($blog->featured_image && Storage::disk('public')->exists($blog->featured_image)) {
                Storage::disk('public')->delete($blog->featured_image);
            }

            if ($blog->gallery_images) {
                foreach ($blog->gallery_images as $image) {
                    if (Storage::disk('public')->exists($image)) {
                        Storage::disk('public')->delete($image);
                    }
                }
            }

            $blog->delete();

            return redirect()->route('admin.blogs.index')
                            ->with('success', 'Blog post deleted successfully!');
                            
        } catch (\Exception $e) {
            Log::error('Blog deletion failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to delete blog post. Please try again.']);
        }
    }
}
