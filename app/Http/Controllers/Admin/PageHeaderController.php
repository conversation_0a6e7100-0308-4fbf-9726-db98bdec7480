<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PageHeader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PageHeaderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $headers = PageHeader::orderBy('page_name')->get();
        return view('admin.page-headers.index', compact('headers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.page-headers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'page_name' => 'required|string|max:255|unique:page_headers,page_name',
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'background_image' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            ], [
                'page_name.unique' => 'A header for this page already exists.',
                'background_image.image' => 'The background image must be a valid image file.',
                'background_image.mimes' => 'The background image must be a file of type: jpeg, png, jpg, gif, webp.',
                'background_image.max' => 'The background image may not be greater than 2MB.',
            ]);

            // Ensure storage directory exists
            if (!Storage::disk('public')->exists('page-headers')) {
                Storage::disk('public')->makeDirectory('page-headers');
            }

            $data = $request->except(['background_image']);
            $data['is_active'] = $request->has('is_active');

            // Handle background image upload
            if ($request->hasFile('background_image')) {
                $backgroundImage = $request->file('background_image');
                $backgroundImagePath = $backgroundImage->store('page-headers', 'public');
                $data['background_image'] = $backgroundImagePath;
            }

            PageHeader::create($data);

            return redirect()->route('admin.page-headers.index')
                           ->with('success', 'Page header created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Error creating page header: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PageHeader $pageHeader)
    {
        return view('admin.page-headers.show', compact('pageHeader'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PageHeader $pageHeader)
    {
        return view('admin.page-headers.edit', compact('pageHeader'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PageHeader $pageHeader)
    {
        try {
            $request->validate([
                'page_name' => 'required|string|max:255|unique:page_headers,page_name,' . $pageHeader->id,
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'background_image' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            ], [
                'page_name.unique' => 'A header for this page already exists.',
                'background_image.image' => 'The background image must be a valid image file.',
                'background_image.mimes' => 'The background image must be a file of type: jpeg, png, jpg, gif, webp.',
                'background_image.max' => 'The background image may not be greater than 2MB.',
            ]);

            $data = $request->except(['background_image']);
            $data['is_active'] = $request->has('is_active');

            // Handle background image upload
            if ($request->hasFile('background_image')) {
                // Delete old image if exists
                if ($pageHeader->background_image && Storage::disk('public')->exists($pageHeader->background_image)) {
                    Storage::disk('public')->delete($pageHeader->background_image);
                }

                $backgroundImage = $request->file('background_image');
                $backgroundImagePath = $backgroundImage->store('page-headers', 'public');
                $data['background_image'] = $backgroundImagePath;
            }

            $pageHeader->update($data);

            return redirect()->route('admin.page-headers.index')
                           ->with('success', 'Page header updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Error updating page header: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PageHeader $pageHeader)
    {
        try {
            // Delete background image if exists
            if ($pageHeader->background_image && Storage::disk('public')->exists($pageHeader->background_image)) {
                Storage::disk('public')->delete($pageHeader->background_image);
            }

            $pageHeader->delete();

            return redirect()->route('admin.page-headers.index')
                           ->with('success', 'Page header deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Error deleting page header: ' . $e->getMessage());
        }
    }
}
