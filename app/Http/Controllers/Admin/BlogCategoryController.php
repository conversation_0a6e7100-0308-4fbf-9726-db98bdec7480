<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BlogCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $blogCategories = BlogCategory::withCount('blogs')->paginate(10);
        return view('admin.blog-categories.index', compact('blogCategories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.blog-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:blog_categories,name',
                'description' => 'nullable|string',
                'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'status' => 'boolean'
            ]);

            $data = $request->all();
            $data['status'] = $request->has('status');

            BlogCategory::create($data);

            return redirect()->route('admin.blog-categories.index')
                            ->with('success', 'Blog category created successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Blog category creation failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to create blog category. Please try again.'])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(BlogCategory $blogCategory)
    {
        $blogCategory->load(['blogs' => function($query) {
            $query->latest()->take(10);
        }]);
        return view('admin.blog-categories.show', compact('blogCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogCategory $blogCategory)
    {
        return view('admin.blog-categories.edit', compact('blogCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BlogCategory $blogCategory)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:blog_categories,name,' . $blogCategory->id,
                'description' => 'nullable|string',
                'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'status' => 'boolean'
            ]);

            $data = $request->all();
            $data['status'] = $request->has('status');

            $blogCategory->update($data);

            return redirect()->route('admin.blog-categories.index')
                            ->with('success', 'Blog category updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Blog category update failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to update blog category. Please try again.'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlogCategory $blogCategory)
    {
        try {
            // Check if category has blogs
            if ($blogCategory->blogs()->count() > 0) {
                return back()->withErrors(['error' => 'Cannot delete category that has blog posts. Please move or delete the blog posts first.']);
            }

            $blogCategory->delete();

            return redirect()->route('admin.blog-categories.index')
                            ->with('success', 'Blog category deleted successfully!');

        } catch (\Exception $e) {
            Log::error('Blog category deletion failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to delete blog category. Please try again.']);
        }
    }
}
