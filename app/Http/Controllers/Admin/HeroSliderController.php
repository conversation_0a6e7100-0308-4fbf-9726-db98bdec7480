<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HeroSlider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HeroSliderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $sliders = HeroSlider::ordered()->get();
        return view('admin.hero-sliders.index', compact('sliders'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $currentCount = HeroSlider::count();

        // Prevent creation if limit reached
        if ($currentCount >= 3) {
            return redirect()->route('admin.hero-sliders.index')
                ->with('error', 'Maximum 3 hero sliders are allowed. Please delete an existing slider before adding a new one.');
        }

        return view('admin.hero-sliders.create', compact('currentCount'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Check if maximum limit of 3 sliders is reached
            $currentCount = HeroSlider::count();
            if ($currentCount >= 3) {
                return redirect()->back()
                    ->withErrors(['limit' => 'Maximum 3 hero sliders are allowed. Please delete an existing slider before adding a new one.'])
                    ->withInput();
            }

            $request->validate([
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'background_image' => 'required|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'button_text' => 'nullable|string|max:100',
                'button_link' => 'nullable|url|max:255',
                'order' => 'required|integer|min:0|max:3',
            ], [
                'background_image.required' => 'Please select a background image.',
                'background_image.image' => 'The background image must be a valid image file.',
                'background_image.mimes' => 'The background image must be a file of type: jpeg, png, jpg, gif, webp.',
                'background_image.max' => 'The background image may not be greater than 2MB.',
                'order.max' => 'Order must be between 1 and 3.',
            ]);

            // Ensure storage directory exists
            if (!Storage::disk('public')->exists('hero-sliders')) {
                Storage::disk('public')->makeDirectory('hero-sliders');
            }

            $data = $request->except(['background_image']);
            $data['is_active'] = $request->has('is_active');

            // Handle background image upload
            if ($request->hasFile('background_image')) {
                $backgroundImage = $request->file('background_image');
                $backgroundImagePath = $backgroundImage->store('hero-sliders', 'public');
                $data['background_image'] = $backgroundImagePath;
            }

            HeroSlider::create($data);

            return redirect()->route('admin.hero-sliders.index')
                           ->with('success', 'Hero slider created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Error creating hero slider: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(HeroSlider $heroSlider)
    {
        return view('admin.hero-sliders.show', compact('heroSlider'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(HeroSlider $heroSlider)
    {
        return view('admin.hero-sliders.edit', compact('heroSlider'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, HeroSlider $heroSlider)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'background_image' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'button_text' => 'nullable|string|max:100',
                'button_link' => 'nullable|url|max:255',
                'order' => 'required|integer|min:0',
            ], [
                'background_image.image' => 'The background image must be a valid image file.',
                'background_image.mimes' => 'The background image must be a file of type: jpeg, png, jpg, gif, webp.',
                'background_image.max' => 'The background image may not be greater than 2MB.',
            ]);

            $data = $request->except(['background_image']);
            $data['is_active'] = $request->has('is_active');

            // Handle background image upload
            if ($request->hasFile('background_image')) {
                // Delete old image if exists
                if ($heroSlider->background_image && Storage::disk('public')->exists($heroSlider->background_image)) {
                    Storage::disk('public')->delete($heroSlider->background_image);
                }

                $backgroundImage = $request->file('background_image');
                $backgroundImagePath = $backgroundImage->store('hero-sliders', 'public');
                $data['background_image'] = $backgroundImagePath;
            }

            $heroSlider->update($data);

            return redirect()->route('admin.hero-sliders.index')
                           ->with('success', 'Hero slider updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Error updating hero slider: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HeroSlider $heroSlider)
    {
        try {
            // Delete background image if exists
            if ($heroSlider->background_image && Storage::disk('public')->exists($heroSlider->background_image)) {
                Storage::disk('public')->delete($heroSlider->background_image);
            }

            $heroSlider->delete();

            return redirect()->route('admin.hero-sliders.index')
                           ->with('success', 'Hero slider deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Error deleting hero slider: ' . $e->getMessage());
        }
    }
}
