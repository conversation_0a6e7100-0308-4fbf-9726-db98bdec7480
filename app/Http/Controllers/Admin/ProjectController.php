<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ProjectCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $projects = Project::with('projectCategory')->latest()->paginate(10);
        return view('admin.projects.index', compact('projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $projectCategories = ProjectCategory::where('status', true)->get();
        return view('admin.projects.create', compact('projectCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'project_category_id' => 'required|exists:project_categories,id',
                'location' => 'required|string|max:255',
                'type' => 'required|string|max:255',
                'status' => 'required|string|max:255',
                'price_range' => 'required|string|max:255',
                'configurations' => 'required|array|min:1',
                'configurations.*' => 'required|string|max:255',
                'area_range' => 'required|string|max:255',
                'description' => 'required|string',
                'amenities' => 'required|array|min:1',
                'amenities.*' => 'required|string|max:255',
                'images' => 'required|array|min:1',
                'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'developer' => 'required|string|max:255',
                'rera_number' => 'nullable|string|max:255',
                'possession' => 'required|string|max:255',
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string',
                'meta_keywords' => 'nullable|string',
            ]);

            $data = $request->except(['images']);
            $data['featured'] = $request->has('featured');
            $data['is_active'] = $request->has('is_active');

            // Handle multiple image uploads
            $imagePaths = [];
            if ($request->hasFile('images')) {
                Log::info('Processing ' . count($request->file('images')) . ' uploaded files');
                foreach ($request->file('images') as $index => $image) {
                    Log::info("File {$index}: " . $image->getClientOriginalName() . " - Size: " . $image->getSize() . " - Valid: " . ($image->isValid() ? 'Yes' : 'No'));
                    if ($image->isValid()) {
                        try {
                            $imagePath = $image->store('projects', 'public');
                            $imagePaths[] = $imagePath;
                            Log::info("File {$index} stored successfully at: {$imagePath}");
                        } catch (\Exception $e) {
                            Log::error("Failed to store file {$index}: " . $e->getMessage());
                        }
                    } else {
                        Log::error("File {$index} is not valid. Error code: " . $image->getError());
                    }
                }
            } else {
                Log::warning('No files were uploaded');
            }

            if (empty($imagePaths)) {
                Log::error('No valid images were processed');
                return back()->withErrors(['images' => 'At least one valid image is required.'])->withInput();
            }

            Log::info('Successfully processed ' . count($imagePaths) . ' images');

            $data['images'] = $imagePaths;

            Project::create($data);

            return redirect()->route('admin.projects.index')
                            ->with('success', 'Project created successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Project creation failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to create project. Please try again.'])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        $project->load('projectCategory');
        return view('admin.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project)
    {
        $projectCategories = ProjectCategory::where('status', true)->get();
        return view('admin.projects.edit', compact('project', 'projectCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'project_category_id' => 'required|exists:project_categories,id',
                'location' => 'required|string|max:255',
                'type' => 'required|string|max:255',
                'status' => 'required|string|max:255',
                'price_range' => 'required|string|max:255',
                'configurations' => 'required|array|min:1',
                'configurations.*' => 'required|string|max:255',
                'area_range' => 'required|string|max:255',
                'description' => 'required|string',
                'amenities' => 'required|array|min:1',
                'amenities.*' => 'required|string|max:255',
                'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'developer' => 'required|string|max:255',
                'rera_number' => 'nullable|string|max:255',
                'possession' => 'required|string|max:255',
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string',
                'meta_keywords' => 'nullable|string',
            ]);

            $data = $request->except(['images']);
            $data['featured'] = $request->has('featured');
            $data['is_active'] = $request->has('is_active');

            // Handle image uploads
            if ($request->hasFile('images')) {
                // Delete old images
                if ($project->images) {
                    foreach ($project->images as $oldImage) {
                        if (Storage::disk('public')->exists($oldImage)) {
                            Storage::disk('public')->delete($oldImage);
                        }
                    }
                }

                // Store new images
                $imagePaths = [];
                foreach ($request->file('images') as $image) {
                    if ($image->isValid()) {
                        $imagePath = $image->store('projects', 'public');
                        $imagePaths[] = $imagePath;
                    }
                }

                if (!empty($imagePaths)) {
                    $data['images'] = $imagePaths;
                }
            }

            $project->update($data);

            return redirect()->route('admin.projects.index')
                            ->with('success', 'Project updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Project update failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to update project. Please try again.'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        // Delete project images
        if ($project->images) {
            foreach ($project->images as $image) {
                if (Storage::disk('public')->exists($image)) {
                    Storage::disk('public')->delete($image);
                }
            }
        }

        $project->delete();

        return redirect()->route('admin.projects.index')
                        ->with('success', 'Project deleted successfully!');
    }
}
