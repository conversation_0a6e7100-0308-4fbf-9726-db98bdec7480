<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ProjectEnquiry;
use App\Models\Project;

class ProjectEnquiryController extends Controller
{
    public function index(Request $request)
    {
        $query = ProjectEnquiry::with('project')->recent();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by project
        if ($request->filled('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        // Search by name, email, or phone
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $enquiries = $query->paginate(20);
        $projects = Project::orderBy('title')->get();

        // Get stats
        $stats = [
            'total' => ProjectEnquiry::count(),
            'new' => ProjectEnquiry::new()->count(),
            'contacted' => ProjectEnquiry::contacted()->count(),
            'converted' => ProjectEnquiry::converted()->count(),
        ];

        return view('admin.project-enquiries.index', compact('enquiries', 'projects', 'stats'));
    }

    public function show(ProjectEnquiry $projectEnquiry)
    {
        $projectEnquiry->load('project');
        return view('admin.project-enquiries.show', compact('projectEnquiry'));
    }

    public function update(Request $request, ProjectEnquiry $projectEnquiry)
    {
        $request->validate([
            'status' => 'required|in:new,contacted,interested,not_interested,converted',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes
        ];

        // Set contacted_at timestamp if status is being changed to contacted for the first time
        if ($request->status === 'contacted' && $projectEnquiry->status !== 'contacted') {
            $updateData['contacted_at'] = now();
        }

        $projectEnquiry->update($updateData);

        return redirect()->back()->with('success', 'Enquiry updated successfully.');
    }

    public function destroy(ProjectEnquiry $projectEnquiry)
    {
        $projectEnquiry->delete();
        return redirect()->route('admin.project-enquiries.index')->with('success', 'Enquiry deleted successfully.');
    }
}
