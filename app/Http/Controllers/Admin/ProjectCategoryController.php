<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProjectCategory;
use Illuminate\Http\Request;

class ProjectCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = ProjectCategory::latest()->paginate(10);
        return view('admin.project-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.project-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:project_categories',
            'description' => 'nullable|string',
            'status' => 'boolean',
        ]);

        ProjectCategory::create([
            'name' => $request->name,
            'description' => $request->description,
            'status' => $request->has('status'),
        ]);

        return redirect()->route('admin.project-categories.index')
                        ->with('success', 'Project category created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(ProjectCategory $projectCategory)
    {
        return view('admin.project-categories.show', compact('projectCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProjectCategory $projectCategory)
    {
        return view('admin.project-categories.edit', compact('projectCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProjectCategory $projectCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:project_categories,name,' . $projectCategory->id,
            'description' => 'nullable|string',
            'status' => 'boolean',
        ]);

        $projectCategory->update([
            'name' => $request->name,
            'description' => $request->description,
            'status' => $request->has('status'),
        ]);

        return redirect()->route('admin.project-categories.index')
                        ->with('success', 'Project category updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProjectCategory $projectCategory)
    {
        $projectCategory->delete();

        return redirect()->route('admin.project-categories.index')
                        ->with('success', 'Project category deleted successfully!');
    }
}
