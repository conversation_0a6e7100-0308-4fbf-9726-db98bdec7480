<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ContactSubmission;
use Illuminate\Support\Facades\Validator;

class ContactSubmissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ContactSubmission::query()->recent();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Search functionality
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('mobile', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Filter by property type
        if ($request->has('property_type') && $request->property_type !== '') {
            $query->where('property_type', $request->property_type);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from !== '') {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to !== '') {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $submissions = $query->paginate(15);

        // Get statistics
        $stats = [
            'total' => ContactSubmission::count(),
            'new' => ContactSubmission::new()->count(),
            'contacted' => ContactSubmission::contacted()->count(),
            'in_progress' => ContactSubmission::inProgress()->count(),
            'closed' => ContactSubmission::closed()->count(),
        ];

        return view('admin.contact-submissions.index', compact('submissions', 'stats'));
    }

    /**
     * Display the specified resource.
     */
    public function show(ContactSubmission $contactSubmission)
    {
        return view('admin.contact-submissions.show', compact('contactSubmission'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ContactSubmission $contactSubmission)
    {
        return view('admin.contact-submissions.edit', compact('contactSubmission'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContactSubmission $contactSubmission)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:new,contacted,in_progress,closed',
            'admin_notes' => 'nullable|string|max:2000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes
        ];

        // Set contacted_at timestamp when status changes to contacted
        if ($request->status === 'contacted' && $contactSubmission->status !== 'contacted') {
            $updateData['contacted_at'] = now();
        }

        $contactSubmission->update($updateData);

        return redirect()->route('admin.contact-submissions.index')
            ->with('success', 'Contact submission updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContactSubmission $contactSubmission)
    {
        $contactSubmission->delete();

        return redirect()->route('admin.contact-submissions.index')
            ->with('success', 'Contact submission deleted successfully.');
    }

    /**
     * Bulk update status
     */
    public function bulkUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'submissions' => 'required|array',
            'submissions.*' => 'exists:contact_submissions,id',
            'bulk_status' => 'required|string|in:new,contacted,in_progress,closed'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->with('error', 'Invalid bulk update request.');
        }

        $updateData = ['status' => $request->bulk_status];

        // Set contacted_at timestamp for bulk contacted status
        if ($request->bulk_status === 'contacted') {
            $updateData['contacted_at'] = now();
        }

        ContactSubmission::whereIn('id', $request->submissions)->update($updateData);

        $count = count($request->submissions);
        return redirect()->route('admin.contact-submissions.index')
            ->with('success', "Successfully updated {$count} contact submissions.");
    }
}
