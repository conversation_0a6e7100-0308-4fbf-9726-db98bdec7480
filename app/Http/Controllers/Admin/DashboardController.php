<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\ProjectCategory;
use App\Models\Project;
use App\Models\Gallery;
use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\ProjectEnquiry;
use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use App\Models\HeroSlider;
use App\Models\Testimonial;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            // Core Statistics
            'total_admins' => Admin::count(),
            'active_admins' => Admin::where('status', true)->count(),
            'total_projects' => Project::count(),
            'active_projects' => Project::where('is_active', true)->count(),
            'featured_projects' => Project::where('featured', true)->count(),
            'total_galleries' => Gallery::count(),
            'total_blogs' => Blog::count(),
            'published_blogs' => Blog::where('is_published', true)->count(),
            'total_categories' => ProjectCategory::count(),
            'blog_categories' => BlogCategory::count(),

            // Project Enquiries Statistics
            'total_enquiries' => ProjectEnquiry::count(),
            'new_enquiries' => ProjectEnquiry::new()->count(),
            'contacted_enquiries' => ProjectEnquiry::contacted()->count(),
            'converted_enquiries' => ProjectEnquiry::where('status', 'converted')->count(),
            'today_enquiries' => ProjectEnquiry::whereDate('created_at', today())->count(),
            'this_week_enquiries' => ProjectEnquiry::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month_enquiries' => ProjectEnquiry::whereMonth('created_at', now()->month)->count(),

            // Contact & Newsletter Statistics
            'total_contacts' => ContactSubmission::count(),
            'new_contacts' => ContactSubmission::new()->count(),
            'total_subscribers' => NewsletterSubscription::count(),
            'active_subscribers' => NewsletterSubscription::active()->count(),
            'this_week_subscribers' => NewsletterSubscription::thisWeek()->count(),

            // Content Statistics
            'hero_sliders' => HeroSlider::count(),
            'active_sliders' => HeroSlider::active()->count(),
            'testimonials' => Testimonial::count(),
            'active_testimonials' => Testimonial::active()->count(),

            // Performance Metrics
            'conversion_rate' => $this->calculateConversionRate(),
            'response_rate' => $this->calculateResponseRate(),
        ];

        // Recent Activities
        $recentEnquiries = ProjectEnquiry::with('project')
            ->latest()
            ->take(5)
            ->get();

        $recentContacts = ContactSubmission::latest()
            ->take(3)
            ->get();

        $recentBlogs = Blog::with('blogCategory')
            ->latest()
            ->take(3)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentEnquiries', 'recentContacts', 'recentBlogs'));
    }

    private function calculateConversionRate()
    {
        $totalEnquiries = ProjectEnquiry::count();
        $convertedEnquiries = ProjectEnquiry::where('status', 'converted')->count();

        if ($totalEnquiries == 0) {
            return 0;
        }

        return round(($convertedEnquiries / $totalEnquiries) * 100, 1);
    }

    private function calculateResponseRate()
    {
        $totalEnquiries = ProjectEnquiry::count();
        $respondedEnquiries = ProjectEnquiry::whereIn('status', ['contacted', 'interested', 'converted'])->count();

        if ($totalEnquiries == 0) {
            return 0;
        }

        return round(($respondedEnquiries / $totalEnquiries) * 100, 1);
    }
}
