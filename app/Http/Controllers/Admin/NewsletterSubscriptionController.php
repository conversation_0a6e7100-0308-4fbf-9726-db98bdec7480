<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\NewsletterSubscription;
use Illuminate\Support\Facades\Validator;

class NewsletterSubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = NewsletterSubscription::query()->recent();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by source
        if ($request->has('source') && $request->source !== '') {
            $query->where('source', $request->source);
        }

        // Search by email
        if ($request->has('search') && $request->search !== '') {
            $query->where('email', 'like', '%' . $request->search . '%');
        }

        // Date range filter
        if ($request->has('date_from') && $request->date_from !== '') {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to !== '') {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $subscriptions = $query->paginate(15);

        // Statistics
        $stats = [
            'total' => NewsletterSubscription::count(),
            'active' => NewsletterSubscription::active()->count(),
            'unsubscribed' => NewsletterSubscription::unsubscribed()->count(),
            'this_month' => NewsletterSubscription::thisMonth()->count(),
            'this_week' => NewsletterSubscription::thisWeek()->count(),
        ];

        return view('admin.newsletter-subscriptions.index', compact('subscriptions', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.newsletter-subscriptions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255|unique:newsletter_subscriptions,email',
            'status' => 'required|in:active,unsubscribed',
            'source' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        NewsletterSubscription::create([
            'email' => $request->email,
            'status' => $request->status,
            'source' => $request->source,
            'subscribed_at' => $request->status === 'active' ? now() : null,
            'unsubscribed_at' => $request->status === 'unsubscribed' ? now() : null,
        ]);

        return redirect()->route('admin.newsletter-subscriptions.index')
                        ->with('success', 'Newsletter subscription created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(NewsletterSubscription $newsletterSubscription)
    {
        return view('admin.newsletter-subscriptions.show', compact('newsletterSubscription'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(NewsletterSubscription $newsletterSubscription)
    {
        return view('admin.newsletter-subscriptions.edit', compact('newsletterSubscription'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, NewsletterSubscription $newsletterSubscription)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255|unique:newsletter_subscriptions,email,' . $newsletterSubscription->id,
            'status' => 'required|in:active,unsubscribed',
            'unsubscribe_reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = [
            'email' => $request->email,
            'status' => $request->status,
        ];

        // Handle status change timestamps
        if ($request->status === 'active' && $newsletterSubscription->status !== 'active') {
            $data['subscribed_at'] = now();
            $data['unsubscribed_at'] = null;
            $data['unsubscribe_reason'] = null;
        } elseif ($request->status === 'unsubscribed' && $newsletterSubscription->status !== 'unsubscribed') {
            $data['unsubscribed_at'] = now();
            $data['unsubscribe_reason'] = $request->unsubscribe_reason;
        }

        $newsletterSubscription->update($data);

        return redirect()->route('admin.newsletter-subscriptions.index')
                        ->with('success', 'Newsletter subscription updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(NewsletterSubscription $newsletterSubscription)
    {
        $newsletterSubscription->delete();

        return redirect()->route('admin.newsletter-subscriptions.index')
                        ->with('success', 'Newsletter subscription deleted successfully.');
    }

    /**
     * Bulk update subscriptions
     */
    public function bulkUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subscriptions' => 'required|array',
            'subscriptions.*' => 'exists:newsletter_subscriptions,id',
            'bulk_status' => 'required|in:active,unsubscribed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $subscriptions = NewsletterSubscription::whereIn('id', $request->subscriptions)->get();

        foreach ($subscriptions as $subscription) {
            if ($request->bulk_status === 'active') {
                $subscription->resubscribe();
            } else {
                $subscription->unsubscribe('Bulk update by admin');
            }
        }

        $count = count($request->subscriptions);
        $status = $request->bulk_status === 'active' ? 'activated' : 'unsubscribed';

        return redirect()->route('admin.newsletter-subscriptions.index')
                        ->with('success', "{$count} subscriptions have been {$status} successfully.");
    }

    /**
     * Export subscriptions
     */
    public function export(Request $request)
    {
        $query = NewsletterSubscription::query();

        // Apply same filters as index
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        if ($request->has('source') && $request->source !== '') {
            $query->where('source', $request->source);
        }

        $subscriptions = $query->orderBy('created_at', 'desc')->get();

        $filename = 'newsletter_subscriptions_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($subscriptions) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Email',
                'Status',
                'Source',
                'Subscribed At',
                'Unsubscribed At',
                'Unsubscribe Reason',
                'IP Address',
                'Created At'
            ]);

            // CSV data
            foreach ($subscriptions as $subscription) {
                fputcsv($file, [
                    $subscription->id,
                    $subscription->email,
                    $subscription->status,
                    $subscription->source,
                    $subscription->subscribed_at ? $subscription->subscribed_at->format('Y-m-d H:i:s') : '',
                    $subscription->unsubscribed_at ? $subscription->unsubscribed_at->format('Y-m-d H:i:s') : '',
                    $subscription->unsubscribe_reason,
                    $subscription->ip_address,
                    $subscription->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
