<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CareerApplication;
use Illuminate\Support\Facades\Storage;

class CareerApplicationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CareerApplication::query();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by position
        if ($request->filled('position')) {
            $query->where('position_applied', $request->position);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $applications = $query->recent()->paginate(20);

        $statusOptions = CareerApplication::getStatusOptions();
        $positionOptions = CareerApplication::getPositionOptions();

        return view('admin.career-applications.index', compact('applications', 'statusOptions', 'positionOptions'));
    }

    /**
     * Display the specified resource.
     */
    public function show(CareerApplication $careerApplication)
    {
        return view('admin.career-applications.show', compact('careerApplication'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CareerApplication $careerApplication)
    {
        $statusOptions = CareerApplication::getStatusOptions();
        return view('admin.career-applications.edit', compact('careerApplication', 'statusOptions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CareerApplication $careerApplication)
    {
        $request->validate([
            'status' => 'required|in:' . implode(',', array_keys(CareerApplication::getStatusOptions())),
            'admin_notes' => 'nullable|string|max:2000'
        ]);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes
        ];

        // Set reviewed_at timestamp if status is being changed from 'new'
        if ($careerApplication->status === 'new' && $request->status !== 'new') {
            $updateData['reviewed_at'] = now();
        }

        $careerApplication->update($updateData);

        return redirect()->route('admin.career-applications.index')
                        ->with('success', 'Career application updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CareerApplication $careerApplication)
    {
        // Delete the resume file if it exists
        if ($careerApplication->resume_path && Storage::disk('public')->exists($careerApplication->resume_path)) {
            Storage::disk('public')->delete($careerApplication->resume_path);
        }

        $careerApplication->delete();

        return redirect()->route('admin.career-applications.index')
                        ->with('success', 'Career application deleted successfully.');
    }

    /**
     * Download resume file
     */
    public function downloadResume(CareerApplication $careerApplication)
    {
        if (!$careerApplication->resume_path || !Storage::disk('public')->exists($careerApplication->resume_path)) {
            return redirect()->back()->with('error', 'Resume file not found.');
        }

        $fileName = 'Resume_' . str_replace(' ', '_', $careerApplication->name) . '_' . $careerApplication->id . '.pdf';

        return Storage::disk('public')->download($careerApplication->resume_path, $fileName);
    }

    /**
     * Bulk update status
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'applications' => 'required|array',
            'applications.*' => 'exists:career_applications,id',
            'status' => 'required|in:' . implode(',', array_keys(CareerApplication::getStatusOptions()))
        ]);

        $updateData = ['status' => $request->status];

        // Set reviewed_at timestamp if status is not 'new'
        if ($request->status !== 'new') {
            $updateData['reviewed_at'] = now();
        }

        CareerApplication::whereIn('id', $request->applications)->update($updateData);

        $count = count($request->applications);
        return redirect()->route('admin.career-applications.index')
                        ->with('success', "{$count} applications updated successfully.");
    }
}
