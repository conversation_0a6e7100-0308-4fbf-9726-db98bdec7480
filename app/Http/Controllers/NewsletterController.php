<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\NewsletterSubscription;

class NewsletterController extends Controller
{
    /**
     * Subscribe to newsletter
     */
    public function subscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please enter a valid email address.',
                    'errors' => $validator->errors()
                ], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        $email = $request->email;

        // Check if email already exists
        $existingSubscription = NewsletterSubscription::where('email', $email)->first();

        if ($existingSubscription) {
            if ($existingSubscription->isActive()) {
                $message = 'You are already subscribed to our newsletter!';
            } else {
                // Reactivate subscription
                $existingSubscription->resubscribe();
                $message = 'Welcome back! Your subscription has been reactivated.';
            }
        } else {
            // Create new subscription
            NewsletterSubscription::create([
                'email' => $email,
                'status' => NewsletterSubscription::STATUS_ACTIVE,
                'source' => 'website',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'subscribed_at' => now()
            ]);

            $message = 'Thank you for subscribing! You will receive our latest updates and property listings.';
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        }

        return back()->with('success', $message);
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribe(Request $request, $email = null)
    {
        $email = $email ?: $request->email;

        if (!$email) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email address is required.'
                ], 400);
            }

            return back()->withErrors(['email' => 'Email address is required.']);
        }

        $subscription = NewsletterSubscription::where('email', $email)->first();

        if (!$subscription) {
            $message = 'Email address not found in our subscription list.';
        } elseif ($subscription->isUnsubscribed()) {
            $message = 'You are already unsubscribed from our newsletter.';
        } else {
            $subscription->unsubscribe($request->reason);
            $message = 'You have been successfully unsubscribed from our newsletter.';
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        }

        return back()->with('success', $message);
    }
}
