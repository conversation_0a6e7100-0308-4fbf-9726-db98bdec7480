<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProjectEnquiry;
use App\Models\Project;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\ProjectEnquiryNotification;

class ProjectEnquiryController extends Controller
{
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'project_name' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'interest' => 'required|in:site_visit,price_details,floor_plans,loan_assistance',
            'message' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your form data and try again.',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Please check your form data and try again.');
        }

        try {
            $enquiry = ProjectEnquiry::create([
                'project_id' => $request->project_id,
                'project_name' => $request->project_name,
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'interest' => $request->interest,
                'message' => $request->message,
                'status' => 'new'
            ]);

            // Send email notification
            try {
                Mail::to('<EMAIL>')->send(new ProjectEnquiryNotification($enquiry));
            } catch (\Exception $mailException) {
                // Log the mail error but don't fail the enquiry submission
                \Log::error('Failed to send project enquiry email: ' . $mailException->getMessage());
            }

            // Check if it's an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for your interest! We will contact you soon.'
                ]);
            }

            return redirect()->back()->with('success', 'Thank you for your interest! We will contact you soon.');

        } catch (\Exception $e) {
            // Check if it's an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong. Please try again later.'
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'Something went wrong. Please try again later.');
        }
    }
}
