<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ContactSubmission;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactSubmissionNotification;

class ContactController extends Controller
{
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'mobile' => 'required|string|max:15|regex:/^[0-9]{10}$/',
            'propertyType' => 'required|string|in:residential,commercial,investment,rental',
            'budgetRange' => 'required|string|in:under-50,50-100,100-200,200-500,above-500',
            'location' => 'required|string|max:255',
            'message' => 'required|string|max:2000'
        ], [
            'name.required' => 'Please enter your full name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'mobile.required' => 'Please enter your mobile number.',
            'mobile.regex' => 'Please enter a valid 10-digit mobile number.',
            'propertyType.required' => 'Please select a property type.',
            'budgetRange.required' => 'Please select your budget range.',
            'location.required' => 'Please select your preferred location.',
            'message.required' => 'Please enter your message.'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $submission = ContactSubmission::create([
                'name' => $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'property_type' => $request->propertyType,
                'budget_range' => $request->budgetRange,
                'location' => $request->location,
                'message' => $request->message,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Send email notification
            try {
                Mail::to('<EMAIL>')->send(new ContactSubmissionNotification($submission));
            } catch (\Exception $mailException) {
                // Log the mail error but don't fail the contact submission
                \Log::error('Failed to send contact submission email: ' . $mailException->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your inquiry! We will get back to you within 24 hours.',
                'submission_id' => $submission->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sorry, there was an error submitting your form. Please try again or contact us directly.'
            ], 500);
        }
    }
}
