/* CKEditor 5 Custom Styling for Admin Panel */

/* CKEditor container styling */
.ck-editor {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.ck-editor:focus-within {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Toolbar styling */
.ck-toolbar {
    border-radius: 8px 8px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    padding: 12px;
}

.ck-toolbar .ck-toolbar__items {
    flex-wrap: wrap;
    gap: 4px;
}

/* Button styling */
.ck-button {
    border-radius: 6px;
    transition: all 0.2s ease;
}

.ck-button:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
}

.ck-button.ck-on {
    background-color: #007bff;
    color: white;
}

/* Editor content area */
.ck-content {
    min-height: 400px;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    border-radius: 0 0 8px 8px;
}

/* Content styling */
.ck-content h1,
.ck-content h2,
.ck-content h3,
.ck-content h4,
.ck-content h5,
.ck-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
}

.ck-content h1 {
    font-size: 2.2em;
    color: #2c3e50;
}

.ck-content h2 {
    font-size: 1.8em;
    color: #34495e;
}

.ck-content h3 {
    font-size: 1.5em;
    color: #34495e;
}

.ck-content p {
    margin-bottom: 1em;
}

.ck-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1em;
    margin: 1em 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1em;
    border-radius: 4px;
}

.ck-content ul,
.ck-content ol {
    margin: 1em 0;
    padding-left: 2em;
}

.ck-content li {
    margin-bottom: 0.5em;
}

.ck-content a {
    color: #007bff;
    text-decoration: underline;
}

.ck-content a:hover {
    color: #0056b3;
}

/* Table styling */
.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

.ck-content table td,
.ck-content table th {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
}

.ck-content table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Image styling */
.ck-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Code styling */
.ck-content code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #e83e8c;
}

.ck-content pre {
    background-color: #f8f9fa;
    padding: 1em;
    border-radius: 4px;
    overflow-x: auto;
    border-left: 4px solid #007bff;
}

.ck-content pre code {
    background: none;
    padding: 0;
    color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ck-toolbar {
        padding: 8px;
    }
    
    .ck-content {
        padding: 15px;
        font-size: 14px;
    }
    
    .ck-toolbar .ck-toolbar__items {
        gap: 2px;
    }
}

/* Loading state */
.ck-editor.ck-editor--loading {
    opacity: 0.7;
}

.ck-editor.ck-editor--loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus states */
.ck-editor__editable:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px #007bff;
}

/* Dropdown styling */
.ck-dropdown__panel {
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Balloon toolbar */
.ck-balloon-panel {
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
