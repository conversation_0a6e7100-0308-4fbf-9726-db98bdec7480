/* Enhanced Blog Page Styles */

/* Hero Section */
.blog-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.blog-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
    background-position: bottom;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.hero-stats {
    margin-top: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-search-box {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.btn-search {
    background: #ff6b6b;
    border: none;
    border-radius: 25px;
    color: white;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-search:hover {
    background: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* Featured Articles Section */
.featured-articles-section {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    position: relative;
}

.section-header {
    margin-bottom: 3rem;
}

.section-badge {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b6b, #ffa726);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

/* Featured Article Cards */
.featured-article-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.featured-article-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.main-featured {
    min-height: 500px;
}

.side-featured {
    min-height: 400px;
}

.article-image-container {
    position: relative;
    overflow: hidden;
}

.main-featured .article-image-container {
    height: 300px;
}

.side-featured .article-image-container {
    height: 200px;
}

.article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-article-card:hover .article-image {
    transform: scale(1.05);
}

.article-image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #f1f3f4, #e8eaed);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #9aa0a6;
}

.article-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), transparent, rgba(0, 0, 0, 0.7));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.featured-article-card:hover .article-overlay {
    opacity: 1;
}

.article-badges {
    position: absolute;
    top: 1rem;
    left: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
    align-items: flex-start;
}

.featured-badge {
    background: linear-gradient(45deg, #ffd700, #ffb347);
    color: #333;
    font-weight: 600;
    border-radius: 15px;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

.category-badge {
    color: white;
    font-weight: 500;
    border-radius: 15px;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

.article-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.meta-item i {
    color: #007bff;
}

.article-title {
    margin-bottom: 1rem;
    line-height: 1.3;
}

.title-link {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.title-link:hover {
    color: #007bff;
}

.article-excerpt {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.article-footer {
    margin-top: auto;
}

.read-more-btn {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    color: white;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

/* Main Content Section */
.main-content-section {
    background: #ffffff;
}

.content-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 2rem;
}

.content-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.content-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Filters Section */
.filters-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid #e9ecef;
}

.filters-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0;
}

.filter-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.filter-pill {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.filter-pill:hover {
    color: #007bff;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.filter-pill.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.category-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.category-count {
    background: rgba(0, 0, 0, 0.1);
    color: inherit;
    padding: 0.1rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

.filter-pill.active .category-count {
    background: rgba(255, 255, 255, 0.2);
}

/* Search Section */
.search-section {
    margin-top: 1.5rem;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.search-input-group:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-icon {
    padding: 0 1rem;
    color: #6c757d;
}

.search-input-main {
    flex: 1;
    border: none;
    outline: none;
    padding: 0.75rem 0;
    font-size: 1rem;
    background: transparent;
}

.search-btn-main {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn-main:hover {
    background: #0056b3;
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.filter-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.active-filter {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.remove-filter {
    color: #1976d2;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.1rem;
    line-height: 1;
}

.remove-filter:hover {
    color: #d32f2f;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .main-featured,
    .side-featured {
        min-height: auto;
    }
    
    .filter-pills {
        justify-content: center;
    }
    
    .search-input-group {
        flex-direction: column;
        border-radius: 15px;
    }
    
    .search-btn-main {
        width: 100%;
        border-radius: 0 0 15px 15px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-search-box {
        padding: 1.5rem;
    }
    
    .filters-section {
        padding: 1.5rem;
    }
    
    .article-content {
        padding: 1.5rem;
    }
}
