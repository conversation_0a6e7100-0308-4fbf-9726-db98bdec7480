/* Enhanced Blog Details Page Styles */

/* Article Hero Section */
.article-hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.article-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.05"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
    background-position: bottom;
}

.article-hero-content {
    position: relative;
    z-index: 2;
}

.article-breadcrumb {
    margin-bottom: 2rem;
}

.article-breadcrumb .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
}

.article-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.article-breadcrumb .breadcrumb-item.active {
    color: white;
}

.article-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.article-badges {
    margin-bottom: 2rem;
}

.article-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-right: 0.75rem;
    display: inline-block;
}

.featured-badge {
    background: linear-gradient(45deg, #ffd700, #ffb347);
    color: #333;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meta-item i {
    color: #3498db;
}

/* Featured Image Section */
.featured-image-section {
    margin: -2rem 0 3rem 0;
    position: relative;
    z-index: 3;
}

.featured-image-container {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    position: relative;
}

.featured-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-image-container:hover .featured-image {
    transform: scale(1.02);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 2rem;
    color: white;
}

.image-caption {
    font-size: 0.9rem;
    margin: 0;
}

/* Article Content */
.article-content-section {
    padding: 3rem 0;
}

.article-excerpt {
    font-size: 1.25rem;
    line-height: 1.7;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 0 10px 10px 0;
    margin-bottom: 3rem;
}

.article-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2c3e50;
}

.article-body h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 2.5rem 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid #007bff;
}

.article-body h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #34495e;
    margin: 2rem 0 1rem 0;
}

.article-body p {
    margin-bottom: 1.5rem;
}

.article-body ul,
.article-body ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-body li {
    margin-bottom: 0.5rem;
}

.article-body blockquote {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 10px 10px 0;
}

.article-body img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 1.5rem 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Gallery Section */
.gallery-section {
    margin: 3rem 0;
}

.gallery-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    text-align: center;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.gallery-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Tags Section */
.tags-section {
    margin: 3rem 0;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
}

.tags-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.tag-item {
    display: inline-block;
    background: white;
    color: #495057;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateY(-2px);
}

/* Social Share Section */
.social-share-section {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin: 3rem 0;
    text-align: center;
}

.share-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    color: white;
}

.share-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    color: white;
}

.share-facebook { background: #3b5998; }
.share-twitter { background: #1da1f2; }
.share-linkedin { background: #0077b5; }
.share-whatsapp { background: #25d366; }

/* Author Section */
.author-section {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    margin: 3rem 0;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.author-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
}

.author-details h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.author-bio {
    color: #6c757d;
    margin: 0;
}

/* Related Articles Section */
.related-articles-section {
    background: #f8f9fa;
    padding: 3rem 0;
    margin-top: 3rem;
}

.related-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3rem;
}

.related-article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.related-article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.related-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.related-content {
    padding: 1.5rem;
}

.related-category {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.related-article-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.related-article-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.related-article-title a:hover {
    color: #007bff;
}

.related-meta {
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    gap: 1rem;
}

/* Sidebar */
.sidebar-section {
    position: sticky;
    top: 2rem;
}

.sidebar-widget {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.widget-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
}

.recent-post-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.recent-post-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.recent-post-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    flex-shrink: 0;
}

.recent-post-content h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.recent-post-content h6 a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.recent-post-content h6 a:hover {
    color: #007bff;
}

.recent-post-meta {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .article-title {
        font-size: 2rem;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .featured-image {
        height: 250px;
    }
    
    .article-body {
        font-size: 1rem;
    }
    
    .article-body h2 {
        font-size: 1.5rem;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .share-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .author-info {
        flex-direction: column;
        text-align: center;
    }
    
    .sidebar-section {
        position: static;
        margin-top: 3rem;
    }
}

@media (max-width: 576px) {
    .article-hero-section {
        padding: 2rem 0;
    }
    
    .article-title {
        font-size: 1.75rem;
    }
    
    .featured-image-section {
        margin: -1rem 0 2rem 0;
    }
    
    .article-excerpt {
        padding: 1.5rem;
        font-size: 1.1rem;
    }
    
    .sidebar-widget {
        padding: 1.5rem;
    }
}
