/* Projects Search & Filter Styles */

/* Search Section */
.search-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.search-input-wrapper {
    position: relative;
}

.search-input {
    padding: 15px 50px 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 18px;
}

.clear-search {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #dc3545;
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.clear-search:hover {
    background: #dc3545;
    color: white;
}

.search-btn {
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: 600;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.results-info {
    text-align: right;
}

.results-count {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

.clear-filters {
    color: #dc3545;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.clear-filters:hover {
    color: #c82333;
    text-decoration: underline;
}

/* Advanced Filters */
.advanced-filters {
    background: white;
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    margin-top: 20px;
}

.filter-select, .sort-select {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.filter-select:focus, .sort-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-label {
    font-weight: 600;
    color: #495057;
    margin: 0;
    white-space: nowrap;
}

.sort-select {
    max-width: 200px;
}

/* View Toggle */
.view-toggle {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.view-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 10px 15px;
    border-radius: 8px;
    color: #6c757d;
    transition: all 0.3s ease;
    cursor: pointer;
}

.view-btn.active,
.view-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Active Filters */
.active-filters {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 15px 20px;
}

.filter-label {
    font-weight: 600;
    color: #856404;
}

.filter-tag {
    background: #007bff;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    margin: 2px 5px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.remove-filter {
    color: white;
    text-decoration: none;
    font-weight: bold;
    font-size: 14px;
    line-height: 1;
    transition: all 0.3s ease;
}

.remove-filter:hover {
    color: #ffdddd;
    transform: scale(1.2);
}

/* Project Cards Enhancements */
.project-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.no-image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
}

/* Project Badges Container */
.project-badges-container {
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    z-index: 10;
    pointer-events: none;
}

.project-badges-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
}

.project-badges-right {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
}

/* Featured Badge */
.featured-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 700;
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 70px;
    height: 28px;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.featured-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.6);
}

.featured-badge i {
    font-size: 10px;
    animation: sparkle 2s infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

/* Project Status Badge */
.project-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 6px 12px;
    border-radius: 18px;
    font-size: 11px;
    font-weight: 600;
    box-shadow: 0 3px 8px rgba(40, 167, 69, 0.4);
    text-transform: capitalize;
    min-width: 80px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.project-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.6);
}

/* Project Possession Status */
.project-status {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(0,0,0,0.7));
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 70px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    z-index: 10;
}

.project-status:hover {
    background: linear-gradient(135deg, rgba(0,0,0,1), rgba(0,0,0,0.8));
    transform: scale(1.05);
}

/* Badge Responsive Design */
@media (max-width: 768px) {
    .project-badges-container {
        top: 10px;
        left: 10px;
        right: 10px;
    }

    .featured-badge,
    .project-badge {
        font-size: 10px;
        padding: 4px 8px;
        min-width: 60px;
        height: 24px;
    }

    .project-status {
        bottom: 10px;
        right: 10px;
        font-size: 9px;
        padding: 4px 8px;
        min-width: 60px;
        height: 22px;
    }
}

@media (max-width: 576px) {
    .project-badges-left,
    .project-badges-right {
        gap: 6px;
    }

    .featured-badge,
    .project-badge {
        font-size: 9px;
        padding: 3px 6px;
        min-width: 50px;
        height: 20px;
    }

    .project-status {
        font-size: 8px;
        padding: 3px 6px;
        min-width: 50px;
        height: 18px;
    }
}

/* List View Styles */
.projects-grid.list-view .project-item {
    margin-bottom: 20px;
}

.projects-grid.list-view .project-card {
    display: flex;
    flex-direction: row;
    height: auto;
}

.projects-grid.list-view .project-image {
    width: 300px;
    height: 200px;
    flex-shrink: 0;
}

.projects-grid.list-view .project-content {
    flex: 1;
    padding: 20px;
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-section {
        padding: 20px;
    }
    
    .advanced-filters {
        padding: 15px;
    }
    
    .sort-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .sort-select {
        max-width: 100%;
        width: 100%;
    }
    
    .view-toggle {
        justify-content: center;
        margin-top: 15px;
    }
    
    .results-info {
        text-align: center;
    }
    
    .filter-tag {
        margin: 5px 2px;
    }
    
    .projects-grid.list-view .project-card {
        flex-direction: column;
    }
    
    .projects-grid.list-view .project-image {
        width: 100%;
        height: 250px;
    }
}

@media (max-width: 576px) {
    .search-input {
        padding: 12px 45px 12px 15px;
        font-size: 14px;
    }
    
    .search-btn {
        padding: 12px 20px;
        font-size: 14px;
    }
    
    .filter-select, .sort-select {
        padding: 8px 12px;
        font-size: 13px;
    }
}
