/* Enhanced Projects Page Styles */

/* Projects Hero Section */
.projects-hero-section {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    position: relative;
    overflow: hidden;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.projects-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
    background-position: bottom;
}

.projects-hero-content {
    position: relative;
    z-index: 2;
}

.projects-hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.projects-hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.projects-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.projects-stat-item {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 150px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Advanced Filters Section */
.filters-section {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    padding: 3rem 0;
    border-bottom: 1px solid #e9ecef;
}

.filters-header {
    text-align: center;
    margin-bottom: 3rem;
}

.filters-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.filters-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
}

.filter-controls {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.filter-row {
    margin-bottom: 2rem;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: block;
}

.category-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.category-filter-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-filter-btn:hover {
    color: #007bff;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.category-filter-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.category-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
}

.project-count {
    background: rgba(0, 0, 0, 0.1);
    color: inherit;
    padding: 0.1rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

.category-filter-btn.active .project-count {
    background: rgba(255, 255, 255, 0.2);
}

.search-filter {
    position: relative;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.search-input-group:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-icon {
    padding: 0 1rem;
    color: #6c757d;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 0.75rem 0;
    font-size: 1rem;
    background: transparent;
}

.search-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background: #0056b3;
}

.sort-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.sort-select {
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 0.5rem 1rem;
    background: white;
    color: #495057;
    font-weight: 500;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.sort-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 0.25rem;
    border: 1px solid #e9ecef;
}

.view-btn {
    background: transparent;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active {
    background: #007bff;
    color: white;
}

/* Projects Grid */
.projects-section {
    padding: 4rem 0;
    background: #ffffff;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.project-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image {
    transform: scale(1.05);
}

.project-image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #f1f3f4, #e8eaed);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #9aa0a6;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), transparent, rgba(0, 0, 0, 0.7));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-badges {
    position: absolute;
    top: 1rem;
    left: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
    align-items: flex-start;
}

.project-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.featured-badge {
    background: linear-gradient(45deg, #ffd700, #ffb347);
    color: #333;
}

.status-badge {
    background: #28a745;
}

.category-badge {
    background: rgba(0, 0, 0, 0.7);
}

.project-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.project-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.project-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.project-title a:hover {
    color: #007bff;
}

.project-location {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.project-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.85rem;
    color: #495057;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.feature-item i {
    color: #007bff;
}

.project-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 1rem;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.view-details-btn {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.view-details-btn:hover {
    color: white;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.project-meta {
    font-size: 0.8rem;
    color: #6c757d;
}

/* List View */
.projects-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 2rem;
}

.project-card-list {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    height: 200px;
}

.project-card-list:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.project-card-list .project-image-container {
    width: 300px;
    height: 100%;
    flex-shrink: 0;
}

.project-card-list .project-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Pagination */
.pagination-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination {
    margin: 0;
}

.page-link {
    border: none;
    border-radius: 10px;
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: #495057;
    background: white;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background: #007bff;
    border-color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .projects-hero-title {
        font-size: 2.5rem;
    }
    
    .projects-stats {
        justify-content: center;
    }
    
    .projects-stat-item {
        min-width: 120px;
        padding: 1rem;
    }
    
    .filter-controls {
        padding: 1.5rem;
    }
    
    .category-filters {
        justify-content: center;
    }
    
    .sort-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .project-card-list {
        flex-direction: column;
        height: auto;
    }
    
    .project-card-list .project-image-container {
        width: 100%;
        height: 200px;
    }
}

@media (max-width: 576px) {
    .projects-hero-title {
        font-size: 2rem;
    }
    
    .filter-controls {
        padding: 1rem;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .project-content {
        padding: 1.5rem;
    }
    
    .project-features {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .project-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .view-details-btn {
        text-align: center;
        justify-content: center;
    }
}
