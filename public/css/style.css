/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #f5f5f5;
    --text-dark: #000000;
    --text-light: #666666;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --border-color: #e5e5e5;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
    --success-color: #10b981;
    --danger-color: #ef4444;
    --dark: #1a1a1a;
    --text-color: #666666;
    --primary-rgb: 0, 0, 0;
    --secondary-rgb: 255, 255, 255;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    background-color: var(--white);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 400;
    line-height: 1.2;
    color: var(--text-dark);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    font-weight: 400;
    padding: 15px 40px;
    border-radius: 0;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
}

.btn-primary:hover {
    background-color: var(--text-light);
    border-color: var(--text-light);
    color: var(--white);
}

.btn-outline-dark {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    font-weight: 400;
    padding: 15px 40px;
    border-radius: 0;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
}

.btn-outline-dark:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* ===== NAVIGATION ===== */
.transparent-nav {
    background: transparent;
    box-shadow: none;
    padding: 30px 0;
    transition: all 0.3s ease;
    /* backdrop-filter: blur(10px); */
    transform: translateY(0);
}

.transparent-nav.scrolled {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 0;
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
}

.transparent-nav.nav-hidden {
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.transparent-nav.nav-hidden.nav-hover-show {
    transform: translateY(0);
    transition: transform 0.2s ease;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 400;
    color: var(--white) !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.transparent-nav.scrolled .navbar-brand {
    color: var(--primary-color) !important;
}

.brand-logo {
    height: 35px;
    width: auto;
    margin-right: 10px;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1); /* Makes logo white on transparent nav */
}

.transparent-nav.scrolled .brand-logo {
    filter: none; /* Shows original logo colors when scrolled */
}

.navbar-brand:hover .brand-logo {
    transform: scale(1.05);
}

.brand-text {
    font-size: 1.2rem;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    letter-spacing: 1px;
}

.navbar-nav .nav-link {
    color: var(--white) !important;
    font-weight: 400;
    margin: 0 25px;
    padding: 8px 0 !important;
    position: relative;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
}

.transparent-nav.scrolled .navbar-nav .nav-link {
    color: var(--text-dark) !important;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.7) !important;
}

.transparent-nav.scrolled .navbar-nav .nav-link:hover {
    color: var(--text-light) !important;
}

.navbar-nav .nav-link i {
    font-size: 16px;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: 0;
    left: 0;
    background-color: var(--white);
    transition: all 0.3s ease;
}

.transparent-nav.scrolled .navbar-nav .nav-link::after {
    background-color: var(--primary-color);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

.navbar-toggler {
    border: none;
    padding: 4px 8px;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler i {
    color: var(--white);
    font-size: 18px;
}

.transparent-nav.scrolled .navbar-toggler i {
    color: var(--primary-color);
}

/* Call Dropdown */
.call-dropdown {
    min-width: 300px;
    border: none;
    box-shadow: var(--shadow);
    border-radius: 15px;
    padding: 20px;
}

.call-dropdown .dropdown-item {
    padding: 8px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.call-dropdown .dropdown-item:hover {
    background-color: var(--light-gray);
    color: var(--secondary-color);
}

.phone-number {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 600;
    margin-top: 10px;
}

/* Search Bar */
.search-bar {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    box-shadow: var(--shadow);
    padding: 20px 0;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.search-bar.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.search-input-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.search-input-wrapper .form-control {
    border-radius: 50px;
    padding: 12px 20px;
    border: 2px solid var(--border-color);
    font-size: 16px;
}

.search-input-wrapper .btn-close {
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
}

/* ===== SIDEBAR ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 350px;
    max-width: 90vw;
    height: 100vh;
    background: #000000;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
    z-index: 1050;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    border-left: 3px solid var(--primary-color);
    border-left: 3px solid #333;
}

.sidebar.active {
    right: 0;
    visibility: visible;
    transform: translateX(0);
}

.sidebar-header {
    padding: 25px 30px;
    border-bottom: 1px solid #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    color: #ffffff;
    font-weight: 600;
}

.sidebar-logo {
    height: 35px;
    width: auto;
    margin-right: 12px;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1);
}

.sidebar-brand:hover .sidebar-logo {
    transform: scale(1.1) rotate(5deg);
}

.sidebar-content {
    padding: 30px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 8px;
}

.sidebar-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    padding: 15px 20px;
    display: block;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    font-size: 13px;
    border-radius: 8px;
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.sidebar-menu a:hover::before {
    left: 100%;
}

.sidebar-menu a:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #ffffff;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
}

/* Dropdown Menu */
.menu-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dropdown-toggle i {
    transition: transform 0.3s ease;
    font-size: 10px;
}

.menu-dropdown.active .dropdown-toggle i {
    transform: rotate(180deg);
}

.dropdown-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    margin-top: 5px;
    border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-dropdown.active .dropdown-menu {
    max-height: 200px;
    padding: 10px 0;
}

.dropdown-menu li {
    margin-bottom: 0;
}

.dropdown-menu a {
    padding: 8px 25px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 0;
}

.dropdown-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Close Button */
.btn-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
}

.btn-close::before,
.btn-close::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: #ffffff;
    transition: all 0.3s ease;
}

.btn-close::before {
    transform: rotate(45deg);
}

.btn-close::after {
    transform: rotate(-45deg);
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.sidebar-contact {
    margin-top: 20px;
}

.sidebar-divider {
    margin: 25px 0;
    border-color: #333;
    opacity: 0.5;
}

.social-section {
    margin-top: 30px;
    text-align: center;
}

.social-section span {
    font-weight: 600;
    color: #ffffff;
    display: block;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-size: 12px;
}

.social-icons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.social-icons a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
    backdrop-filter: blur(10px);
    padding: 0 !important;
    margin: 0;
    text-transform: none;
    letter-spacing: 0;
}

.social-icons a:hover {
    background: #ffffff;
    color: #000000;
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

body.sidebar-open {
    overflow: hidden;
}

/* Sidebar Active State Animations */
.sidebar.active .sidebar-menu li {
    animation: slideInRight 0.3s ease forwards;
    opacity: 0;
    transform: translateX(30px);
}

.sidebar.active .sidebar-menu li:nth-child(1) { animation-delay: 0.1s; }
.sidebar.active .sidebar-menu li:nth-child(2) { animation-delay: 0.15s; }
.sidebar.active .sidebar-menu li:nth-child(3) { animation-delay: 0.2s; }
.sidebar.active .sidebar-menu li:nth-child(4) { animation-delay: 0.25s; }
.sidebar.active .sidebar-menu li:nth-child(5) { animation-delay: 0.3s; }
.sidebar.active .sidebar-menu li:nth-child(6) { animation-delay: 0.35s; }
.sidebar.active .sidebar-menu li:nth-child(7) { animation-delay: 0.4s; }
.sidebar.active .sidebar-menu li:nth-child(8) { animation-delay: 0.45s; }
.sidebar.active .sidebar-menu li:nth-child(9) { animation-delay: 0.5s; }

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sidebar Brand Animation */
.sidebar.active .sidebar-brand {
    animation: fadeInScale 0.5s ease 0.2s forwards;
    opacity: 0;
    transform: scale(0.8);
}

@keyframes fadeInScale {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Social Icons Animation */
.sidebar.active .social-icons a {
    animation: bounceIn 0.4s ease forwards;
    opacity: 0;
    transform: scale(0);
}

.sidebar.active .social-icons a:nth-child(1) { animation-delay: 0.6s; }
.sidebar.active .social-icons a:nth-child(2) { animation-delay: 0.65s; }
.sidebar.active .social-icons a:nth-child(3) { animation-delay: 0.7s; }
.sidebar.active .social-icons a:nth-child(4) { animation-delay: 0.75s; }

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Debug: Temporarily show sidebar for testing */
/* Remove this after testing */
/*
.sidebar {
    left: 0 !important;
}
.sidebar-overlay {
    opacity: 1 !important;
    visibility: visible !important;
}
*/

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.hero-slide {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
}

/* Page Header Background Styles (excluding hero slider) */
section:not(.hero-section) .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
    
}

/* Ensure page header sections have consistent styling (excluding main hero slider) */
section[class*="-hero-section"]:not(.hero-section) {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

/* Hero content positioning */
.hero-content {
    position: relative;
    z-index: 3;
    width: 100%;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 4rem;
    font-weight: 400;
    margin-bottom: 30px;
    line-height: 1.1;
    color: var(--white);
    font-style: italic;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 50px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-buttons .btn {
    min-width: 180px;
}

.btn-outline-light {
    border: 2px solid var(--white);
    color: var(--white);
    background-color: transparent;
    font-weight: 400;
    padding: 15px 40px;
    border-radius: 0;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
}

.btn-outline-light:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.carousel-indicators {
    bottom: 30px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
}

.carousel-indicators button.active {
    background-color: var(--white);
}

.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    margin: 0 30px;
    transition: all 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== SERVICES SECTION ===== */
.services-section {
    padding: 100px 0;
    background-color: var(--white);
}

.section-header {
    margin-bottom: 80px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 400;
    color: var(--text-dark);
    text-align: center;
    font-style: italic;
    letter-spacing: 2px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.service-card {
    position: relative;
    overflow: hidden;
    border-radius: 0;
    transition: all 0.3s ease;
    background: var(--white);
    box-shadow: var(--shadow);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-image {
    position: relative;
    height: 450px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-content {
    padding: 0;
    background: var(--white);
    text-align: center;
}

.service-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
    font-family: 'Inter', sans-serif;
}

/* ===== PROPERTIES SECTION ===== */
.properties-section {
    padding: 100px 0;
    background: var(--accent-color);
}

.properties-section .section-title {
    opacity: 0;
    transform: translateY(-30px);
    transition: all 0.8s ease;
}

.properties-section.title-visible .section-title {
    opacity: 1;
    transform: translateY(0);
}

.property-showcase {
    margin-top: 80px;
    margin-bottom: 80px;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

.property-showcase.property-hidden {
    opacity: 0;
    transform: translateY(50px);
    margin-bottom: 0;
    height: 0;
    overflow: hidden;
}

.property-showcase.property-visible {
    opacity: 1;
    transform: translateY(0);
    margin-bottom: 80px;
    height: auto;
}

/* Animation states for property content and images */
.property-info {
    opacity: 0;
    transform: translateY(-80px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

.property-image-main {
    opacity: 0;
    transform: translateY(80px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
}

/* Reverse animations for right-to-left layout */
.property-reverse .property-info {
    transform: translateY(80px);
}

.property-reverse .property-image-main {
    transform: translateY(-80px);
}

/* Visible state animations */
.property-showcase.animate-in .property-info {
    opacity: 1;
    transform: translateY(0);
}

.property-showcase.animate-in .property-image-main {
    opacity: 1;
    transform: translateY(0);
}

/* Additional animation effects */
.property-showcase.animate-in .property-number {
    animation: fadeInScale 0.6s ease 0.1s both;
}

.property-showcase.animate-in .property-name {
    animation: slideInFromTop 0.8s ease 0.3s both;
}

.property-showcase.animate-in .property-type {
    animation: fadeInUp 0.6s ease 0.5s both;
}

.property-showcase.animate-in .property-location {
    animation: fadeInUp 0.6s ease 0.6s both;
}

.property-showcase.animate-in .property-description {
    animation: fadeInUp 0.6s ease 0.7s both;
}

.property-showcase.animate-in .property-price {
    animation: slideInFromBottom 0.8s ease 0.8s both;
}

/* Keyframe animations */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Individual element animation trigger */
.animate-element {
    opacity: 1 !important;
    transform: translateY(0) !important;
    transform: scale(1) !important;
}

/* First property visible by default */
.property-showcase:first-of-type .property-info,
.property-showcase:first-of-type .property-image-main {
    opacity: 1;
    transform: translateX(0);
}

.property-info {
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transform: translateY(-80px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

.property-number {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 20px;
    font-weight: 300;
    letter-spacing: 2px;
    opacity: 0;
    transform: scale(0.8);
}

.property-name {
    font-size: 3rem;
    font-weight: 400;
    color: var(--text-dark);
    margin-bottom: 20px;
    line-height: 1.1;
    font-style: italic;
    opacity: 0;
    transform: translateY(-50px);
}

.property-type {
    font-size: 0.9rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 10px;
    font-weight: 500;
    opacity: 0;
    transform: translateY(30px);
}

.property-location {
    font-size: 1rem;
    color: var(--text-light);
    font-weight: 300;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(30px);
}

.property-description {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 30px;
    font-weight: 300;
    opacity: 0;
    transform: translateY(30px);
}

.property-price {
    font-size: 1.5rem;
    color: var(--text-dark);
    font-weight: 600;
    font-family: 'Playfair Display', serif;
    opacity: 0;
    transform: translateY(50px);
}

.property-image-main {
    position: relative;
    height: 500px;
    overflow: hidden;
    opacity: 0;
    transform: translateY(80px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
}

.property-image-main img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.property-showcase:hover .property-image-main img {
    transform: scale(1.02);
}

/* Reverse layout for alternating properties */
.property-reverse .row {
    flex-direction: row-reverse;
}

@media (max-width: 991px) {
    .property-reverse .row {
        flex-direction: column;
    }
}

/* ===== GALLERY SECTION ===== */
.gallery-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--white) 0%, var(--accent-color) 100%);
    position: relative;
    overflow: hidden;
}

.gallery-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,0,0,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
    z-index: 0;
}

.gallery-section .container-fluid {
    position: relative;
    z-index: 1;
}

.gallery-section .section-title {
    opacity: 0;
    transform: translateY(-30px);
    transition: all 0.8s ease;
    animation: titleFadeIn 1s ease-out forwards;
    animation-delay: 0.2s;
}

.gallery-section.title-visible .section-title {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

@keyframes titleFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Gallery Filter Tabs */
.gallery-filters {
    margin: 50px 0;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease 0.2s;
}

.gallery-section.title-visible .gallery-filters {
    opacity: 1;
    transform: translateY(0);
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 30px;
    margin: 0 10px 10px;
    border-radius: 0;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Creative Gallery Layout */
.gallery-creative {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(6, 120px);
    gap: 20px;
    margin-top: 60px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

/* Gallery Item Sizes */
.gallery-large {
    grid-column: span 6;
    grid-row: span 4;
}

.gallery-medium {
    grid-column: span 3;
    grid-row: span 3;
}

.gallery-small {
    grid-column: span 3;
    grid-row: span 2;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    transform: translateY(80px) scale(0.9);
    box-shadow: var(--shadow);
    animation: galleryFadeIn 1s ease-out forwards;
    will-change: opacity, transform;
}

.gallery-item:nth-child(1) { animation-delay: 0.2s; }
.gallery-item:nth-child(2) { animation-delay: 0.4s; }
.gallery-item:nth-child(3) { animation-delay: 0.6s; }
.gallery-item:nth-child(4) { animation-delay: 0.8s; }
.gallery-item:nth-child(5) { animation-delay: 1.0s; }
.gallery-item:nth-child(6) { animation-delay: 1.2s; }

.gallery-item.animate-in {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
}

@keyframes galleryFadeIn {
    from {
        opacity: 0;
        transform: translateY(80px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-hover);
    z-index: 10;
}

.gallery-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
    filter: brightness(0.9);
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.1);
    filter: brightness(1);
}

/* Enhanced Gallery Overlay */
.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 100%);
    color: var(--white);
    opacity: 0;
    transition: all 0.4s ease;
    display: flex;
    align-items: flex-end;
    padding: 30px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.overlay-content {
    transform: translateY(30px);
    transition: all 0.4s ease 0.1s;
}

.gallery-item:hover .overlay-content {
    transform: translateY(0);
}

.property-tag {
    display: inline-block;
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 5px 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    border-radius: 0;
}

.overlay-content h3,
.overlay-content h4,
.overlay-content h5 {
    color: var(--white);
    margin-bottom: 10px;
    font-weight: 400;
    font-style: italic;
}

.overlay-content h3 {
    font-size: 1.8rem;
    line-height: 1.2;
}

.overlay-content h4 {
    font-size: 1.4rem;
}

.overlay-content h5 {
    font-size: 1.2rem;
}

.overlay-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
    font-size: 0.95rem;
    line-height: 1.4;
}

.property-details {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.property-details span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.property-details i {
    font-size: 12px;
}

/* Filter Animation */
.gallery-item.filter-hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.gallery-item.filter-visible {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 100px 0;
    background: var(--white);
    position: relative;
}

/* Split Screen Layout */
.about-split {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

/* Left Side - Content */
.about-content-side {
    padding-right: 40px;
}

.content-wrapper {
    /* opacity: 0; */
    transform: translateX(-50px);
    transition: all 0.8s ease;
    padding: 0 20px;
    max-width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.about-section.animate-in .content-wrapper {
    opacity: 1;
    transform: translateX(0);
}

.section-badgee {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    border-radius: 0;
}

.about-title {
    font-size: 3.2rem;
    font-weight: 400;
    color: var(--text-dark);
    margin-bottom: 30px;
    font-style: italic;
    line-height: 1.1;
}

.about-description {
    margin-bottom: 50px;
}

.about-description p {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.7;
    font-weight: 300;
    margin-bottom: 20px;
}

.about-features {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.feature-item:hover {
    padding-left: 10px;
    border-bottom-color: var(--primary-color);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    background: var(--primary-color);
    transform: scale(1.1);
}

.feature-icon i {
    font-size: 1.2rem;
    color: var(--white);
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon i {
    color: var(--white);
}

.feature-content h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-weight: 600;
}

.feature-content p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.5;
}

.about-cta-left {
    margin-top: 30px;
}

/* Right Side - Statistics Cards */
.about-stats-side {
    position: relative;
}

.stats-container {
    /* opacity: 0; */
    transform: translateX(50px);
    transition: all 0.8s ease 0.2s;
}

.about-section.animate-in .stats-container {
    opacity: 1;
    transform: translateX(0);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

/* Creative Statistics Cards */
.stat-card {
    position: relative;
    padding: 40px 30px;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateY(0);
    opacity: 1;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Removed duplicate - using enhanced version below */

.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
    border-color: #555;
}

/* Card Background Patterns */
.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.card-pattern {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    opacity: 0.05;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 2px, transparent 2px);
    background-size: 25px 25px;
    animation: patternMove 25s linear infinite;
}

@keyframes patternMove {
    0% { transform: rotate(0deg) translate(0, 0); }
    100% { transform: rotate(360deg) translate(20px, 20px); }
}

/* Card Color Themes - Black Background with White Text */
.card-blue,
.card-green,
.card-orange,
.card-purple,
.card-dark {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--white);
    border: 1px solid #333;
}

/* Dark card specific styling to match screenshot */
.card-dark {
    background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
    border: 1px solid #444;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.card-dark:hover {
    background: linear-gradient(135deg, #333 0%, #222 100%);
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Card Content */
.card-content {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 20px 0;
}

.stat-number {
    font-size: 4.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
    display: block;
    transition: all 0.4s ease;
    line-height: 1;
}

.stat-card:hover .stat-number {
    transform: scale(1.05);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    line-height: 1.4;
}

/* Card Animation Delays */
.stat-card:nth-child(1) { transition-delay: 0.1s; }
.stat-card:nth-child(2) { transition-delay: 0.2s; }
.stat-card:nth-child(3) { transition-delay: 0.3s; }
.stat-card:nth-child(4) { transition-delay: 0.4s; }

/* ===== INDEX PAGE ABOUT US SECTION - SEPARATE STYLES ===== */
.index-about-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
}

.index-about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 0;
}

.index-about-split {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.index-about-content-side {
    padding-right: 40px;
}

.index-content-wrapper {
    max-width: 600px;
}

.index-section-badge {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 25px;
    text-transform: uppercase;
}

.index-about-title {
    font-size: 3.2rem;
    font-weight: 400;
    color: var(--dark);
    margin-bottom: 30px;
    line-height: 1.2;
    font-style: italic;
    letter-spacing: 2px;
    font-family: 'Playfair Display', serif;
}

.index-about-description {
    margin-bottom: 40px;
}

.index-about-description p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color);
    margin-bottom: 20px;
}

.index-about-features {
    margin-bottom: 40px;
}

.index-feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.index-feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.index-feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.index-feature-icon i {
    font-size: 1.5rem;
}

.index-feature-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 8px;
}

.index-feature-content p {
    font-size: 1rem;
    color: var(--text-color);
    margin: 0;
    line-height: 1.6;
}

.index-about-cta {
    margin-top: 30px;
}

/* Index Statistics Cards */
.index-about-stats-side {
    position: relative;
}

.index-stats-container {
    position: relative;
    z-index: 1;
}

.index-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.index-stat-card {
    position: relative;
    padding: 40px 30px;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateY(50px);
    opacity: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Removed - using animation keyframes instead */

.index-stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Index Card Background Patterns */
.index-card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.index-card-pattern {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    opacity: 0.05;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 2px, transparent 2px);
    background-size: 25px 25px;
    animation: patternMove 25s linear infinite;
}

/* Index Card Dark Theme */
.index-card-dark {
    background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
    color: var(--white);
    border: 1px solid #444;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.index-card-dark:hover {
    background: linear-gradient(135deg, #333 0%, #222 100%);
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Index Card Content */
.index-card-content {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 20px 0;
}

.index-stat-number {
    font-size: 4.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
    display: block;
    transition: all 0.4s ease;
    line-height: 1;
}

.index-stat-card:hover .index-stat-number {
    transform: scale(1.05);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.index-stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.index-stat-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    line-height: 1.4;
}

/* Index Card Animation Delays */
.index-stat-card:nth-child(1) {
    transition-delay: 0.1s;
    animation-delay: 0.1s;
}
.index-stat-card:nth-child(2) {
    transition-delay: 0.2s;
    animation-delay: 0.2s;
}
.index-stat-card:nth-child(3) {
    transition-delay: 0.3s;
    animation-delay: 0.3s;
}
.index-stat-card:nth-child(4) {
    transition-delay: 0.4s;
    animation-delay: 0.4s;
}

/* Index Stats Animation Keyframes */
@keyframes indexStatSlideUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes indexStatPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Index Stats Floating Animation */
@keyframes indexStatFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.index-stat-card.animate-in {
    animation: indexStatSlideUp 0.6s ease-out forwards, indexStatFloat 4s ease-in-out infinite 1s;
}

.index-stat-card.animate-in .index-stat-number {
    animation: indexStatPulse 2s ease-in-out infinite;
}

/* Enhanced Hover Effects */
.index-stat-card:hover .index-card-pattern {
    animation-duration: 15s;
    opacity: 0.1;
}

.index-stat-card:hover .index-stat-label {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

.index-stat-card:hover .index-stat-description {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
    color: rgba(255, 255, 255, 1);
}

/* Responsive Design for Index About Section */
@media (max-width: 991px) {
    .index-about-split {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .index-about-content-side {
        padding-right: 0;
    }

    .index-about-title {
        font-size: 2.8rem;
    }

    .index-stats-grid {
        gap: 20px;
    }

    .index-stat-card {
        padding: 30px 20px;
    }

    .index-stat-number {
        font-size: 3.5rem;
    }

    .index-feature-item {
        text-align: left;
    }
}

@media (max-width: 768px) {
    .index-about-section {
        padding: 80px 0;
    }

    .index-about-title {
        font-size: 2.4rem;
    }

    .index-stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .index-stat-card {
        padding: 35px 25px;
        margin-bottom: 20px;
    }

    .index-stat-number {
        font-size: 3.2rem;
    }

    .index-stat-label {
        font-size: 1rem;
    }

    .index-feature-item {
        flex-direction: column;
        text-align: center;
    }

    .index-feature-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
}

/* ===== INDEX PAGE FEATURED PROPERTIES SECTION - SEPARATE STYLES ===== */
.index-properties-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.index-properties-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23000" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: 0;
}

.index-section-header {
    margin-bottom: 50px;
    position: relative;
    z-index: 1;
}

.index-section-title {
    font-size: 3.5rem;
    font-weight: 400;
    color: var(--dark);
    margin-bottom: 20px;
    font-style: italic;
    font-family: 'Playfair Display', serif;
    position: relative;
}

.index-section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.index-property-showcase {
    margin-bottom: 60px;
    position: relative;
    z-index: 1;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.index-property-showcase .row {
    display: flex;
    align-items: center;
}

.index-property-showcase.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.index-property-showcase.index-property-hidden {
    display: none;
}

.index-property-showcase.index-property-visible {
    display: block;
}

.index-property-info {
    padding: 30px;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    transition: all 0.4s ease;
}

.index-property-info:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.index-property-number {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    letter-spacing: 2px;
    margin-bottom: 15px;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.6s ease 0.2s;
}

.index-property-showcase.animate-in .index-property-number {
    opacity: 1;
    transform: translateX(0);
}

.index-property-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 12px;
    font-family: 'Playfair Display', serif;
    line-height: 1.2;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.6s ease 0.4s;
}

.index-property-showcase.animate-in .index-property-name {
    opacity: 1;
    transform: translateX(0);
}

.index-property-type {
    font-size: 1rem;
    font-weight: 500;
    color: var(--secondary-color);
    letter-spacing: 1px;
    margin-bottom: 10px;
    text-transform: uppercase;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.6s ease 0.6s;
}

.index-property-showcase.animate-in .index-property-type {
    opacity: 1;
    transform: translateX(0);
}

.index-property-location {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.6s ease 0.8s;
}

.index-property-showcase.animate-in .index-property-location {
    opacity: 1;
    transform: translateX(0);
}

.index-property-location::before {
    content: '\f3c5';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    color: var(--primary-color);
}

.index-property-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 20px;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.6s ease 1s;
}

.index-property-showcase.animate-in .index-property-description {
    opacity: 1;
    transform: translateX(0);
}

.index-property-price {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Playfair Display', serif;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.6s ease 1.2s;
}

.index-property-showcase.animate-in .index-property-price {
    opacity: 1;
    transform: translateX(0);
}

.index-property-image-main {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    width: 100%;
    height: auto;
}

.index-property-image-main:hover {
    transform: scale(1.02);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
}

.index-property-image-main img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: all 0.4s ease;
}

.index-property-image-main:hover img {
    transform: scale(1.05);
}

/* Index Property Reverse Layout */
.index-property-reverse .row {
    flex-direction: row-reverse;
}

/* Ensure proper spacing for reverse layout */
.index-property-reverse .index-property-info {
    text-align: left;
}

/* Fix any potential margin issues */
.index-property-showcase .col-lg-6 {
    margin-bottom: 0;
}

@media (max-width: 991px) {
    .index-property-reverse .row {
        flex-direction: column;
    }

    .index-property-showcase .col-lg-6 {
        margin-bottom: 30px;
    }

    .index-property-showcase .col-lg-6:last-child {
        margin-bottom: 0;
    }
}

/* Index Properties Responsive Design */
@media (max-width: 991px) {
    .index-properties-section {
        padding: 60px 0;
    }

    .index-section-title {
        font-size: 2.4rem;
    }

    .index-property-showcase {
        margin-bottom: 40px;
    }

    .index-property-info {
        padding: 25px;
        margin-bottom: 25px;
    }

    .index-property-name {
        font-size: 1.8rem;
    }

    .index-property-price {
        font-size: 1.4rem;
    }

    .index-property-image-main img {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .index-properties-section {
        padding: 40px 0;
    }

    .index-section-header {
        margin-bottom: 30px;
    }

    .index-section-title {
        font-size: 2rem;
    }

    .index-property-showcase {
        margin-bottom: 30px;
    }

    .index-property-info {
        padding: 20px;
    }

    .index-property-name {
        font-size: 1.6rem;
    }

    .index-property-description {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .index-property-price {
        font-size: 1.3rem;
    }

    .index-property-image-main img {
        height: 200px;
    }
}

/* Index Properties Animation Delays */
.index-property-showcase:nth-child(2) { transition-delay: 0.2s; }
.index-property-showcase:nth-child(3) { transition-delay: 0.4s; }
.index-property-showcase:nth-child(4) { transition-delay: 0.6s; }

/* ===== PROPERTIES PAGE STYLES ===== */
.properties-hero-section {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('images/real1.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    padding: 150px 0 100px;
    color: var(--white);
    position: relative;
}

.properties-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
    z-index: 1;
}

.properties-hero-section .hero-content {
    position: relative;
    z-index: 2;
}

.properties-hero-section .hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 20px;
    font-family: 'Playfair Display', serif;
}

.properties-hero-section .hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Properties Filter Section */
.properties-filter-section {
    padding: 60px 0;
    background: var(--light-gray);
}

.filter-controls {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-btn {
    background: var(--white);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* All Properties Section */
.all-properties-section {
    padding: 80px 0;
    background: var(--white);
}

.property-showcase {
    margin-bottom: 80px;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

.property-showcase.property-reverse .row {
    flex-direction: row-reverse;
}

.property-info {
    padding: 40px;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
}

.property-info:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.property-number {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    letter-spacing: 2px;
    margin-bottom: 15px;
}

.property-name {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 12px;
    font-family: 'Playfair Display', serif;
    line-height: 1.2;
}

.property-type {
    font-size: 1rem;
    font-weight: 500;
    color: var(--secondary-color);
    letter-spacing: 1px;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.property-location {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.property-location::before {
    content: '\f3c5';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    color: var(--primary-color);
}

.property-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 20px;
}

.property-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Playfair Display', serif;
    margin-bottom: 20px;
}

.property-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
}

.property-features span {
    background: var(--light-gray);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.property-features span i {
    margin-right: 5px;
    color: var(--primary-color);
}

.property-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.property-image-main {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
}

.property-image-main:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.property-image-main img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: all 0.4s ease;
}

.property-image-main:hover img {
    transform: scale(1.05);
}

.property-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 2;
}

.property-badge.luxury {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: var(--dark);
}

.property-badge.new {
    background: linear-gradient(135deg, #10b981, #34d399);
}

.property-badge.commercial {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.property-badge.premium {
    background: linear-gradient(135deg, #8b5cf6, #a78bfa);
}

/* Properties CTA Section */
.properties-cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    text-align: center;
}

.properties-cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    font-family: 'Playfair Display', serif;
}

.properties-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Properties Page Responsive Design */
@media (max-width: 991px) {
    .properties-hero-section {
        padding: 120px 0 80px;
    }

    .properties-hero-section .hero-title {
        font-size: 3rem;
    }

    .filter-controls {
        justify-content: center;
    }

    .property-showcase {
        margin-bottom: 60px;
    }

    .property-showcase.property-reverse .row {
        flex-direction: column;
    }

    .property-info {
        padding: 30px;
        margin-bottom: 30px;
    }

    .property-name {
        font-size: 2rem;
    }

    .property-price {
        font-size: 1.6rem;
    }

    .property-image-main img {
        height: 300px;
    }

    .property-actions {
        justify-content: center;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 768px) {
    .properties-hero-section {
        padding: 100px 0 60px;
        background-attachment: scroll;
    }

    .properties-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .properties-hero-section .hero-subtitle {
        font-size: 1.1rem;
    }

    .properties-filter-section {
        padding: 40px 0;
    }

    .filter-controls {
        gap: 10px;
    }

    .filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .all-properties-section {
        padding: 60px 0;
    }

    .property-showcase {
        margin-bottom: 50px;
    }

    .property-info {
        padding: 25px;
    }

    .property-name {
        font-size: 1.8rem;
    }

    .property-description {
        font-size: 0.95rem;
    }

    .property-price {
        font-size: 1.5rem;
    }

    .property-features {
        gap: 10px;
    }

    .property-features span {
        padding: 6px 12px;
        font-size: 0.85rem;
    }

    .property-image-main img {
        height: 250px;
    }

    .property-actions {
        flex-direction: column;
    }

    .properties-cta-section {
        padding: 60px 0;
    }

    .properties-cta-section h2 {
        font-size: 2rem;
    }

    .properties-cta-section p {
        font-size: 1rem;
    }
}

/* ===== MODERN WHY CHOOSE US FEATURES SECTION ===== */
.modern-features-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #202020 0%, #ffffff 50%, #202020 100%);
    position: relative;
    overflow: hidden;
}

.modern-features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="modernGrid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%23000" stroke-width="0.5" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23modernGrid)"/></svg>');
    z-index: 0;
}

/* Modern Header */
.modern-header {
    position: relative;
    z-index: 2;
    margin-bottom: 80px;
}

.floating-elements {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 100px;
    z-index: -1;
}

.float-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--secondary-rgb), 0.1));
    animation: float 6s ease-in-out infinite;
}

.float-1 {
    width: 60px;
    height: 60px;
    top: 0;
    left: 0;
    animation-delay: 0s;
}

.float-2 {
    width: 40px;
    height: 40px;
    top: 20px;
    right: 0;
    animation-delay: 2s;
}

.float-3 {
    width: 30px;
    height: 30px;
    bottom: 0;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.section-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    margin-bottom: 20px;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modern-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 20px;
    font-family: 'Playfair Display', serif;
    line-height: 1.2;
}

.modern-subtitle {
    font-size: 1.2rem;
    color: var(--text-color);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Timeline Features */
.timeline-features {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding: 60px 0;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
}

.timeline-item {
    position: relative;
    margin-bottom: 80px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 1s ease-out;
    will-change: opacity, transform;
}

.timeline-item.animate-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Force animation for debugging */
.modern-features-section .timeline-item {
    animation: fadeInUp 1s ease-out forwards;
}

.modern-features-section .timeline-item:nth-child(1) { animation-delay: 0.2s; }
.modern-features-section .timeline-item:nth-child(2) { animation-delay: 0.6s; }
.modern-features-section .timeline-item:nth-child(3) { animation-delay: 1.0s; }
.modern-features-section .timeline-item:nth-child(4) { animation-delay: 1.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.timeline-item::before {
    content: attr(data-number);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    z-index: 3;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 4px solid var(--white);
}

.timeline-left .timeline-content {
    margin-right: 60%;
    text-align: right;
}

.timeline-right .timeline-content {
    margin-left: 60%;
    text-align: left;
}

.timeline-content {
    background: var(--white);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: all 0.4s ease;
}

.timeline-content:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.timeline-left .timeline-content::after {
    content: '';
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid var(--white);
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
}

.timeline-right .timeline-content::after {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-right: 15px solid var(--white);
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
}

.feature-icon-modern {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color));
    color: var(--white);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    font-size: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.timeline-left .feature-icon-modern {
    margin-left: auto;
}

.timeline-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.timeline-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: 25px;
}

.feature-stats {
    display: flex;
    gap: 20px;
    justify-content: flex-start;
}

.timeline-left .feature-stats {
    justify-content: flex-end;
}

.stat-item {
    text-align: center;
    padding: 15px 20px;
    background: white;
    border-radius: 15px;
    min-width: 100px;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Playfair Display', serif;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* Features Showcase Section */
.features-showcase-section {
    margin: 80px 0;
    background: var(--white);
    border-radius: 25px;
    padding: 60px 40px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 2;
}

.features-showcase-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark);
    text-align: center;
    margin-bottom: 50px;
    font-family: 'Playfair Display', serif;
    position: relative;
}

.features-showcase-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.features-showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-showcase-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 20px;
    padding: 35px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.feature-showcase-card:nth-child(1) { animation-delay: 0.2s; }
.feature-showcase-card:nth-child(2) { animation-delay: 0.4s; }
.feature-showcase-card:nth-child(3) { animation-delay: 0.6s; }
.feature-showcase-card:nth-child(4) { animation-delay: 0.8s; }
.feature-showcase-card:nth-child(5) { animation-delay: 1.0s; }
.feature-showcase-card:nth-child(6) { animation-delay: 1.2s; }

.feature-showcase-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.feature-showcase-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.feature-showcase-card:hover::before {
    transform: scaleX(1);
}

.feature-showcase-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color));
    color: var(--white);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-showcase-card:hover .feature-showcase-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.feature-showcase-content h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.feature-showcase-content p {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: 20px;
}

.feature-highlight {
    display: inline-block;
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--secondary-rgb), 0.1));
    color: var(--primary-color);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(var(--primary-rgb), 0.2);
}

/* Trust Indicators */
.trust-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.trust-item {
    background: var(--white);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
    will-change: opacity, transform;
}

.trust-item:nth-child(1) { animation-delay: 1.8s; }
.trust-item:nth-child(2) { animation-delay: 2.0s; }
.trust-item:nth-child(3) { animation-delay: 2.2s; }
.trust-item:nth-child(4) { animation-delay: 2.4s; }

.trust-item.animate-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

.trust-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.trust-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.trust-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--primary-rgb), 0.1));
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.trust-item:hover .trust-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color));
    color: var(--white);
    transform: scale(1.1);
}

.trust-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 8px;
}

.trust-content p {
    font-size: 1rem;
    color: var(--text-color);
    margin: 0;
}

/* Modern Features Responsive Design */
@media (max-width: 991px) {
    .modern-features-section {
        padding: 80px 0;
    }

    .modern-title {
        font-size: 2.8rem;
    }

    .timeline-line {
        left: 30px;
    }

    .timeline-item::before {
        left: 30px;
    }

    .timeline-left .timeline-content,
    .timeline-right .timeline-content {
        margin-left: 80px;
        margin-right: 0;
        text-align: left;
    }

    .timeline-left .timeline-content::after,
    .timeline-right .timeline-content::after {
        left: -15px;
        right: auto;
        border-right: 15px solid var(--white);
        border-left: none;
    }

    .timeline-left .feature-icon-modern {
        margin-left: 0;
    }

    .timeline-left .feature-stats {
        justify-content: flex-start;
    }

    .features-showcase-section {
        padding: 40px 25px;
    }

    .features-showcase-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .feature-showcase-card {
        padding: 30px 25px;
    }

    .trust-indicators {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .modern-features-section {
        padding: 60px 0;
    }

    .modern-title {
        font-size: 2.2rem;
    }

    .modern-subtitle {
        font-size: 1rem;
    }

    .timeline-content {
        padding: 25px;
    }

    .timeline-content h3 {
        font-size: 1.5rem;
    }

    .timeline-content p {
        font-size: 1rem;
    }

    .feature-stats {
        flex-direction: column;
        gap: 15px;
    }

    .stat-item {
        min-width: auto;
    }

    .features-showcase-title {
        font-size: 2rem;
    }

    .features-showcase-section {
        padding: 30px 20px;
    }

    .feature-showcase-card {
        padding: 25px 20px;
    }

    .feature-showcase-content h4 {
        font-size: 1.2rem;
    }

    .feature-showcase-content p {
        font-size: 0.95rem;
    }

    .trust-item {
        padding: 25px 20px;
    }
}

/* Modern CTA Section */
.modern-cta-section {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333333 100%);
    border-radius: 25px;
    padding: 60px 40px;
    margin: 60px 0;
    text-align: center;
    overflow: hidden;
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.cta-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="ctaPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23ctaPattern)"/></svg>');
    animation: patternMove 30s linear infinite;
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 20px;
    text-transform: uppercase;
    backdrop-filter: blur(10px);
}

.modern-cta-section h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.modern-cta-section p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.modern-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    overflow: hidden;
    z-index: 1;
}

.modern-btn.primary {
    background: var(--white);
    color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.modern-btn.primary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.4);
}

.modern-btn.secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.modern-btn.secondary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modern-btn:hover .btn-glow {
    left: 100%;
}

.cta-guarantee {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
}

.cta-guarantee i {
    color: #10b981;
    font-size: 1.1rem;
}

/* Modern CTA Responsive */
@media (max-width: 768px) {
    .modern-cta-section {
        padding: 40px 25px;
        margin: 40px 0;
    }

    .modern-cta-section h3 {
        font-size: 2rem;
    }

    .modern-cta-section p {
        font-size: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .modern-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

/* Responsive Design for About Section */
@media (max-width: 991px) {
    .about-split {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .about-content-side {
        padding-right: 0;
        text-align: center;
    }

    .about-title {
        font-size: 2.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .stat-card {
        padding: 30px 20px;
    }

    .stat-number {
        font-size: 3.5rem;
    }

    .feature-item {
        text-align: left;
    }
}

@media (max-width: 767px) {
    .about-section {
        padding: 80px 0;
    }

    .about-split {
        gap: 50px;
    }

    .about-title {
        font-size: 2.2rem;
    }

    .about-description p {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stat-card {
        padding: 35px 25px;
        margin-bottom: 20px;
    }

    .stat-number {
        font-size: 3.2rem;
    }

    .stat-label {
        font-size: 1rem;
    }

    .feature-item {
        padding: 15px 0;
        margin-bottom: 20px;
    }

    .feature-icon {
        width: 45px;
        height: 45px;
        margin-right: 15px;
    }

    .feature-content h4 {
        font-size: 1.1rem;
    }

    .about-cta-left {
        text-align: center;
    }
}

/* Responsive Design for About Section */
@media (max-width: 991px) {
    .about-split {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .about-content-side {
        padding-right: 0;
        order: 2;
    }

    .about-visual-side {
        order: 1;
    }

    .about-title {
        font-size: 2.8rem;
        text-align: center;
    }

    .intro-text {
        font-size: 1.1rem;
        text-align: center;
    }

    .main-visual {
        height: 400px;
    }

    .overlay-stats {
        flex-direction: row;
        gap: 20px;
        flex-wrap: wrap;
    }

    .floating-stat {
        padding: 15px 20px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .cta-content h3 {
        font-size: 2rem;
    }
}

@media (max-width: 767px) {
    .about-section {
        padding: 80px 0;
    }

    .about-split {
        gap: 40px;
    }

    .about-title {
        font-size: 2.2rem;
    }

    .intro-text {
        font-size: 1rem;
    }

    .content-card {
        margin-bottom: 15px;
    }

    .card-header {
        padding: 20px;
    }

    .card-header h3 {
        font-size: 1.1rem;
    }

    .card-content {
        padding: 0 20px;
    }

    .content-card.active .card-content {
        padding: 20px;
    }

    .main-visual {
        height: 300px;
    }

    .overlay-stats {
        flex-direction: column;
        gap: 15px;
    }

    .floating-stat {
        padding: 12px 16px;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-text {
        font-size: 0.8rem;
    }

    .about-cta {
        margin-top: 60px;
        padding: 40px 20px;
    }

    .cta-content h3 {
        font-size: 1.8rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    .decorative-elements {
        display: none;
    }
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 120px 0;
    background: linear-gradient(135deg, var(--white) 0%, var(--accent-color) 100%);
    position: relative;
    overflow: hidden;
}

/* Animated Background */
.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 50%;
    animation: floatShape 15s infinite ease-in-out;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: -3s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 15%;
    animation-delay: -6s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    right: 20%;
    animation-delay: -9s;
}

.shape-5 {
    width: 90px;
    height: 90px;
    top: 50%;
    left: 50%;
    animation-delay: -12s;
}

@keyframes floatShape {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-30px) rotate(90deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-60px) rotate(180deg);
        opacity: 0.3;
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
        opacity: 0.6;
    }
}

.container {
    position: relative;
    z-index: 1;
}

/* Creative Header */
.contact-header {
    margin-bottom: 80px;
    opacity: 1;
    transform: translateY(-50px);
    transition: all 0.8s ease;
}

.contact-section.animate-in .contact-header {
    opacity: 1;
    transform: translateY(0);
}

.header-badge {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 10px 25px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 30px;
    border-radius: 0;
}

.contact-title {
    font-size: 3.5rem;
    font-weight: 400;
    color: var(--text-dark);
    margin-bottom: 30px;
    line-height: 1.1;
    font-style: italic;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateX(-30px);
    animation: slideInTitle 0.8s ease forwards;
}

.title-highlight {
    display: block;
    color: var(--primary-color);
    opacity: 0;
    transform: scale(0.8);
    animation: scaleInTitle 0.8s ease forwards;
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-highlight { animation-delay: 0.5s; }
.title-line:nth-child(3) { animation-delay: 0.8s; }

@keyframes slideInTitle {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleInTitle {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.contact-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    font-weight: 300;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Interactive Contact Methods */
.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 100px;
    /* opacity: 0; */
    transform: translateY(50px);
    transition: all 0.8s ease 0.3s;
}

.contact-section.animate-in .contact-methods {
    opacity: 1;
    transform: translateY(0);
}

.method-card {
    position: relative;
    background: var(--white);
    padding: 40px 30px;
    text-align: center;
    border-radius: 0;
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: all 0.4s ease;
    overflow: hidden;
    transform: translateY(20px);
    opacity: 1;
}

.method-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.method-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.method-hover-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,0,0,0.05), transparent);
    transition: all 0.6s ease;
}

.method-card:hover .method-hover-effect {
    left: 100%;
}

.method-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    transition: all 0.4s ease;
}

.method-card:hover .method-icon {
    background: var(--primary-color);
    transform: scale(1.1) rotate(5deg);
}

.method-icon i {
    font-size: 1.8rem;
    color: var(--text-dark);
    transition: all 0.4s ease;
}

.method-card:hover .method-icon i {
    color: var(--white);
}

.method-content h4 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
}

.method-content p {
    color: var(--text-light);
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.method-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.method-card:hover .method-link {
    color: var(--text-dark);
}

/* Method Card Animation Delays */
.method-card:nth-child(1) { transition-delay: 0.1s; }
.method-card:nth-child(2) { transition-delay: 0.2s; }
.method-card:nth-child(3) { transition-delay: 0.3s; }
.method-card:nth-child(4) { transition-delay: 0.4s; }

/* Enhanced Contact Form */
.contact-form-section {
    margin-bottom: 80px;
    transform: translateY(50px);
    transition: all 0.8s ease 0.5s;
}

.contact-section.animate-in .contact-form-section {
    opacity: 1;
    transform: translateY(0);
}

.form-container {
    background: var(--white);
    padding: 60px 50px;
    border-radius: 0;
    box-shadow: var(--shadow-hover);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--text-dark));
}

.form-header {
    text-align: center;
    margin-bottom: 50px;
}

.form-header h3 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 400;
    font-style: italic;
}

.form-header p {
    color: var(--text-light);
    font-size: 1rem;
    margin: 0;
}

/* Form Layout */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.form-group {
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

/* Ensure consistent spacing for location and budget fields */
.form-row .form-group .select-wrapper {
    margin-bottom: 0;
}

/* Location field specific styling */
.form-group select[name="location"],
.form-group select[name="budget"] {
    width: 100%;
}

/* Select field validation states */
.form-select.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
}

.form-select.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25);
}

/* Filled state for select wrappers */
.select-wrapper.filled .form-label {
    transform: translateY(-25px) scale(0.85);
    color: var(--primary-color);
}

.select-wrapper.filled .input-line {
    transform: scaleX(1);
    background: var(--primary-color);
}

/* Error states for form wrappers */
.input-wrapper.error .form-input,
.select-wrapper.error .form-select,
.textarea-wrapper.error .form-textarea {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25);
}

.input-wrapper.error .form-label,
.select-wrapper.error .form-label,
.textarea-wrapper.error .form-label {
    color: var(--danger-color);
}

.input-wrapper.error .input-line,
.select-wrapper.error .input-line,
.textarea-wrapper.error .input-line {
    background: var(--danger-color);
}

/* Modern Input Styling */
.input-wrapper,
.select-wrapper,
.textarea-wrapper {
    position: relative;
    margin-bottom: 30px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 20px 0 10px;
    border: none;
    border-bottom: 2px solid var(--border-color);
    background: transparent;
    font-size: 16px;
    color: var(--text-dark);
    transition: all 0.3s ease;
    outline: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-bottom-color: var(--primary-color);
}

.form-label {
    position: absolute;
    top: 20px;
    left: 0;
    font-size: 16px;
    color: var(--text-light);
    pointer-events: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    font-size: 12px;
}

.form-input:focus + .form-label,
.form-input:valid + .form-label,
.form-select:focus + .form-label,
.form-select:valid + .form-label,
.form-textarea:focus + .form-label,
.form-textarea:valid + .form-label {
    top: 0;
    font-size: 12px;
    color: var(--primary-color);
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
}

.form-input:focus ~ .input-line,
.form-select:focus ~ .input-line,
.form-textarea:focus ~ .input-line {
    width: 100%;
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* Submit Button */
.form-submit {
    text-align: center;
    margin-top: 40px;
}

.submit-btn {
    position: relative;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 18px 50px;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 0;
}

.submit-btn:hover {
    background: var(--text-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-text {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.btn-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%) translateX(30px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 2;
}

.submit-btn:hover .btn-icon {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
}

.submit-btn:hover .btn-text {
    transform: translateX(-10px);
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.submit-btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

/* Contact Footer */
.contact-footer {
    opacity: 1;
    transform: translateY(30px);
    transition: all 0.8s ease 0.7s;
}

.contact-section.animate-in .contact-footer {
    opacity: 1;
    transform: translateY(0);
}

.office-hours,
.social-section {
    text-align: center;
}

.office-hours h4,
.social-section h4 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hours-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--white);
    border-radius: 0;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.hours-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.day {
    font-weight: 600;
    color: var(--text-dark);
}

.time {
    color: var(--primary-color);
    font-weight: 500;
}

/* Social Links */
.social-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    max-width: 400px;
    margin: 0 auto;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 18px 20px;
    background: var(--white);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 8px;
    box-shadow: var(--shadow);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    min-height: 60px;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 0;
}

.social-link:hover::before {
    left: 0;
}

.social-link:hover {
    color: var(--white);
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.social-link i {
    font-size: 1.4rem;
    /* margin-right: 12px; */
    position: relative;
    z-index: 1;
    width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.social-link:hover i {
    transform: scale(1.1);
}

.social-link span {
    font-weight: 600;
    position: relative;
    z-index: 1;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
}

/* Platform-specific colors */
.social-link[data-platform="instagram"]:hover::before {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
}

.social-link[data-platform="linkedin"]:hover::before {
    background: #0077b5;
}

.social-link[data-platform="youtube"]:hover::before {
    background: #ff0000;
}

.social-link[data-platform="facebook"]:hover::before {
    background: #1877f2;
}

/* Icon-only social links */
.social-links-icons-only {
    display: flex !important;
    justify-content: center;
    gap: 20px;
    max-width: none;
    margin: 0 auto;
}

.social-link-icon {
    width: 60px;
    height: 60px;
    border-radius: 50% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 !important;
    min-height: auto !important;
    background: var(--white);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: scale(0);
    transition: all 0.3s ease;
    z-index: 0;
}

.social-link-icon:hover::before {
    transform: scale(1);
    left: 0;
}

.social-link-icon:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: var(--white);
}

.social-link-icon i {
    font-size: 1.5rem;
    margin-right: 0 !important;
    position: relative;
    z-index: 1;
    width: auto;
    text-align: center;
    transition: all 0.3s ease;
}

.social-link-icon:hover i {
    transform: scale(1.2);
    color: var(--white);
}

/* Responsive Design for Contact Section */
@media (max-width: 991px) {
    .contact-title {
        font-size: 2.8rem;
    }

    .contact-methods {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .form-container {
        padding: 40px 30px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .social-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        max-width: 350px;
    }

    .social-links-icons-only {
        gap: 15px;
    }

    .social-link {
        padding: 15px 15px;
        min-height: 55px;
    }

    .social-link-icon {
        width: 55px;
        height: 55px;
    }

    .social-link-icon i {
        font-size: 1.3rem;
    }

    .social-link i {
        font-size: 1.3rem;
        margin-right: 10px;
    }

    .social-link span {
        font-size: 0.9rem;
    }
}

@media (max-width: 767px) {
    .contact-section {
        padding: 80px 0;
    }

    .contact-title {
        font-size: 2.2rem;
    }

    .contact-methods {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 60px;
    }

    .method-card {
        padding: 30px 20px;
    }

    .form-container {
        padding: 30px 20px;
    }

    .form-header h3 {
        font-size: 1.6rem;
    }

    .submit-btn {
        width: 100%;
        padding: 16px 40px;
    }

    .hours-item,
    .social-link {
        padding: 12px 15px;
    }

    .social-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        max-width: 100%;
    }

    .social-links-icons-only {
        gap: 12px;
    }

    .social-link {
        padding: 14px 12px;
        min-height: 50px;
        justify-content: center;
    }

    .social-link-icon {
        width: 50px;
        height: 50px;
    }

    .social-link-icon i {
        font-size: 1.2rem;
    }

    .social-link i {
        font-size: 1.2rem;
        margin-right: 8px;
        width: 20px;
    }

    .social-link span {
        font-size: 0.85rem;
    }

    .floating-shapes .shape {
        display: none;
    }

    /* Logo responsive adjustments */
    .brand-logo {
        height: 30px;
        margin-right: 8px;
    }

    .sidebar-logo {
        height: 25px;
        margin-right: 8px;
    }

    /* Extra responsive styles for sidebar on small devices */
    .sidebar {
        width: 260px;
        max-width: 90vw;
    }

    .sidebar-header {
        padding: 15px 20px;
    }

    .sidebar-content {
        padding: 20px;
    }

    .sidebar-brand {
        font-size: 1rem;
    }

    .sidebar-menu a {
        padding: 10px 12px;
        font-size: 11px;
        letter-spacing: 0.8px;
    }

    .social-section span {
        font-size: 11px;
        margin-bottom: 15px;
    }

    .social-icons {
        gap: 10px;
    }

    .social-icons a {
        width: 35px;
        height: 35px;
        font-size: 13px;
    }

    .btn-close {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .btn-close::before,
    .btn-close::after {
        width: 16px;
    }
}

/* Extra Small Devices (phones, 480px and down) */
@media (max-width: 480px) {
    .sidebar {
        width: 240px;
        max-width: 95vw;
    }

    .sidebar-header {
        padding: 12px 15px;
    }

    .sidebar-content {
        padding: 15px;
    }

    .sidebar-brand {
        font-size: 0.9rem;
    }

    .sidebar-logo {
        height: 22px;
        margin-right: 6px;
    }

    .sidebar-menu a {
        padding: 8px 10px;
        font-size: 10px;
        letter-spacing: 0.5px;
    }

    .social-section span {
        font-size: 10px;
        margin-bottom: 12px;
    }

    .social-icons {
        gap: 8px;
    }

    .social-icons a {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .btn-close {
        width: 30px;
        height: 30px;
        font-size: 18px;
    }

    .btn-close::before,
    .btn-close::after {
        width: 14px;
    }

    .sidebar-divider {
        margin: 15px 0;
    }

    /* Touch-friendly improvements for mobile */
    .sidebar-menu a {
        min-height: 44px; /* iOS recommended touch target size */
        display: flex;
        align-items: center;
    }

    .social-icons a {
        min-width: 44px;
        min-height: 44px;
    }

    .btn-close {
        min-width: 44px;
        min-height: 44px;
    }

    /* Fix background attachment for mobile devices (excluding hero slider) */
    section:not(.hero-section) .hero-background {
        background-attachment: scroll !important;
    }
}

    .footer-logo {
        height: 35px;
        margin-right: 10px;
    }
}

/* ===== PROPERTY DETAILS PAGE ===== */
.property-hero {
    padding: 120px 0 80px;
    background: var(--white);
}

.property-gallery {
    margin-bottom: 30px;
}

.main-image {
    position: relative;
    height: 400px;
    overflow: hidden;
    border-radius: 0;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.image-counter {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.thumbnail-gallery {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    padding: 10px 0;
}

.thumbnail {
    flex-shrink: 0;
    width: 100px;
    height: 80px;
    overflow: hidden;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    border: 2px solid transparent;
}

.thumbnail.active,
.thumbnail:hover {
    opacity: 1;
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.property-info {
    background: var(--white);
    padding: 40px;
    border-radius: 0;
    box-shadow: var(--shadow);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.property-badge {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    border-radius: 0;
}

.property-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    line-height: 1.3;
}

.property-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.property-location {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.property-location i {
    margin-right: 10px;
    color: var(--primary-color);
}

.property-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: var(--accent-color);
    border-radius: 0;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.feature-item i {
    font-size: 1.2rem;
    margin-right: 12px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.feature-item:hover i {
    color: var(--white);
}

.property-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.property-actions .btn {
    padding: 15px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
}

/* Property Content */
.property-content {
    padding: 80px 0;
    background: var(--accent-color);
}

.property-description {
    background: var(--white);
    padding: 40px;
    border-radius: 0;
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.property-description h2,
.property-description h3 {
    color: var(--text-dark);
    margin-bottom: 20px;
}

.property-description h2 {
    font-size: 1.8rem;
    font-weight: 600;
}

.property-description h3 {
    font-size: 1.4rem;
    font-weight: 500;
    margin-top: 30px;
}

.property-description p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 20px;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 30px;
}

.features-list li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.property-amenities {
    background: var(--white);
    padding: 40px;
    border-radius: 0;
    box-shadow: var(--shadow);
}

.property-amenities h2 {
    color: var(--text-dark);
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
}

.amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.amenity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 25px 15px;
    background: var(--accent-color);
    border-radius: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.amenity-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.amenity-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.amenity-item:hover i {
    color: var(--white);
    transform: scale(1.1);
}

.amenity-item span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Property Sidebar */
.property-sidebar {
    position: sticky;
    top: 120px;
}

.agent-card,
.inquiry-form {
    background: var(--white);
    padding: 30px;
    border-radius: 0;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.agent-card h3,
.inquiry-form h3 {
    color: var(--text-dark);
    margin-bottom: 25px;
    font-size: 1.4rem;
    font-weight: 600;
}

.agent-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.agent-avatar {
    width: 60px;
    height: 60px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.agent-avatar i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.agent-details h4 {
    color: var(--text-dark);
    margin-bottom: 5px;
    font-size: 1.1rem;
    font-weight: 600;
}

.agent-details p {
    color: var(--text-light);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.agent-contact {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.contact-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 0;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background: var(--text-dark);
    color: var(--white);
    transform: translateY(-2px);
}

.contact-btn i {
    margin-right: 8px;
}

.inquiry-form .form-group {
    margin-bottom: 20px;
}

.inquiry-form .form-control {
    border: 1px solid var(--border-color);
    border-radius: 0;
    padding: 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.inquiry-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: none;
    outline: none;
}

/* Similar Properties Section */
.similar-properties {
    padding: 80px 0;
    background: var(--white);
}

.similar-properties .section-title {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 60px;
    font-weight: 400;
    font-style: italic;
}

.property-card {
    background: var(--white);
    border-radius: 0;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.4s ease;
    margin-bottom: 30px;
    cursor: pointer;
}

.property-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.property-card .property-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.property-card .property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.property-card:hover .property-image img {
    transform: scale(1.1);
}

.property-card .property-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
}

.property-card .property-details {
    padding: 25px;
}

.property-card .property-details h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
    line-height: 1.3;
}

.property-card .property-price {
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 8px;
    font-family: 'Playfair Display', serif;
}

.property-card .property-location {
    color: var(--text-light);
    font-size: 0.95rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.property-card .property-location i {
    margin-right: 5px;
    color: var(--primary-color);
}

.property-card .property-features {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}

.property-card .property-features span {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.property-card .property-features span i {
    margin-right: 5px;
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* Property Location Map */
.property-location-map {
    background: var(--white);
    padding: 40px;
    border-radius: 0;
    box-shadow: var(--shadow);
    margin-top: 40px;
}

.property-location-map h2 {
    color: var(--text-dark);
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
}

.map-container {
    margin-bottom: 40px;
}

.map-placeholder {
    height: 300px;
    background: var(--accent-color);
    border: 2px dashed var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 0;
    transition: all 0.3s ease;
}

.map-placeholder:hover {
    border-color: var(--primary-color);
    background: rgba(0, 0, 0, 0.02);
}

.map-placeholder i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.map-placeholder h4 {
    color: var(--text-dark);
    margin-bottom: 10px;
    font-size: 1.3rem;
    font-weight: 600;
}

.map-placeholder p {
    color: var(--text-light);
    margin-bottom: 20px;
}

.nearby-places h3 {
    color: var(--text-dark);
    margin-bottom: 25px;
    font-size: 1.4rem;
    font-weight: 600;
}

.places-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.place-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: var(--accent-color);
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.place-item:hover {
    background: var(--white);
    border-left-color: var(--primary-color);
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.place-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 15px;
    margin-top: 5px;
    flex-shrink: 0;
}

.place-info h5 {
    color: var(--text-dark);
    margin-bottom: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.place-info p {
    color: var(--text-light);
    margin-bottom: 5px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.place-info p:last-child {
    margin-bottom: 0;
}

/* Property Specifications */
.property-specifications {
    background: var(--white);
    padding: 40px;
    border-radius: 0;
    box-shadow: var(--shadow);
    margin-top: 40px;
}

.property-specifications h2 {
    color: var(--text-dark);
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
}

.specs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.spec-category {
    background: var(--accent-color);
    padding: 25px;
    border-radius: 0;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.spec-category:hover {
    background: var(--white);
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.spec-category h4 {
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.spec-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
}

.spec-value {
    color: var(--text-dark);
    font-size: 0.9rem;
    font-weight: 600;
    text-align: right;
}

/* Property Details Responsive */
@media (max-width: 991px) {
    .property-hero {
        padding: 100px 0 60px;
    }

    .property-info {
        position: static;
        margin-top: 30px;
    }

    .property-title {
        font-size: 1.6rem;
    }

    .property-price {
        font-size: 2rem;
    }

    .property-features {
        grid-template-columns: 1fr;
    }

    .property-actions {
        flex-direction: row;
        gap: 10px;
    }

    .property-actions .btn {
        flex: 1;
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .property-sidebar {
        position: static;
        margin-top: 40px;
    }

    .amenities-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 767px) {
    .main-image {
        height: 250px;
    }

    .thumbnail-gallery {
        gap: 10px;
    }

    .thumbnail {
        width: 80px;
        height: 60px;
    }

    .property-info {
        padding: 25px;
    }

    .property-title {
        font-size: 1.4rem;
    }

    .property-price {
        font-size: 1.8rem;
    }

    .property-actions {
        flex-direction: column;
    }

    .property-content {
        padding: 60px 0;
    }

    .property-description,
    .property-amenities {
        padding: 25px;
    }

    .agent-card,
    .inquiry-form {
        padding: 20px;
    }

    .agent-info {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .agent-contact {
        flex-direction: row;
        gap: 10px;
    }

    .contact-btn {
        flex: 1;
        font-size: 0.8rem;
        padding: 8px 12px;
    }

    .amenities-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .amenity-item {
        padding: 20px 10px;
    }

    .amenity-item i {
        font-size: 1.5rem;
    }

    .property-card .property-features {
        flex-direction: column;
        gap: 5px;
    }

    .property-card .property-features span {
        justify-content: flex-start;
    }
}

/* ===== NEWSLETTER SECTION ===== */
.newsletter-section {
    background: var(--primary-color);
    padding: 80px 0;
    color: var(--white);
}

.newsletter-content h3 {
    color: var(--white);
    font-size: 2rem;
    margin-bottom: 20px;
    font-weight: 400;
    font-style: italic;
}

.newsletter-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form .input-group {
    display: flex;
    border-radius: 0;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.newsletter-form .form-control {
    border: none;
    border-radius: 0;
    padding: 18px 25px;
    font-size: 16px;
    flex: 1;
}

.newsletter-form .btn {
    border-radius: 0;
    padding: 18px 30px;
    background: var(--white);
    color: var(--primary-color);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.newsletter-form .btn:hover {
    background: var(--accent-color);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--accent-color);
    padding: 80px 0 0;
}

.footer-section {
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.footer-logo {
    height: 40px;
    width: auto;
    margin-right: 12px;
    transition: all 0.3s ease;
}

.footer-brand:hover .footer-logo {
    transform: scale(1.05);
}

.footer-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 30px;
    font-size: 0.95rem;
}

.footer-stats {
    display: flex;
    gap: 30px;
}

.footer-stats .stat-item {
    text-align: center;
}

.footer-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-family: 'Playfair Display', serif;
}

.footer-stats .stat-label {
    font-size: 0.8rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-section h5 {
    color: var(--text-dark);
    font-size: 1.2rem;
    margin-bottom: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--text-dark);
    padding-left: 5px;
}

.footer-contact .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.footer-contact .contact-item i {
    width: 20px;
    color: var(--text-dark);
    margin-right: 15px;
}

.footer-contact .contact-item a,
.footer-contact .contact-item span {
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-contact .contact-item a:hover {
    color: var(--text-dark);
}

.footer-social {
    margin-top: 30px;
}

.footer-social h6 {
    color: var(--text-dark);
    font-size: 1rem;
    margin-bottom: 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-social .social-icons {
    display: flex;
    gap: 15px;
}

.footer-social .social-icons a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.footer-social .social-icons a:hover {
    background: var(--text-dark);
    transform: translateY(-3px);
}

.footer-bottom {
    background: var(--text-dark);
    padding: 30px 0;
    border-top: 1px solid #333;
    margin-top: 60px;
}

.footer-copyright {
    font-size: 0.9rem;
    color: var(--white);
    margin: 0;
}

.footer-bottom-links {
    text-align: right;
}

.footer-bottom-links a {
    color: var(--white);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.footer-bottom-links a:hover {
    color: var(--accent-color);
}

.footer-bottom-links span {
    color: var(--white);
    margin: 0 10px;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--text-dark);
    color: var(--white);
    transform: translateY(-3px);
}

/* ===== WHATSAPP FLOAT ===== */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.whatsapp-float a {
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 28px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.whatsapp-float a:hover {
    background: #128c7e;
    transform: scale(1.1);
    color: var(--white);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* ===== LOCATIONS SECTION ===== */
.locations-section {
    padding: 100px 0;
    background: var(--white);
}

.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.location-card {
    position: relative;
    height: 300px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.location-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.location-image {
    position: relative;
    width: 100%;
    height: 100%;
}

.location-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.location-card:hover .location-image img {
    transform: scale(1.1);
}

.location-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(44, 62, 80, 0.8), rgba(52, 73, 94, 0.6));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    opacity: 0;
    transition: all 0.3s ease;
}

.location-card:hover .location-overlay {
    opacity: 1;
}

.location-overlay h4 {
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.location-overlay p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.testimonial-card {
    background: var(--white);
    padding: 50px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    margin: 0 20px;
}

.stars {
    margin-bottom: 30px;
}

.stars i {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin: 0 2px;
}

.testimonial-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-dark);
    margin-bottom: 40px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h5 {
    margin: 0;
    color: var(--text-dark);
    font-weight: 600;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* ===== SERVICES SECTION ===== */
.services-section {
    padding: 100px 0;
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.service-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
    border-color: var(--secondary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--secondary-color), #f1c40f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-card h4 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 25px;
}

.service-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-link:hover {
    color: var(--primary-color);
}

.service-link i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.service-card:hover .service-link i {
    transform: translateX(5px);
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.contact-form-wrapper {
    background: var(--white);
    padding: 50px;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.form-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.contact-info {
    padding: 50px 30px;
}

.contact-info h3 {
    margin-bottom: 40px;
    color: var(--text-dark);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: var(--white);
    font-size: 1.2rem;
}

.contact-details h5 {
    margin-bottom: 5px;
    color: var(--text-dark);
}

.contact-details p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.6;
}

/* ===== NEWSLETTER SECTION ===== */
.newsletter-section {
    padding: 80px 0;
    background: var(--primary-color);
}

.newsletter-wrapper {
    background: rgba(255, 255, 255, 0.1);
    padding: 50px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-section h3 {
    color: var(--white);
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.newsletter-section p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.subscribe-form .input-group {
    max-width: 400px;
    margin-left: auto;
}

.subscribe-form .form-control {
    border: none;
    border-radius: 50px 0 0 50px;
    padding: 15px 20px;
    font-size: 16px;
}

.subscribe-form .btn {
    border-radius: 0 50px 50px 0;
    padding: 15px 30px;
    border: none;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--primary-color);
    color: var(--white);
    padding: 80px 0 0;
}

.footer-content {
    padding-bottom: 50px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-section h5 {
    color: var(--white);
    margin-bottom: 25px;
    font-size: 1.2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    height: 40px;
    margin-right: 10px;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Lora', serif;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 30px;
}

.footer-social {
    display: flex;
    gap: 15px;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--secondary-color);
}

.footer-contact p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-contact i {
    color: var(--secondary-color);
    width: 20px;
}

.footer-bottom {
    padding: 30px 0;
    text-align: center;
}

.footer-logo-bottom img {
    height: 30px;
}

.footer-bottom-links {
    text-align: right;
}

.footer-bottom-links p {
    margin-bottom: 10px;
    color: rgba(255, 255, 255, 0.8);
}

.bottom-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.bottom-links a:hover {
    color: var(--secondary-color);
}

.bottom-links span {
    color: rgba(255, 255, 255, 0.5);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991px) {
    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .service-image {
        height: 400px;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .property-name {
        font-size: 2.5rem;
    }

    .property-image-main {
        height: 400px;
        margin-top: 30px;
    }

    .property-info {
        padding: 40px 20px;
    }

    .navbar-nav .nav-link {
        margin: 0 15px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 50px;
        height: 50px;
        margin: 0 15px;
    }

    /* Tablet Gallery Layout */
    .gallery-creative {
        grid-template-columns: repeat(6, 1fr);
        grid-template-rows: repeat(8, 100px);
        gap: 15px;
    }

    .gallery-large {
        grid-column: span 6;
        grid-row: span 3;
    }

    .gallery-medium {
        grid-column: span 3;
        grid-row: span 2;
    }

    .gallery-small {
        grid-column: span 2;
        grid-row: span 2;
    }

    .filter-btn {
        padding: 10px 25px;
        margin: 5px;
    }
}

@media (max-width: 767px) {
    .minimal-nav {
        padding: 20px 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
        margin-bottom: 15px;
    }

    .services-section {
        padding: 80px 0;
    }

    .service-image {
        height: 300px;
    }

    .gallery-section {
        padding: 80px 0;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 50px;
    }

    /* Mobile Gallery Layout */
    .gallery-creative {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 15px;
        padding: 0 15px;
    }

    .gallery-large,
    .gallery-medium,
    .gallery-small {
        grid-column: span 1;
        grid-row: span 1;
        height: 250px;
    }

    .gallery-item {
        transform: translateY(30px) scale(0.95);
    }

    .gallery-item.animate-in {
        transform: translateY(0) scale(1);
    }

    .gallery-item:hover {
        transform: translateY(-5px) scale(1.02);
    }

    .overlay-content {
        padding: 20px;
    }

    .overlay-content h3 {
        font-size: 1.4rem;
    }

    .overlay-content h4 {
        font-size: 1.2rem;
    }

    .property-details {
        flex-direction: column;
        gap: 10px;
    }

    .filter-btn {
        padding: 10px 20px;
        margin: 5px;
        font-size: 12px;
    }

    .properties-section {
        padding: 80px 0;
    }

    .property-info {
        padding: 40px 20px;
        text-align: center;
    }

    .property-name {
        font-size: 2rem;
    }

    .property-image-main {
        height: 300px;
        margin-top: 30px;
    }

    .property-showcase {
        margin-bottom: 60px;
    }

    /* Reduce animation distance on mobile */
    .property-info {
        transform: translateY(-40px);
    }

    .property-image-main {
        transform: translateY(40px);
    }

    .property-reverse .property-info {
        transform: translateY(40px);
    }

    .property-reverse .property-image-main {
        transform: translateY(-40px);
    }

    /* Reduce individual element animation distances */
    .property-name {
        transform: translateY(-30px);
    }

    .property-type,
    .property-location,
    .property-description {
        transform: translateY(20px);
    }

    .property-price {
        transform: translateY(30px);
    }

    /* Contact Section Mobile */
    .contact-form-wrapper {
        padding: 30px 20px;
        margin-bottom: 30px;
    }

    .contact-info-card {
        padding: 30px 20px;
    }

    /* Newsletter Mobile */
    .newsletter-form .input-group {
        flex-direction: column;
    }

    .newsletter-form .form-control {
        border-radius: 0;
        margin-bottom: 15px;
    }

    .newsletter-form .btn {
        border-radius: 0;
        width: 100%;
    }

    /* Footer Mobile */
    .footer-stats {
        justify-content: center;
        gap: 20px;
    }

    .footer-bottom-links {
        text-align: center;
        margin-top: 20px;
    }

    .footer-bottom-links a {
        color: var(--white);
    }

    .footer-bottom-links a:hover {
        color: var(--accent-color);
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .about-stats {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .sidebar {
        width: 280px;
        max-width: 85vw;
        right: -100%;
    }

    .sidebar-header {
        padding: 20px 25px;
    }

    .sidebar-content {
        padding: 25px;
    }

    .sidebar-brand {
        font-size: 1.1rem;
    }

    .sidebar-logo {
        height: 30px;
        margin-right: 10px;
    }

    .sidebar-menu a {
        padding: 12px 15px;
        font-size: 12px;
        letter-spacing: 1px;
    }

    .social-icons {
        gap: 12px;
    }

    .social-icons a {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    /* General Content Wrapper Mobile Small */
    .content-wrapper {
        padding: 0 10px !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
        transform: none !important;
        max-width: 100% !important;
    }

    .about-content-side {
        padding: 0 !important;
    }

    .index-content-wrapper {
        padding: 0 10px !important;
        text-align: center !important;
        max-width: 100% !important;
    }

    .index-about-content-side {
        padding: 0 !important;
    }

    /* Who We Are Section Mobile Small */
    .who-we-are-section {
        padding: 60px 0 !important;
    }

    .who-we-are-section .content-wrapper {
        padding: 0 15px;
        margin-bottom: 2rem;
    }

    .who-we-are-section .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .who-we-are-section .section-badge {
        font-size: 12px;
        padding: 5px 12px;
        margin-bottom: 0.8rem;
    }

    .who-we-are-section .content-text p {
        font-size: 0.95rem !important;
        line-height: 1.5 !important;
        margin-bottom: 1rem !important;
        padding: 0 10px;
    }

    .who-we-are-section .about-image {
        height: 280px;
        max-width: 90%;
        margin: 20px auto 0;
        display: block;
    }

    /* Home Buyers Section Mobile Small */
    .home-buyers-section {
        padding: 60px 0;
    }

    .section-center-header {
        margin-bottom: 40px;
        padding: 0 15px;
    }

    .center-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .center-icon-wrapper {
        margin-bottom: 20px;
    }

    .service-title {
        font-size: 1.8rem;
    }

    .service-badge {
        padding: 6px 15px;
        font-size: 0.7rem;
        margin-bottom: 15px;
    }

    /* Services Grid Mobile Small */
    .services-grid-layout {
        flex-direction: column;
        gap: 25px;
        padding: 0 15px;
    }

    .center-divider {
        display: none;
    }

    .left-services .service-item,
    .right-services .service-item {
        flex-direction: column;
        text-align: center;
        margin: 0 auto;
        max-width: 100%;
        padding: 20px 15px;
        gap: 12px;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin: 0 auto 15px auto;
    }

    .service-details h4 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .service-details p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Section Bottom Mobile */
    .section-bottom {
        margin-top: 40px;
        padding: 0 15px;
    }

    .testimonial-box {
        padding: 20px 15px;
        margin-bottom: 25px;
    }

    .testimonial-text {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .testimonial-author {
        font-size: 0.8rem;
    }

    .service-cta .btn {
        padding: 12px 25px;
        font-size: 0.9rem;
    }
}

    .whatsapp-float {
        bottom: 20px;
        right: 20px;
    }

    .whatsapp-float a {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        display: none;
    }
}

/* ===== ADDITIONAL ENHANCEMENTS ===== */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--secondary-color), #f1c40f);
    z-index: 9999;
    transition: width 0.3s ease;
}

.navbar-nav .nav-link.active,
.sidebar-menu a.active {
    color: var(--secondary-color) !important;
    font-weight: 600;
}

.navbar-nav .nav-link.active::after {
    width: 100%;
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Enhanced hover effects */
.btn:hover {
    transform: translateY(-2px);
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Improved focus states for accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .navbar,
    .sidebar,
    .whatsapp-float,
    .back-to-top,
    .scroll-progress {
        display: none !important;
    }

    .hero-section {
        height: auto;
        page-break-after: always;
    }

    section {
        page-break-inside: avoid;
    }
}

/* ===== ABOUT PAGE STYLES ===== */

.about-hero-section {
    height: 50vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-light) 100%);
    margin-top: 0;
}

.about-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/real.jpg');
    background-size: cover;
    background-position: center;
}

.about-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.about-hero-section .hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    padding-top: 80px;
}

.about-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.about-hero-section .hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Who We Are Section */
.who-we-are-section {
    padding: 100px 0 !important;
    background-color: var(--white) !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative !important;
    z-index: 1 !important;
}

.who-we-are-section .content-wrapper {
    padding-right: 2rem;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.who-we-are-section .section-badge {
    display: inline-block !important;
    background-color: var(--accent-color) !important;
    color: var(--text-dark) !important;
    padding: 8px 20px;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    opacity: 1 !important;
    visibility: visible !important;
}

.who-we-are-section .section-title {
    font-size: 2.5rem !important;
    margin-bottom: 2rem;
    color: var(--text-dark) !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.who-we-are-section .content-text p {
    margin-bottom: 1.5rem !important;
    font-size: 1.1rem !important;
    line-height: 1.8 !important;
    color: var(--text-dark) !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px;
}

.who-we-are-section .image-wrapper {
    position: relative;
}

.who-we-are-section .about-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: var(--shadow-hover);
}

/* Expertise Section */
.expertise-section {
    padding: 100px 0;
    background-color: var(--light-gray);
}

.expertise-section .section-header {
    margin-bottom: 4rem;
}

.expertise-section .section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.expertise-section .section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
}

.expertise-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
    height: 100%;
    transition: transform 0.3s ease;
}

.expertise-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.expertise-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.expertise-list {
    list-style: none;
    padding: 0;
}

.expertise-list li {
    padding: 0.8rem 0;
    font-size: 1.1rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.expertise-list li:last-child {
    border-bottom: none;
}

.expertise-list li i {
    color: var(--primary-color);
    margin-right: 1rem;
    width: 20px;
}

.expertise-tagline {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-style: italic;
    max-width: 800px;
    margin: 0 auto;
}

/* Mission & Vision Section */
.mission-vision-section {
    padding: 100px 0;
    background-color: var(--white);
}

.mission-card,
.vision-card {
    background: var(--light-gray);
    padding: 3rem;
    border-radius: 15px;
    height: 100%;
    text-align: center;
    transition: transform 0.3s ease;
}

.mission-card:hover,
.vision-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.mission-card .card-icon,
.vision-card .card-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
}

.mission-card h3,
.vision-card h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.mission-card p,
.vision-card p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Differentiators Section */
.differentiators-section {
    padding: 100px 0;
    background-color: var(--light-gray);
}

.differentiators-section .section-header {
    margin-bottom: 4rem;
}

.differentiators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.diff-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.diff-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.diff-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
}

.diff-item h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin: 0;
}

.tagline-box {
    background: var(--white);
    padding: 3rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    max-width: 800px;
    margin: 0 auto;
}

.main-tagline {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.sub-tagline {
    font-size: 1.1rem;
    color: var(--text-light);
    font-style: italic;
    margin: 0;
}

/* Founder Section */
.founder-section {
    padding: 100px 0;
    background-color: var(--white);
}

.founder-section .section-header {
    margin-bottom: 4rem;
}

.founder-quote {
    font-size: 1.3rem;
    font-style: italic;
    color: var(--text-light);
    margin-top: 1rem;
}

.founder-image-wrapper {
    position: relative;
    text-align: center;
}

.founder-image {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
}

.founder-image img {
    width: 300px;
    height: 300px;
    object-fit: cover;
    border-radius: 50%;
    box-shadow: var(--shadow-hover);
}

.founder-name-card {
    background: var(--primary-color);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.founder-name-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--white);
}

.founder-name-card p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

.founder-content {
    padding-left: 2rem;
}

.founder-intro {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.founder-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.perspective-points {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 10px;
    margin: 2rem 0;
}

.perspective-points ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.perspective-points li {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
}

.perspective-points li:before {
    content: "•";
    color: var(--primary-color);
    font-size: 1.5rem;
    position: absolute;
    left: 0;
    top: -2px;
}

.founder-final-quote {
    background: var(--primary-color);
    color: var(--white);
    padding: 2rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-style: italic;
    line-height: 1.8;
    margin: 2rem 0 0;
    border-left: none;
}

.founder-final-quote cite {
    display: block;
    text-align: right;
    margin-top: 1rem;
    font-weight: 600;
    font-style: normal;
}

/* ===== SERVICES PAGE STYLES ===== */

/* Services Hero Section */
.services-hero-section {
    height: 50vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-light) 100%);
    margin-top: 0;
}

.services-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/real.jpg');
    background-size: cover;
    background-position: center;
}

.services-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.services-hero-section .hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    padding-top: 80px;
}

.services-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.services-hero-section .hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Services Overview Section */
.services-overview-section {
    padding: 80px 0;
    background-color: var(--white);
}

.services-overview-section .section-header {
    max-width: 800px;
    margin: 0 auto;
}

.services-overview-section .section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.services-overview-section .section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.8;
}

/* Home Buyers Section */
.home-buyers-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.home-buyers-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0,0,0,0.02) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Section Center Header */
.section-center-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.center-icon-wrapper {
    margin-bottom: 30px;
}

.center-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    position: relative;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    animation: centerIconFloat 4s ease-in-out infinite;
}

.builders-center-icon {
    background: linear-gradient(135deg, var(--text-light), #555);
}

@keyframes centerIconFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-10px) scale(1.05); }
}

.center-icon::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.builders-center-icon::before {
    border-color: var(--text-light);
}

/* Services Grid Layout */
.services-grid-layout {
    display: flex;
    align-items: flex-start;
    gap: 60px;
    margin-bottom: 80px;
    position: relative;
}

.services-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.left-services .service-item {
    margin-left: auto;
    text-align: right;
}

.right-services .service-item {
    margin-right: auto;
    text-align: left;
}

/* Center Divider */
.center-divider {
    position: relative;
    width: 2px;
    align-self: stretch;
    display: flex;
    align-items: center;
    justify-content: center;
}

.divider-line {
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, var(--primary-color), transparent);
    position: relative;
}

.divider-line::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
}

/* Builders Services Layout */
.builders-services-layout {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.service-content {
    padding-right: 2rem;
    position: relative;
    z-index: 2;
}

.service-content::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: linear-gradient(45deg, rgba(0,0,0,0.05), transparent, rgba(0,0,0,0.05));
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-content:hover::before {
    opacity: 1;
}

.service-badge {
    display: inline-block;
    background: linear-gradient(45deg, var(--primary-color), #333);
    color: var(--white);
    padding: 12px 25px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    border-radius: 25px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.service-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.service-badge:hover::before {
    left: 100%;
}

.service-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.builders-badge {
    background-color: var(--text-light);
}

.service-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.services-list {
    margin-bottom: 3rem;
}

.service-item {
    display: flex;
    align-items: flex-start;
    padding: 2rem;
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    /* max-width: 400px; */
    margin-bottom: 0;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,0,0,0.05), transparent);
    transition: left 0.6s ease;
}

.service-item:hover::before {
    left: 100%;
}

.service-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

/* Left Services Alignment */
.left-services .service-item {
    flex-direction: row-reverse;
}

.left-services .service-icon {
    margin-left: 1.5rem;
    margin-right: 0;
}

.left-services .service-details {
    text-align: right;
}

/* Right Services Alignment */
.right-services .service-item {
    flex-direction: row;
}

.right-services .service-icon {
    margin-right: 1.5rem;
    margin-left: 0;
}

.right-services .service-details {
    text-align: left;
}

/* Builders Service Items */
.builders-service-item {
    max-width: none;
    margin: 0 auto;
    text-align: center;
    flex-direction: column;
    align-items: center;
}

.builders-service-item .service-icon {
    margin: 0 0 1.5rem 0;
}

.builders-service-item .service-details {
    text-align: center;
}

.builders-service-item:hover {
    border-color: var(--text-light);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    font-size: 1.8rem;
    flex-shrink: 0;
    position: relative;
    transition: all 0.4s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
}

.service-item:hover .service-icon {
    transform: rotate(360deg) scale(1.1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.service-item:hover .service-icon::before {
    width: 100%;
    height: 100%;
}

.service-icon i {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.service-item:hover .service-icon i {
    transform: scale(1.2);
}

.builders-icon {
    background: var(--text-light);
}

.service-details {
    flex: 1;
    position: relative;
}

.service-details h4 {
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
    color: var(--text-dark);
    position: relative;
    transition: all 0.3s ease;
    font-weight: 600;
}

.service-details h4::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    transition: width 0.4s ease;
}

.service-item:hover .service-details h4::after {
    width: 100%;
}

.service-item:hover .service-details h4 {
    color: var(--primary-color);
    transform: translateX(5px);
}

.service-details p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.7;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.service-item:hover .service-details p {
    color: var(--text-dark);
    transform: translateX(5px);
}

/* Testimonial Box */
.testimonial-box {
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
    padding: 2.5rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.testimonial-box::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

.testimonial-box:hover::before {
    transform: scale(1);
}

.testimonial-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.testimonial-icon {
    font-size: 2.5rem;
    opacity: 0.4;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.testimonial-box:hover .testimonial-icon {
    opacity: 0.6;
    transform: scale(1.1) rotate(5deg);
}

.testimonial-text {
    font-size: 1.2rem;
    font-style: italic;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.testimonial-box:hover .testimonial-text {
    transform: translateY(-2px);
}

.testimonial-author {
    font-weight: 600;
    font-size: 1rem;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    opacity: 0.9;
}

.testimonial-box:hover .testimonial-author {
    opacity: 1;
    transform: translateY(-2px);
}

/* Service CTA */
.service-cta .btn {
    padding: 18px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 35px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    background: linear-gradient(45deg, var(--primary-color), #333);
    border: none;
}

.service-cta .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.service-cta .btn:hover::before {
    left: 100%;
}

.service-cta .btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(0,0,0,0.3);
}

.service-cta .btn i {
    margin-right: 0.8rem;
    transition: all 0.3s ease;
}

.service-cta .btn:hover i {
    transform: scale(1.2) rotate(10deg);
}

/* Additional Creative Animations */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.typewriter {
    overflow: hidden;
    border-right: 3px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3s steps(30, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--primary-color); }
}

/* Staggered Animation for Service Items */
.service-item:nth-child(1) { animation-delay: 0.1s; }
.service-item:nth-child(2) { animation-delay: 0.2s; }
.service-item:nth-child(3) { animation-delay: 0.3s; }
.service-item:nth-child(4) { animation-delay: 0.4s; }
.service-item:nth-child(5) { animation-delay: 0.5s; }
.service-item:nth-child(6) { animation-delay: 0.6s; }

/* Interactive Number Counter Animation */
.service-item::after {
    content: attr(data-number);
    position: absolute;
    top: 10px;
    right: 15px;
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.service-item:hover::after {
    opacity: 1;
    transform: scale(1);
}

/* Floating Elements Animation */
.service-content::after {
    content: '🏠';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 3s ease-in-out infinite;
    pointer-events: none;
}

/* Interactive Hover Effects for Better UX */
.service-item:hover .service-details h4 {
    background: linear-gradient(45deg, var(--primary-color), #333);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Service Image Wrapper */
.service-image-wrapper {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-hover);
}

.service-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-image-wrapper:hover .service-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-image-wrapper:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: var(--white);
}

.overlay-content h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--white);
}

.overlay-content p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Builders Section */
.builders-section {
    padding: 120px 0;
    background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 50%, var(--white) 100%);
    position: relative;
    overflow: hidden;
}

.builders-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0,0,0,0.01) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: float 25s ease-in-out infinite reverse;
    pointer-events: none;
}

/* Section Bottom */
.section-bottom {
    text-align: center;
    position: relative;
    z-index: 2;
}

/* About Page Mobile Responsive Styles */
@media (max-width: 768px) {
    .about-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .about-hero-section .hero-subtitle {
        font-size: 1rem;
    }

    /* Who We Are Section Mobile */
    .who-we-are-section {
        padding: 80px 0 !important;
    }

    /* General Content Wrapper Mobile */
    .content-wrapper {
        padding: 0 15px !important;
        margin-bottom: 2rem !important;
        text-align: center !important;
        transform: none !important;
        max-width: 100% !important;
    }

    .about-content-side {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }

    .index-content-wrapper {
        padding: 0 15px !important;
        text-align: center !important;
        max-width: 100% !important;
    }

    .index-about-content-side {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }

    .who-we-are-section .content-wrapper {
        padding-right: 0;
        margin-bottom: 3rem;
        text-align: center;
    }

    .who-we-are-section .section-title {
        font-size: 2rem;
    }

    .who-we-are-section .section-badge {
        font-size: 14px;
        padding: 6px 16px;
    }

    .who-we-are-section .content-text p {
        font-size: 1rem !important;
        line-height: 1.6 !important;
        margin-bottom: 1.2rem !important;
    }

    .who-we-are-section .about-image {
        height: 350px;
        margin-top: 20px;
    }

    /* Home Buyers Section Mobile */
    .home-buyers-section {
        padding: 80px 0;
    }

    .section-center-header {
        margin-bottom: 60px;
        padding: 0 20px;
    }

    .center-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .service-title {
        font-size: 2rem;
    }

    .service-badge {
        font-size: 0.8rem;
        padding: 8px 20px;
        margin-bottom: 20px;
    }

    .expertise-section .section-title {
        font-size: 2rem;
    }

    .expertise-card {
        margin-bottom: 2rem;
        padding: 2rem;
    }

    .mission-card,
    .vision-card {
        margin-bottom: 2rem;
        padding: 2rem;
    }

    .differentiators-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .founder-content {
        padding-left: 0;
        margin-top: 2rem;
    }

    .founder-image img {
        width: 250px;
        height: 250px;
    }

    .tagline-box {
        padding: 2rem;
    }

    .main-tagline {
        font-size: 1.1rem;
    }

    .sub-tagline {
        font-size: 1rem;
    }

    /* Services Page Mobile Styles */
    .services-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .services-hero-section .hero-subtitle {
        font-size: 1rem;
    }

    .service-title {
        font-size: 2rem;
    }

    /* Center Icon Mobile */
    .center-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    /* Services Grid Mobile */
    .services-grid-layout {
        flex-direction: column;
        gap: 30px;
    }

    .center-divider {
        display: none;
    }

    .left-services .service-item,
    .right-services .service-item {
        flex-direction: column;
        text-align: center;
        margin: 0 auto;
        max-width: 100%;
    }

    .left-services .service-icon,
    .right-services .service-icon {
        margin: 0 0 1rem 0;
    }

    .left-services .service-details,
    .right-services .service-details {
        text-align: center;
    }

    /* Builders Layout Mobile */
    .builders-services-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .builders-service-item {
        max-width: 100%;
    }

    /* Section spacing mobile */
    .section-center-header {
        margin-bottom: 50px;
    }

    .section-bottom {
        margin-top: 50px;
    }
}

/* ===== WHY CHOOSE US PAGE STYLES ===== */

/* Why Choose Us Hero Section */
.why-choose-hero-section {
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-light) 100%);
    margin-top: 0;
}

.why-choose-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/real.jpg');
    background-size: cover;
    background-position: center;
}

.why-choose-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.why-choose-hero-section .hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    padding-top: 80px;
}

.why-choose-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.why-choose-hero-section .hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Introduction Section */
.introduction-section {
    padding: 100px 0;
    background-color: var(--white);
}

.intro-content {
    max-width: 800px;
    margin: 0 auto;
}

.intro-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.intro-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 3rem;
}

.intro-highlight {
    background: linear-gradient(135deg, var(--light-gray), var(--accent-color));
    padding: 2rem;
    border-radius: 15px;
    border-left: 5px solid var(--primary-color);
}

.intro-highlight h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin: 0;
}

/* Enhanced Features Section */
.features-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 30%, #f8f9fa 70%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 50%, rgba(0,0,0,0.02) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0,0,0,0.02) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0,0,0,0.02) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Features Header */
.features-header {
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.header-icon-wrapper {
    margin-bottom: 30px;
}

.rotating-icons {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.rotating-icons i {
    position: absolute;
    font-size: 2rem;
    color: var(--primary-color);
    animation: rotateIcons 8s linear infinite;
    opacity: 0.8;
}

.rotating-icons i:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); animation-delay: 0s; }
.rotating-icons i:nth-child(2) { top: 50%; right: 0; transform: translateY(-50%); animation-delay: 2s; }
.rotating-icons i:nth-child(3) { bottom: 0; left: 50%; transform: translateX(-50%); animation-delay: 4s; }
.rotating-icons i:nth-child(4) { top: 50%; left: 0; transform: translateY(-50%); animation-delay: 6s; }

@keyframes rotateIcons {
    0% { opacity: 0.3; transform: scale(0.8); }
    25% { opacity: 1; transform: scale(1.2); }
    50% { opacity: 0.3; transform: scale(0.8); }
    100% { opacity: 0.3; transform: scale(0.8); }
}

.features-title {
    font-size: 3rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 700;
}

.features-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    font-style: italic;
}

/* Clean Feature Cards Grid */
.features-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    max-width: 1400px;
    margin: 0 auto 80px;
    padding: 15px;
}

.feature-card-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 320px;
}

.feature-card {
    width: 350px;
    height: 300px;
    position: relative;
    cursor: pointer;
    perspective: 1000px;
}

.card-inner {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s ease;
}

.feature-card:hover .card-inner {
    transform: rotateY(180deg);
}

.card-front,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.card-front {
    background: linear-gradient(135deg, var(--white), #f8f9fa);
    border: 2px solid var(--primary-color);
}

.card-back {
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
    transform: rotateY(180deg);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.card-front h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
}

.feature-tag {
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
}

.card-back h4 {
    color: var(--white);
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.card-back p {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin-bottom: 20px;
    font-size: 0.95rem;
}

.feature-badge {
    background: rgba(255,255,255,0.2);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

/* Interactive Progress Bars */
.features-progress {
    background: var(--white);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.progress-header {
    text-align: center;
    margin-bottom: 30px;
}

.progress-header h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin: 0;
}

.progress-bars {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 20px;
}

.progress-label {
    min-width: 150px;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1rem;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: var(--light-gray);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #333);
    border-radius: 10px;
    width: 0%;
    transition: width 2s ease-in-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    min-width: 50px;
    text-align: right;
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Enhanced CTA Section */
.features-cta {
    margin-top: 4rem;
    position: relative;
    z-index: 2;
}

.cta-highlight {
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
    padding: 4rem 3rem;
    border-radius: 30px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    position: relative;
    overflow: hidden;
}

.cta-highlight::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: ctaGlow 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes ctaGlow {
    0%, 100% { transform: scale(0.8) rotate(0deg); opacity: 0.5; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

.cta-icon-animation {
    font-size: 3rem;
    margin-bottom: 20px;
    animation: ctaIconBounce 3s ease-in-out infinite;
}

@keyframes ctaIconBounce {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-10px) scale(1.1); }
}

.cta-highlight h3 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--white);
    position: relative;
    z-index: 2;
}

.cta-interactive-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.cta-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.primary-btn {
    background: var(--white);
    color: var(--primary-color);
}

.secondary-btn {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.cta-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.primary-btn:hover {
    background: var(--accent-color);
    color: var(--text-dark);
}

.secondary-btn:hover {
    background: var(--white);
    color: var(--primary-color);
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.cta-btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

/* Testimonials Section */
.testimonials-section {
    padding: 100px 0;
    background-color: var(--white);
}

.testimonials-section .section-header {
    margin-bottom: 4rem;
}

.testimonials-section .section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.testimonials-section .section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.testimonial-card {
    background: var(--light-gray);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.testimonial-content {
    padding: 3rem;
    position: relative;
}

.quote-icon {
    font-size: 3rem;
    color: var(--primary-color);
    opacity: 0.3;
    margin-bottom: 1.5rem;
}

.testimonial-text {
    font-size: 1.2rem;
    line-height: 1.7;
    color: var(--text-dark);
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-info h5 {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin-bottom: 0.3rem;
    font-weight: 600;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Stats Section */
.stats-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: var(--white);
}

.stats-section .section-title {
    color: var(--white);
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

/* Final CTA Section */
.final-cta-section {
    padding: 100px 0;
    background-color: var(--light-gray);
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: 2.2rem;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.cta-buttons .btn i {
    margin-right: 0.5rem;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* Why Choose Us Mobile Responsive Styles */
@media (max-width: 768px) {
    .why-choose-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .why-choose-hero-section .hero-subtitle {
        font-size: 1rem;
    }

    .intro-title {
        font-size: 2rem;
    }

    .intro-text {
        font-size: 1.1rem;
    }

    .features-title {
        font-size: 2.2rem;
    }

    .features-subtitle {
        font-size: 1rem;
    }

    .features-cards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        max-width: 350px;
    }

    .feature-card-wrapper {
        height: 280px;
    }

    .feature-card {
        width: 260px;
        height: 260px;
    }

    .progress-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .progress-label {
        min-width: auto;
        width: 100%;
    }

    .progress-bar {
        width: 100%;
    }

    .features-progress {
        padding: 30px 20px;
    }

    .cta-highlight {
        padding: 3rem 2rem;
    }

    .cta-highlight h3 {
        font-size: 1.5rem;
    }

    .cta-interactive-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .testimonial-content {
        padding: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stat-card {
        padding: 2rem;
    }

    .cta-title {
        font-size: 1.8rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* ===== CONTACT PAGE STYLES ===== */

/* Contact Hero Section */
.contact-hero {
    position: relative;
    height: 70vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.contact-hero .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.contact-hero .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 100%);
    z-index: 2;
}

.contact-hero .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.contact-hero .container {
    position: relative;
    z-index: 3;
}

.contact-hero .hero-content {
    color: white;
    max-width: 800px;
    margin: 0 auto;
}

.contact-hero .hero-badge {
    display: inline-block;
    background: rgba(255,255,255,0.1);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.contact-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: 1.2;
}

.contact-hero .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.6;
}

.contact-hero .hero-cta {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-hero .btn-outline-light {
    border: 2px solid rgba(255,255,255,0.8);
    color: white;
    background: transparent;
    padding: 15px 30px;
    transition: all 0.3s ease;
}

.contact-hero .btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    border-color: white;
}

/* Contact Info Section */
.contact-info-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.section-header {
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin: 0;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.contact-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--text-light));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.contact-card:hover::before {
    transform: scaleX(1);
}

.contact-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.contact-card .card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--text-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    transition: all 0.3s ease;
}

.contact-card:hover .card-icon {
    transform: scale(1.1);
}

.contact-card .card-icon i {
    font-size: 2rem;
    color: white;
}

.contact-card h4 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.contact-card p,
.contact-card .contact-link {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 10px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-card .contact-link:hover {
    color: var(--primary-color);
}

.contact-card .card-subtitle {
    font-size: 0.9rem;
    color: var(--text-light);
    opacity: 0.8;
}

.contact-card .social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.contact-card .social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    color: white;
}

.contact-card .social-link.facebook {
    background: #1877f2;
}

.contact-card .social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.contact-card .social-link.linkedin {
    background: #0077b5;
}

.contact-card .social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Contact Form Section */
.contact-form-section {
    padding: 100px 0;
    background: white;
}

/* Enhanced Select Styling for Contact Forms */
.select-wrapper {
    position: relative;
}

.select-wrapper .form-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: transparent;
    cursor: pointer;
}

.select-wrapper .select-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.select-wrapper .form-select:focus ~ .select-arrow {
    color: var(--primary-color);
    transform: translateY(-50%) rotate(180deg);
}

.select-wrapper .form-select:focus ~ .form-label,
.select-wrapper .form-select:valid ~ .form-label {
    top: 0;
    font-size: 12px;
    color: var(--primary-color);
}

.contact-form-section .form-container {
    background: white;
    padding: 50px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.contact-form-section .form-header {
    text-align: center;
    margin-bottom: 40px;
}

.contact-form-section .form-header h3 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.contact-form-section .form-header p {
    font-size: 1.1rem;
    color: var(--text-light);
}

/* Contact Sidebar */
.contact-sidebar {
    padding-left: 30px;
}

.sidebar-card {
    background: var(--light-gray);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    border-left: 4px solid var(--primary-color);
}

.sidebar-card h4 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.sidebar-card .feature-list {
    list-style: none;
    padding: 0;
}

.sidebar-card .feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 1rem;
    color: var(--text-light);
}

.sidebar-card .feature-list li i {
    color: var(--primary-color);
    margin-right: 15px;
    font-size: 1.1rem;
}

.sidebar-card .quick-contact {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.sidebar-card .quick-contact-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.sidebar-card .quick-contact-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow);
    color: var(--primary-color);
}

.sidebar-card .quick-contact-item i {
    margin-right: 15px;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar-card .quick-contact-item.whatsapp {
    background: #25d366;
    color: white;
    border-color: #25d366;
}

.sidebar-card .quick-contact-item.whatsapp:hover {
    background: #128c7e;
    color: white;
}

/* Appointment Section */
.appointment-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--light-gray) 0%, white 100%);
}

.appointment-container {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.appointment-content {
    padding: 60px;
}

.appointment-content .section-badge {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.appointment-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.appointment-content p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 30px;
    line-height: 1.6;
}

.appointment-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.appointment-features .feature-item {
    display: flex;
    align-items: center;
    font-size: 1rem;
    color: var(--text-light);
}

.appointment-features .feature-item i {
    color: var(--primary-color);
    margin-right: 15px;
    font-size: 1.2rem;
    width: 20px;
}

.appointment-form-container {
    background: var(--light-gray);
    padding: 60px;
}

.appointment-form h4 {
    font-size: 1.8rem;
    margin-bottom: 30px;
    color: var(--text-dark);
    text-align: center;
}

.appointment-form .form-group {
    margin-bottom: 25px;
}

.appointment-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.appointment-btn:hover {
    background: var(--text-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.appointment-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* FAQ Section */
.faq-section {
    padding: 100px 0;
    background: white;
}

.faq-container {
    margin-top: 60px;
}

.faq-section .accordion-item {
    border: 1px solid var(--border-color);
    border-radius: 10px;
    margin-bottom: 15px;
    overflow: hidden;
}

.faq-section .accordion-button {
    background: white;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1.1rem;
    padding: 20px 25px;
    border: none;
    box-shadow: none;
    position: relative;
}

.faq-section .accordion-button:not(.collapsed) {
    background: var(--light-gray);
    color: var(--primary-color);
}

.faq-section .accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    transition: transform 0.3s ease;
}

.faq-section .accordion-button:not(.collapsed)::after {
    transform: rotate(180deg);
}

.faq-section .accordion-body {
    padding: 20px 25px;
    background: white;
    color: var(--text-light);
    line-height: 1.6;
    border-top: 1px solid var(--border-color);
}

/* Contact Page Responsive Design */
@media (max-width: 768px) {
    .contact-hero .hero-title {
        font-size: 2.5rem;
    }

    .contact-hero .hero-subtitle {
        font-size: 1rem;
    }

    .contact-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .contact-hero .hero-cta .btn {
        width: 100%;
        max-width: 300px;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .contact-card {
        padding: 30px 20px;
    }

    .contact-form-section .form-container {
        padding: 30px 20px;
    }

    .contact-sidebar {
        padding-left: 0;
        margin-top: 40px;
    }

    .appointment-content,
    .appointment-form-container {
        padding: 40px 30px;
    }

    .appointment-content h2 {
        font-size: 2rem;
    }

    .appointment-features {
        margin-top: 20px;
    }
}

@media (max-width: 576px) {
    .contact-hero {
        height: 60vh;
        min-height: 400px;
    }

    .contact-hero .hero-title {
        font-size: 2rem;
    }

    .contact-info-section,
    .contact-form-section,
    .appointment-section,
    .faq-section {
        padding: 60px 0;
    }

    .contact-form-section .form-header h3 {
        font-size: 2rem;
    }

    .appointment-content,
    .appointment-form-container {
        padding: 30px 20px;
    }

    .appointment-content h2 {
        font-size: 1.8rem;
    }

    .sidebar-card {
        padding: 20px;
    }

    .faq-section .accordion-button {
        font-size: 1rem;
        padding: 15px 20px;
    }

    .faq-section .accordion-body {
        padding: 15px 20px;
    }
}

/* ===== BLOG STYLES ===== */

/* Blog Hero Section */
.blog-hero-section {
    position: relative;
    height: 60vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
}

.blog-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.blog-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
}

.blog-hero-section .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-hero-section .container {
    position: relative;
    z-index: 3;
}

.blog-hero-section .hero-content {
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.blog-hero-section .hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.blog-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: 1.2;
}

.blog-hero-section .hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    line-height: 1.6;
    font-weight: 300;
}

/* Featured Article Section */
.featured-article-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.featured-article {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-top: 50px;
}

.featured-article .article-image {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.featured-article .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-article:hover .article-image img {
    transform: scale(1.05);
}

.featured-article .article-category {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.featured-article .article-content {
    padding: 40px;
}

.featured-article .article-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.featured-article .article-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.featured-article .article-title {
    font-size: 2rem;
    margin-bottom: 20px;
    line-height: 1.3;
}

.featured-article .article-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.featured-article .article-title a:hover {
    color: var(--primary-color);
}

.featured-article .article-excerpt {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-light);
    margin-bottom: 25px;
}

.featured-article .article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.featured-article .tag {
    background: var(--light-gray);
    color: var(--text-dark);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Blog Articles Section */
.blog-articles-section {
    padding: 100px 0;
    background: var(--white);
}

.articles-grid {
    margin-top: 50px;
}

.blog-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.blog-card-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.blog-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
    transform: scale(1.1);
}

.blog-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.blog-card-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.blog-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.8rem;
    color: var(--text-light);
}

.blog-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.blog-title {
    font-size: 1.3rem;
    margin-bottom: 15px;
    line-height: 1.4;
    flex-grow: 1;
}

.blog-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.blog-title a:hover {
    color: var(--primary-color);
}

.blog-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
    font-size: 0.95rem;
}

.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.blog-tags .tag {
    background: var(--light-gray);
    color: var(--text-dark);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.read-more-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    margin-top: auto;
}

.read-more-btn:hover {
    color: var(--primary-color);
    gap: 12px;
}

.read-more-btn i {
    transition: transform 0.3s ease;
}

.read-more-btn:hover i {
    transform: translateX(3px);
}

/* Blog Article Styles */
.article-hero-section {
    position: relative;
    height: 70vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.article-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.article-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 2;
}

.article-hero-section .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-hero-section .container {
    position: relative;
    z-index: 3;
}

.article-hero-section .hero-content {
    color: var(--white);
    max-width: 800px;
}

.article-hero-section .breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 20px;
}

.article-hero-section .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.article-hero-section .breadcrumb-item.active {
    color: var(--white);
}

.article-category-badge {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.article-hero-section .article-title {
    font-size: 3rem;
    font-weight: 400;
    margin-bottom: 30px;
    line-height: 1.2;
}

.article-hero-section .article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    font-size: 1rem;
}

.article-hero-section .meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0.9;
}

/* Article Content Section */
.article-content-section {
    padding: 100px 0;
    background: var(--white);
}

.article-content {
    background: var(--white);
    padding: 50px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.article-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
}

.article-body h2 {
    font-size: 2rem;
    margin: 40px 0 20px 0;
    color: var(--text-dark);
}

.article-body h3 {
    font-size: 1.5rem;
    margin: 30px 0 15px 0;
    color: var(--text-dark);
}

.article-body p {
    margin-bottom: 20px;
}

.article-body ul, .article-body ol {
    margin: 20px 0;
    padding-left: 30px;
}

.article-body li {
    margin-bottom: 10px;
}

.article-body blockquote {
    background: var(--light-gray);
    border-left: 4px solid var(--primary-color);
    padding: 20px 30px;
    margin: 30px 0;
    font-style: italic;
    font-size: 1.2rem;
}

.article-tags-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

.article-tags-section h4 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.article-tags .tag {
    background: var(--light-gray);
    color: var(--text-dark);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.article-tags .tag:hover {
    background: var(--primary-color);
    color: var(--white);
}

.social-share-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

.social-share-section h4 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.social-share-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.share-btn.facebook {
    background: #1877f2;
    color: white;
}

.share-btn.twitter {
    background: #1da1f2;
    color: white;
}

.share-btn.linkedin {
    background: #0077b5;
    color: white;
}

.share-btn.whatsapp {
    background: #25d366;
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
}

/* Article Sidebar */
.article-sidebar {
    padding-left: 30px;
}

.sidebar-widget {
    background: var(--white);
    padding: 30px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.sidebar-widget h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

/* Author Widget */
.author-info {
    display: flex;
    gap: 15px;
}

.author-avatar {
    flex-shrink: 0;
}

.author-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-details h4 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    border: none;
    padding: 0;
}

.author-details p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 15px;
}

.author-social {
    display: flex;
    gap: 10px;
}

.author-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: var(--light-gray);
    color: var(--text-dark);
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.author-social a:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Related Articles Widget */
.related-articles {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.related-article {
    display: flex;
    gap: 15px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.related-article:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.related-article-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
}

.related-article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-article-content h5 {
    font-size: 1rem;
    margin-bottom: 8px;
    line-height: 1.3;
}

.related-article-content h5 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.related-article-content h5 a:hover {
    color: var(--primary-color);
}

.related-article-meta {
    font-size: 0.8rem;
    color: var(--text-light);
}

.related-article-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* CTA Widget */
.cta-widget {
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: var(--white);
    text-align: center;
}

.cta-widget h4 {
    color: var(--white);
    border-color: rgba(255, 255, 255, 0.3);
}

.cta-widget p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 25px;
}

.cta-widget .btn {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    font-weight: 600;
}

.cta-widget .btn:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Newsletter Widget */
.newsletter-widget p {
    color: var(--text-light);
    margin-bottom: 20px;
}

.newsletter-form .input-group {
    display: flex;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.newsletter-form .form-control {
    border: none;
    padding: 12px 20px;
    font-size: 0.9rem;
}

.newsletter-form .form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.newsletter-form .btn {
    border: none;
    padding: 12px 25px;
    font-weight: 500;
    white-space: nowrap;
}

/* More Articles Section */
.more-articles-section {
    padding: 80px 0;
    background: var(--light-gray);
}

/* Responsive Design */
@media (max-width: 991px) {
    .article-sidebar {
        padding-left: 0;
        margin-top: 50px;
    }

    .article-content {
        padding: 30px 20px;
    }

    .article-hero-section .article-title {
        font-size: 2.5rem;
    }

    .article-hero-section .article-meta {
        gap: 20px;
    }

    .social-share-buttons {
        justify-content: center;
    }

    .author-info {
        flex-direction: column;
        text-align: center;
    }

    .related-article {
        flex-direction: column;
        text-align: center;
    }

    .related-article-image {
        width: 100%;
        height: 200px;
        align-self: center;
    }
}

@media (max-width: 768px) {
    .blog-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .featured-article .article-content {
        padding: 30px 20px;
    }

    .featured-article .article-title {
        font-size: 1.5rem;
    }

    .article-hero-section .article-title {
        font-size: 2rem;
    }

    .article-hero-section .article-meta {
        flex-direction: column;
        gap: 10px;
    }

    .social-share-buttons {
        flex-direction: column;
    }

    .share-btn {
        justify-content: center;
    }
}

/* ===== HOW WE WORK PAGE STYLES ===== */

/* How We Work Hero Section */
.how-we-work-hero-section {
    position: relative;
    height: 60vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
}

.how-we-work-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.how-we-work-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
}

.how-we-work-hero-section .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.how-we-work-hero-section .container {
    position: relative;
    z-index: 3;
}

.how-we-work-hero-section .hero-content {
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.how-we-work-hero-section .hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.how-we-work-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: 1.2;
}

.how-we-work-hero-section .hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    line-height: 1.6;
    font-weight: 300;
}

/* Process Overview Section */
.process-overview-section {
    padding: 100px 0;
    background: var(--white);
}

.process-timeline {
    margin-top: 80px;
    position: relative;
}

.process-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
    transform: translateX(-50%);
    z-index: 1;
}

.process-step {
    position: relative;
    margin-bottom: 80px;
    opacity: 1;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.process-step.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.process-step:nth-child(even) .step-container {
    flex-direction: row-reverse;
    text-align: right;
}

.step-container {
    display: flex;
    align-items: center;
    gap: 50px;
    position: relative;
}

.step-number {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 0 0 10px var(--white);
}

.step-content {
    flex: 1;
    background: var(--white);
    padding: 40px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    max-width: 500px;
}

.process-step:nth-child(even) .step-content {
    margin-left: auto;
}

.step-icon {
    width: 60px;
    height: 60px;
    background: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.step-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.step-description {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.step-details ul {
    list-style: none;
    padding: 0;
}

.step-details li {
    padding: 8px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
}

.step-details li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Why Process Works Section */
.why-process-works-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.process-benefits {
    margin-top: 40px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.benefit-content h4 {
    margin-bottom: 8px;
    color: var(--text-dark);
}

.benefit-content p {
    color: var(--text-light);
    margin: 0;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.stat-card {
    background: var(--white);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    font-family: 'Playfair Display', serif;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.stat-description {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Client Journey Section */
.client-journey-section {
    padding: 100px 0;
    background: var(--white);
}

.testimonials-grid {
    margin-top: 50px;
}

.testimonial-card {
    background: var(--white);
    padding: 30px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    flex-grow: 1;
    margin-bottom: 20px;
}

.quote-icon {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 15px;
}

.testimonial-content p {
    font-style: italic;
    line-height: 1.6;
    color: var(--text-light);
}

.testimonial-author {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
}

.author-info h5 {
    color: var(--text-dark);
    margin-bottom: 5px;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Process CTA Section */
.process-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--white);
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
}

/* ===== FEATURED PROJECTS PAGE STYLES ===== */

/* Featured Projects Hero Section */
.featured-projects-hero-section {
    position: relative;
    height: 60vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
}

.featured-projects-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.featured-projects-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
}

.featured-projects-hero-section .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-projects-hero-section .container {
    position: relative;
    z-index: 3;
}

.featured-projects-hero-section .hero-content {
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.featured-projects-hero-section .hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.featured-projects-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: 1.2;
}

.featured-projects-hero-section .hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    line-height: 1.6;
    font-weight: 300;
}

/* Projects Filter Section */
.projects-filter-section {
    padding: 50px 0;
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--white);
    border: 1px solid var(--border-color);
    color: var(--text-dark);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.location-filter select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 25px;
    background: var(--white);
    color: var(--text-dark);
    font-size: 0.9rem;
    min-width: 150px;
}

/* Featured Projects Grid */
.featured-projects-grid-section {
    padding: 80px 0;
    background: var(--white);
}

.project-item {
    transition: all 0.3s ease;
}

.project-item.hidden {
    display: none;
}

.project-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.project-status {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.project-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.project-header {
    margin-bottom: 20px;
}

.project-title {
    font-size: 1.4rem;
    margin-bottom: 8px;
    color: var(--text-dark);
}

.project-location {
    color: var(--text-light);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.project-details {
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.detail-value {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
}

.project-amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 25px;
}

.amenity-tag {
    background: var(--light-gray);
    color: var(--text-dark);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.project-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

.project-actions .btn {
    flex: 1;
    padding: 10px 15px;
    font-size: 0.9rem;
    border-radius: 25px;
    text-align: center;
}

/* Why Featured Section */
.why-featured-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.feature-card {
    background: var(--white);
    padding: 40px 20px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 20px;
}

.feature-card h4 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Projects CTA Section */
.projects-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: var(--white);
    text-align: center;
}

.projects-cta-section .cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--white);
}

.projects-cta-section .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.projects-cta-section .cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.projects-cta-section .cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
}

/* Responsive Design for How We Work and Featured Projects */
@media (max-width: 991px) {
    /* How We Work Responsive */
    .process-timeline::before {
        left: 30px;
    }

    .step-container {
        flex-direction: column !important;
        text-align: left !important;
        padding-left: 80px;
    }

    .process-step:nth-child(even) .step-content {
        margin-left: 0;
    }

    .step-number {
        left: 30px;
        width: 60px;
        height: 60px;
        font-size: 1.2rem;
    }

    .step-content {
        max-width: none;
        padding: 30px 20px;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    /* Featured Projects Responsive */
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-tabs {
        justify-content: center;
    }

    .location-filter {
        text-align: center;
    }

    .project-actions {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    /* Hero Sections */
    .how-we-work-hero-section .hero-title,
    .featured-projects-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .how-we-work-hero-section .hero-subtitle,
    .featured-projects-hero-section .hero-subtitle {
        font-size: 1.1rem;
    }

    /* Process Steps */
    .step-container {
        padding-left: 60px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1rem;
        left: 25px;
    }

    .process-timeline::before {
        left: 25px;
    }

    .step-title {
        font-size: 1.3rem;
    }

    .step-content {
        padding: 20px 15px;
    }

    /* CTA Sections */
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    /* Project Cards */
    .project-image {
        height: 200px;
    }

    .project-content {
        padding: 20px;
    }

    .project-title {
        font-size: 1.2rem;
    }

    .filter-tabs {
        gap: 5px;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    /* Hero adjustments */
    .how-we-work-hero-section,
    .featured-projects-hero-section {
        height: 50vh;
        min-height: 400px;
    }

    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    /* Process timeline mobile */
    .process-step {
        margin-bottom: 60px;
    }

    .step-container {
        padding-left: 50px;
    }

    .step-number {
        left: 20px;
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .process-timeline::before {
        left: 20px;
    }

    /* Amenity tags */
    .amenity-tag {
        font-size: 0.7rem;
        padding: 3px 8px;
    }

    /* Feature cards */
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .feature-card {
        padding: 30px 15px;
    }
}

/* ===== PROJECT DETAILS PAGE STYLES ===== */

/* Project Details Hero Section */
.project-details-hero-section {
    position: relative;
    height: 70vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.project-details-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.project-details-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 2;
}

.project-details-hero-section .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.project-details-hero-section .container {
    position: relative;
    z-index: 3;
}

.project-details-hero-section .hero-content {
    color: var(--white);
    max-width: 800px;
}

.project-details-hero-section .breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 20px;
}

.project-details-hero-section .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.project-details-hero-section .breadcrumb-item.active {
    color: var(--white);
}

.project-badges {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.project-badge {
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.project-status {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.project-details-hero-section .project-title {
    font-size: 3rem;
    font-weight: 400;
    margin-bottom: 15px;
    line-height: 1.2;
}

.project-details-hero-section .project-location {
    font-size: 1.2rem;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0.9;
}

.project-price {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.price-label {
    font-size: 1rem;
    opacity: 0.8;
}

.price-value {
    font-size: 2rem;
    font-weight: 600;
    color: var(--white);
    font-family: 'Playfair Display', serif;
}

/* Project Overview Section */
.project-overview-section {
    padding: 100px 0;
    background: var(--white);
}

.project-content {
    background: var(--white);
    padding: 40px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.project-content h2 {
    font-size: 2.2rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.project-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-light);
    margin-bottom: 40px;
}

.project-highlights {
    margin-bottom: 50px;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: var(--light-gray);
    border-radius: 10px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.highlight-item:hover {
    transform: translateY(-3px);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.highlight-content h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.highlight-content p {
    color: var(--text-light);
    margin: 0;
    font-weight: 500;
}

/* Amenities Section */
.amenities-section {
    margin-bottom: 50px;
}

.amenities-section h3 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: var(--text-dark);
}

.amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.amenity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: var(--light-gray);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.amenity-item:hover {
    background: var(--primary-color);
    color: var(--white);
}

.amenity-item i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.amenity-item:hover i {
    color: var(--white);
}

.amenity-item span {
    font-weight: 500;
}

/* Gallery Section */
.gallery-section h3 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: var(--text-dark);
}

.project-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.gallery-item {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Project Sidebar */
.project-sidebar {
    padding-left: 30px;
}

.sidebar-widget {
    background: var(--white);
    padding: 30px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.sidebar-widget h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

/* Enquiry Widget */
.enquiry-widget {
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: var(--white);
}

.enquiry-widget h4 {
    color: var(--white);
    border-color: rgba(255, 255, 255, 0.3);
}

.enquiry-form .form-group {
    margin-bottom: 20px;
}

.enquiry-form .form-control {
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 0.9rem;
}

.enquiry-form .form-control:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
    border-color: transparent;
}

.enquiry-form .btn {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    width: 100%;
}

.enquiry-form .btn:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Contact Widget */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.contact-item i {
    width: 40px!important;
    height: 40px;
    background: var(--light-gray);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-item div {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.contact-item span:first-child {
    font-size: 0.9rem;
    color: var(--text-light);
}

.contact-item a,
.contact-item span:last-child {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
}

.contact-item a:hover {
    color: var(--primary-color);
}

/* Related Projects Widget */
.related-projects {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.related-project {
    display: flex;
    gap: 15px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.related-project:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.related-project-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
}

.related-project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-project-content h5 {
    font-size: 1rem;
    margin-bottom: 8px;
    line-height: 1.3;
}

.related-project-content h5 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.related-project-content h5 a:hover {
    color: var(--primary-color);
}

.related-project-location {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.related-project-price {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 600;
}

/* Project CTA Section */
.project-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: var(--white);
    text-align: center;
}

.project-cta-section .cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--white);
}

.project-cta-section .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.project-cta-section .cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.project-cta-section .cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
}

/* ===== GALLERY PAGE STYLES ===== */

/* ===== PROJECT ENQUIRY MODAL STYLES ===== */
.enquiry-modal-content {
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
    overflow: hidden !important;
    background: var(--white) !important;
}

.enquiry-modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: var(--white) !important;
    border: none !important;
    border-bottom: none !important;
    padding: 25px 30px !important;
    position: relative !important;
}

.modal-title-wrapper {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

.modal-icon {
    width: 50px !important;
    height: 50px !important;
    background: rgba(255, 255, 255, 0.2) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    color: var(--white) !important;
    flex-shrink: 0 !important;
}

.enquiry-modal-header .modal-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
    color: var(--white) !important;
    line-height: 1.2 !important;
}

.modal-subtitle {
    font-size: 0.9rem !important;
    opacity: 0.9 !important;
    margin: 0 !important;
    color: var(--white) !important;
    line-height: 1.3 !important;
}

.enquiry-btn-close {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 1 !important;
    transition: all 0.3s ease !important;
    color: var(--white) !important;
    font-size: 1rem !important;
}

.enquiry-btn-close:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: rotate(90deg) !important;
    color: var(--white) !important;
}

.enquiry-btn-close:focus {
    box-shadow: none !important;
    background: rgba(255, 255, 255, 0.3) !important;
    color: var(--white) !important;
}

.enquiry-modal-body {
    padding: 30px !important;
    background: #fafafa !important;
}

.enquiry-form-container {
    background: var(--white) !important;
    padding: 30px !important;
    border-radius: 15px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08) !important;
}

.enquiry-input,
.enquiry-select,
.enquiry-textarea {
    border: 2px solid #e9ecef !important;
    border-radius: 12px !important;
    padding: 15px 20px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: var(--white) !important;
    color: var(--text-dark) !important;
}

.enquiry-input:focus,
.enquiry-select:focus,
.enquiry-textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.15) !important;
    background: var(--white) !important;
    outline: none !important;
}

.form-floating > label {
    color: #6c757d !important;
    font-weight: 500 !important;
    padding-left: 20px !important;
    background: transparent !important;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    color: var(--primary-color);
    font-weight: 600;
}

.enquiry-textarea {
    min-height: 120px !important;
    resize: vertical !important;
}

.enquiry-modal-footer {
    background: var(--white) !important;
    border: none !important;
    border-top: none !important;
    padding: 25px 30px !important;
    gap: 15px !important;
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
}

.enquiry-btn-cancel {
    border-radius: 25px !important;
    padding: 12px 25px !important;
    font-weight: 500 !important;
    border: 2px solid #6c757d !important;
    transition: all 0.3s ease !important;
    background: transparent !important;
    color: #6c757d !important;
}

.enquiry-btn-cancel:hover {
    background: #6c757d !important;
    color: var(--white) !important;
    transform: translateY(-2px) !important;
    border-color: #6c757d !important;
}

.enquiry-btn-submit {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    border: none !important;
    border-radius: 25px !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3) !important;
    color: var(--white) !important;
}

.enquiry-btn-submit:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(var(--primary-rgb), 0.4) !important;
    color: var(--white) !important;
}

.enquiry-btn-submit:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25) !important;
    color: var(--white) !important;
}

/* Modal Dialog Improvements */
.modal-dialog {
    max-width: 600px !important;
}

.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6) !important;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out !important;
    transform: translate(0, -50px) !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

/* Loading State for Submit Button */
.enquiry-btn-submit.loading {
    position: relative !important;
    color: transparent !important;
}

.enquiry-btn-submit.loading::after {
    content: '' !important;
    position: absolute !important;
    width: 20px !important;
    height: 20px !important;
    top: 50% !important;
    left: 50% !important;
    margin-left: -10px !important;
    margin-top: -10px !important;
    border: 2px solid transparent !important;
    border-top-color: var(--white) !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enquiry Modal Responsive Styles */
@media (max-width: 991px) {
    .modal-dialog {
        margin: 20px !important;
        max-width: calc(100% - 40px) !important;
    }

    .enquiry-modal-header {
        padding: 20px 25px !important;
    }

    .modal-title-wrapper {
        gap: 12px !important;
    }

    .modal-icon {
        width: 45px !important;
        height: 45px !important;
        font-size: 1.3rem !important;
    }

    .enquiry-modal-header .modal-title {
        font-size: 1.3rem !important;
    }

    .enquiry-modal-body {
        padding: 25px 20px !important;
    }

    .enquiry-form-container {
        padding: 25px 20px !important;
    }
}

@media (max-width: 768px) {
    .modal-dialog {
        margin: 15px !important;
        max-width: calc(100% - 30px) !important;
    }

    .enquiry-modal-header {
        padding: 18px 20px !important;
    }

    .modal-title-wrapper {
        gap: 10px !important;
    }

    .modal-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 1.2rem !important;
    }

    .enquiry-modal-header .modal-title {
        font-size: 1.2rem !important;
    }

    .modal-subtitle {
        font-size: 0.85rem !important;
    }

    .enquiry-modal-body {
        padding: 20px 15px !important;
    }

    .enquiry-form-container {
        padding: 20px 15px !important;
    }

    .enquiry-input,
    .enquiry-select,
    .enquiry-textarea {
        padding: 12px 15px !important;
        font-size: 0.95rem !important;
    }

    .form-floating > label {
        padding-left: 15px !important;
        font-size: 0.9rem !important;
    }

    .enquiry-modal-footer {
        padding: 20px 15px !important;
        flex-direction: column !important;
        gap: 10px !important;
    }

    .enquiry-btn-cancel,
    .enquiry-btn-submit {
        width: 100% !important;
        padding: 12px 20px !important;
    }
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 10px !important;
        max-width: calc(100% - 20px) !important;
    }

    .enquiry-modal-content {
        border-radius: 15px !important;
    }

    .enquiry-modal-header {
        padding: 15px !important;
    }

    .modal-title-wrapper {
        flex-direction: column !important;
        text-align: center !important;
        gap: 8px !important;
    }

    .enquiry-modal-body {
        padding: 15px 10px !important;
    }

    .enquiry-form-container {
        padding: 15px !important;
        border-radius: 10px !important;
    }

    .enquiry-textarea {
        min-height: 100px;
    }

    .enquiry-modal-footer {
        padding: 15px;
    }
}

/* Loading state for submit button */
.enquiry-btn-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

.enquiry-btn-submit.loading {
    position: relative;
    color: transparent;
}

.enquiry-btn-submit.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== GALLERY PAGE STYLES ===== */

/* Gallery Hero Section */
.gallery-hero-section {
    position: relative;
    height: 60vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
}

.gallery-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.gallery-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
}

.gallery-hero-section .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-hero-section .container {
    position: relative;
    z-index: 3;
}

.gallery-hero-section .hero-content {
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.gallery-hero-section .hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.gallery-hero-section .hero-badge.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: 1.2;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease 0.2s;
}

.gallery-hero-section .hero-title.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-hero-section .hero-subtitle {
    font-size: 1.3rem;
    opacity: 0;
    line-height: 1.6;
    font-weight: 300;
    transform: translateY(30px);
    transition: all 0.8s ease 0.4s;
}

.gallery-hero-section .hero-subtitle.animate-in {
    opacity: 0.9;
    transform: translateY(0);
}

/* Gallery Search Section */
.gallery-search-section {
    background: var(--white);
    border-bottom: 1px solid var(--border-color);
}

.gallery-search-form .input-group {
    box-shadow: var(--shadow);
    border-radius: 50px;
    overflow: hidden;
    background: var(--white);
}

.gallery-search-form .form-control {
    border: none;
    padding: 15px 25px;
    font-size: 1rem;
    background: transparent;
    box-shadow: none;
}

.gallery-search-form .form-control:focus {
    border: none;
    box-shadow: none;
    background: transparent;
}

.gallery-search-form .btn {
    border: none;
    padding: 15px 30px;
    background: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    transition: all 0.3s ease;
}

.gallery-search-form .btn:hover {
    background: var(--secondary-color);
    transform: translateX(-2px);
}

/* Gallery Filter Section */
.gallery-filter-section {
    padding: 50px 0;
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--white);
    border: 2px solid var(--border-color);
    color: var(--text-dark);
    padding: 12px 25px;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Gallery Grid Section */
.gallery-grid-section {
    padding: 100px 0;
    background: var(--white);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.gallery-item {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.gallery-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-item.hidden {
    display: none;
}

.gallery-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 300px;
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.gallery-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-card:hover .gallery-image img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: var(--white);
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-content {
    transform: translateY(0);
}

.gallery-content h4 {
    font-size: 1.5rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.gallery-content p {
    font-size: 1rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.gallery-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.gallery-btn {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.gallery-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* No Gallery Items Message */
.no-gallery-items {
    padding: 60px 20px;
    text-align: center;
}

.no-gallery-items i {
    color: #ccc;
    margin-bottom: 20px;
}

.no-gallery-items h4 {
    color: #999;
    margin-bottom: 15px;
    font-weight: 500;
}

.no-gallery-items p {
    color: #aaa;
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Pagination Styling */
.pagination-wrapper {
    margin-top: 50px;
}

.pagination-wrapper .pagination {
    justify-content: center;
}

.pagination-wrapper .page-link {
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.pagination-wrapper .page-link:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-wrapper .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* Gallery Stats Section */
.gallery-stats-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.stat-card {
    background: var(--white);
    padding: 40px 20px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(30px);
}

/* Removed duplicate - using enhanced version below */

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
}

.stat-number {
    font-size: 3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    font-family: 'Playfair Display', serif;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 500;
}

/* Gallery CTA Section */
.gallery-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: var(--white);
    text-align: center;
}

.gallery-cta-section .cta-content {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.gallery-cta-section .cta-content.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-cta-section .cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--white);
}

.gallery-cta-section .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Gallery Modal Styles */
.modal-content {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    background: var(--primary-color);
    color: var(--white);
    border-bottom: none;
}

.modal-body {
    padding: 0;
}

#modalImage {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
}

/* Responsive Design */
@media (max-width: 991px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .gallery-card {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .gallery-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .gallery-hero-section .hero-subtitle {
        font-size: 1.1rem;
    }

    .filter-tabs {
        gap: 10px;
    }

    .filter-btn {
        padding: 10px 20px;
        font-size: 0.8rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .gallery-card {
        height: 200px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 576px) {
    .gallery-hero-section {
        height: 50vh;
        min-height: 400px;
    }

    .gallery-hero-section .hero-title {
        font-size: 2rem;
    }

    .gallery-hero-section .hero-subtitle {
        font-size: 1rem;
    }

    .gallery-content h4 {
        font-size: 1.2rem;
    }

    .gallery-content p {
        font-size: 0.9rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* ===== ANIMATION FIXES FOR CONSISTENCY ===== */

/* Ensure all animate-in classes work consistently */
.animate-in {
    animation: slideInUp 0.6s ease forwards !important;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Title visibility animations like original sections */
.gallery-section.title-visible .section-title,
.how-we-work-section.title-visible .section-title,
.featured-projects-section.title-visible .section-title {
    opacity: 1;
    transform: translateY(0);
}

/* Property card animations consistency */
.gallery-item.property-visible,
.project-item.property-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Removed duplicate - using enhanced version below */

/* Method card animations for consistency */
.method-card.animate-in,
.feature-card.animate-in,
.benefit-item.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.6s ease forwards;
}

/* Hero section animations */
.hero-badge.animate-in,
.hero-title.animate-in,
.hero-subtitle.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* CTA section animations */
.cta-content.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.8s ease forwards;
}

/* Testimonial card animations */
.testimonial-card.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.6s ease forwards;
}

/* Process step animations */
.process-step.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.8s ease forwards;
}

/* Gallery specific animations to match original style */
.gallery-grid-section .section-title {
    opacity: 0;
    transform: translateY(-30px);
    transition: all 0.8s ease;
}

.gallery-grid-section.title-visible .section-title {
    opacity: 1;
    transform: translateY(0);
}

/* Ensure consistent hover effects */
.gallery-card:hover,
.project-card:hover,
.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Button hover effects consistency */
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.filter-btn:hover,
.filter-btn.active {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* ===== NOTIFICATION STYLES ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 9999;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid var(--primary-color);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-success {
    border-left-color: #28a745;
}

.notification.notification-success i {
    color: #28a745;
}

.notification.notification-error {
    border-left-color: #dc3545;
}

.notification.notification-error i {
    color: #dc3545;
}

.notification.notification-info {
    border-left-color: #17a2b8;
}

.notification.notification-info i {
    color: #17a2b8;
}

.notification i {
    font-size: 1.2rem;
}

.notification span {
    flex: 1;
    color: var(--text-dark);
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.notification-close:hover {
    color: var(--text-dark);
}

@media (max-width: 576px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* ===== ENHANCED CONTACT PAGE STYLES ===== */

/* Hero Stats */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Text Gradient */
.text-gradient {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Buttons */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-enhanced .btn-text {
    transition: transform 0.3s ease;
}

.btn-enhanced .btn-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%) translateX(30px);
    opacity: 0;
    transition: all 0.3s ease;
}

.btn-enhanced:hover .btn-icon {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
}

.btn-enhanced:hover .btn-text {
    transform: translateX(-10px);
}

/* Section Badge */
.section-badge {
    display: inline-block;
    background: rgba(255, 215, 0, 0.1);
    color: #FFD700;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

/* Enhanced Form Inputs */
.enhanced-input {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    transition: all 0.3s ease;
    z-index: 2;
}

.enhanced-input .form-input {
    padding-left: 45px;
}

.enhanced-input .form-input:focus ~ .input-icon {
    color: var(--primary-color);
}

.input-hint {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-top: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.enhanced-input .form-input:focus ~ .input-hint {
    opacity: 1;
}

/* Enhanced Select */
.enhanced-select .form-select {
    background-image: none;
}

/* Enhanced Textarea */
.enhanced-textarea {
    position: relative;
}

.character-count {
    position: absolute;
    bottom: 10px;
    right: 15px;
    font-size: 0.8rem;
    color: var(--text-light);
}

.character-count .current {
    color: var(--primary-color);
    font-weight: 600;
}

/* Enhanced Submit Button */
.enhanced-submit {
    position: relative;
    overflow: hidden;
}

.btn-loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.enhanced-submit.loading .btn-text,
.enhanced-submit.loading .btn-icon {
    opacity: 0;
}

.enhanced-submit.loading .btn-loading {
    opacity: 1;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}

/* ===== TESTIMONIAL RATING STYLES ===== */
.testimonial-rating {
    margin-top: 10px;
}

.testimonial-rating i {
    font-size: 14px;
    margin-right: 2px;
}

.testimonial-rating .text-warning {
    color: #ffc107 !important;
}

.testimonial-rating .text-muted {
    color: #6c757d !important;
}

/* ===== COMPACT TESTIMONIAL CAROUSEL STYLES ===== */
.testimonials-carousel-container {
    position: relative;
    margin: 40px 0;
    padding: 20px 0;
}

/* Testimonial Animation Keyframes */
@keyframes testimonialSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes testimonialPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

.testimonial-card-compact {
    animation: testimonialSlideIn 0.6s ease-out;
}

.testimonial-card-compact:nth-child(1) {
    animation-delay: 0.1s;
}

.testimonial-card-compact:nth-child(2) {
    animation-delay: 0.2s;
}

.testimonial-card-compact:nth-child(3) {
    animation-delay: 0.3s;
}

.testimonial-card-compact {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(0,0,0,0.08);
    border-radius: 16px;
    padding: 24px;
    height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.testimonial-card-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.testimonial-card-compact:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: rgba(var(--primary-color-rgb), 0.2);
}

.testimonial-card-compact:hover::before {
    transform: scaleX(1);
}

.testimonial-content-compact {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.quote-icon-small {
    color: var(--primary-color);
    font-size: 20px;
    margin-bottom: 12px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.testimonial-card-compact:hover .quote-icon-small {
    opacity: 1;
    transform: scale(1.1);
}

.testimonial-text-compact {
    font-size: 15px;
    line-height: 1.6;
    color: #4a5568;
    margin-bottom: 16px;
    flex: 1;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    font-style: italic;
    position: relative;
}

.testimonial-text-compact::before {
    content: '"';
    font-size: 24px;
    color: var(--primary-color);
    opacity: 0.3;
    position: absolute;
    left: -8px;
    top: -4px;
    font-family: serif;
}

.testimonial-text-compact::after {
    content: '"';
    font-size: 24px;
    color: var(--primary-color);
    opacity: 0.3;
    position: absolute;
    right: 0;
    bottom: -8px;
    font-family: serif;
}

.testimonial-author-compact {
    margin-top: auto;
    padding-top: 12px;
    border-top: 1px solid rgba(0,0,0,0.06);
}

.testimonial-author-compact h6 {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 4px;
    transition: color 0.3s ease;
}

.testimonial-card-compact:hover .testimonial-author-compact h6 {
    color: var(--primary-color);
}

.testimonial-author-compact small {
    font-size: 13px;
    color: #718096;
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
}

.testimonial-rating-compact {
    margin-top: 6px;
    display: flex;
    align-items: center;
    gap: 2px;
}

.testimonial-rating-compact i {
    font-size: 13px;
    margin-right: 1px;
    transition: all 0.2s ease;
}

.testimonial-rating-compact i.text-warning {
    color: #f6ad55 !important;
}

.testimonial-card-compact:hover .testimonial-rating-compact i.text-warning {
    transform: scale(1.1);
}

/* Carousel Controls Styling */
.testimonials-carousel-container .carousel-control-prev,
.testimonials-carousel-container .carousel-control-next {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.9;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    border: 2px solid rgba(255,255,255,0.2);
}

.testimonials-carousel-container .carousel-control-prev {
    left: -25px;
}

.testimonials-carousel-container .carousel-control-next {
    right: -25px;
}

.testimonials-carousel-container .carousel-control-prev:hover,
.testimonials-carousel-container .carousel-control-next:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.testimonials-carousel-container .carousel-control-prev-icon,
.testimonials-carousel-container .carousel-control-next-icon {
    width: 18px;
    height: 18px;
    filter: brightness(0) invert(1);
}

/* Carousel Indicators */
.testimonials-carousel-container .carousel-indicators {
    bottom: -50px;
    margin-bottom: 0;
    gap: 8px;
}

.testimonials-carousel-container .carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.2);
    border: 2px solid transparent;
    margin: 0 4px;
    transition: all 0.3s ease;
    position: relative;
}

.testimonials-carousel-container .carousel-indicators button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: transparent;
    transition: all 0.3s ease;
}

.testimonials-carousel-container .carousel-indicators button.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: rgba(255,255,255,0.3);
    transform: scale(1.2);
}

.testimonials-carousel-container .carousel-indicators button.active::before {
    background-color: rgba(255,255,255,0.8);
}

.testimonials-carousel-container .carousel-indicators button:hover {
    transform: scale(1.1);
    background-color: rgba(var(--primary-color-rgb), 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .testimonial-card-compact {
        height: auto;
        min-height: 200px;
        margin-bottom: 20px;
        padding: 20px;
    }

    .testimonial-text-compact {
        font-size: 14px;
        -webkit-line-clamp: 4;
    }

    .testimonials-carousel-container .carousel-control-prev,
    .testimonials-carousel-container .carousel-control-next {
        width: 40px;
        height: 40px;
        left: -15px;
        right: -15px;
    }

    .testimonials-carousel-container .carousel-control-prev {
        left: -15px;
    }

    .testimonials-carousel-container .carousel-control-next {
        right: -15px;
    }

    .testimonials-carousel-container .carousel-indicators {
        bottom: -40px;
    }

    .testimonials-carousel-container .carousel-indicators button {
        width: 10px;
        height: 10px;
        margin: 0 3px;
    }
}

@media (max-width: 576px) {
    .testimonial-card-compact {
        min-height: 180px;
        padding: 18px;
    }

    .testimonial-text-compact {
        font-size: 13px;
        line-height: 1.5;
    }

    .testimonial-author-compact h6 {
        font-size: 14px;
    }

    .testimonial-author-compact small {
        font-size: 12px;
    }

    .testimonials-carousel-container .carousel-control-prev,
    .testimonials-carousel-container .carousel-control-next {
        display: none;
    }

    .quote-icon-small {
        font-size: 18px;
    }
}

/* Notification Toast Styles */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.notification-toast.show {
    opacity: 1;
    transform: translateX(0);
}

/* Navbar Scroll Effect */
.navbar-scrolled {
    background-color: rgba(8, 8, 8, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

/* Page Loader */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.page-loader .spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--light-gray);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Validation Styles */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

/* Enhanced Stat Card Animation */
.stat-card {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card.animate-in {
    opacity: 1;
    transform: translateY(0);
    animation: statCardSlideIn 0.8s ease-out forwards;
}

@keyframes statCardSlideIn {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-10px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Staggered Animation for Multiple Stat Cards */
.stat-card:nth-child(1).animate-in {
    animation-delay: 0.1s;
}

.stat-card:nth-child(2).animate-in {
    animation-delay: 0.2s;
}

.stat-card:nth-child(3).animate-in {
    animation-delay: 0.3s;
}

.stat-card:nth-child(4).animate-in {
    animation-delay: 0.4s;
}


/* No Gallery Items Message */
.no-gallery-items {
    padding: 60px 20px;
    text-align: center;
}

.no-gallery-items i {
    color: #ccc;
    margin-bottom: 20px;
}

.no-gallery-items h4 {
    color: #999;
    margin-bottom: 15px;
    font-weight: 500;
}

.no-gallery-items p {
    color: #aaa;
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Pagination Styling */
.pagination-wrapper {
    margin-top: 50px;
}

.pagination-wrapper .pagination {
    justify-content: center;
}

.pagination-wrapper .page-link {
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.pagination-wrapper .page-link:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-wrapper .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}


/* About Page Mobile Responsive Styles */
@media (max-width: 991px) {
    .about-hero-section .hero-title {
        font-size: 3rem;
    }

    .about-hero-section .hero-subtitle {
        font-size: 1.1rem;
    }

    /* General Content Wrapper Tablet */
    .content-wrapper {
        padding: 0 20px !important;
        margin-bottom: 2.5rem !important;
        text-align: center !important;
        transform: none !important;
        max-width: 100% !important;
    }

    .about-content-side {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }

    .index-content-wrapper {
        padding: 0 20px !important;
        text-align: center !important;
        max-width: 100% !important;
    }

    .index-about-content-side {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }

    .who-we-are-section .content-wrapper {
        padding-right: 0;
        margin-bottom: 3rem;
        text-align: center;
    }

    .who-we-are-section .section-title {
        font-size: 2.2rem;
    }

    .who-we-are-section .image-wrapper {
        text-align: center;
    }

    .who-we-are-section .about-image {
        max-width: 80%;
        height: auto;
    }
}

/* Extra Small Screens (575px and below) */
@media (max-width: 575px) {
    /* Who We Are Section Extra Small */
    .who-we-are-section {
        padding: 50px 0 !important;
    }

    /* General Content Wrapper Extra Small */
    .content-wrapper {
        padding: 0 5px !important;
        margin-bottom: 1rem !important;
        text-align: center !important;
        transform: none !important;
        max-width: 100% !important;
    }

    .about-content-side {
        padding: 0 !important;
    }

    .index-content-wrapper {
        padding: 0 5px !important;
        text-align: center !important;
        max-width: 100% !important;
    }

    .index-about-content-side {
        padding: 0 !important;
    }

    .who-we-are-section .content-wrapper {
        padding: 0 10px;
        margin-bottom: 1.5rem;
    }

    .who-we-are-section .section-title {
        font-size: 1.6rem;
        margin-bottom: 1.2rem;
        line-height: 1.2;
    }

    .who-we-are-section .section-badge {
        font-size: 11px;
        padding: 4px 10px;
        margin-bottom: 0.6rem;
    }

    .who-we-are-section .content-text p {
        font-size: 0.9rem !important;
        line-height: 1.4 !important;
        margin-bottom: 0.8rem !important;
        padding: 0 5px;
    }

    .who-we-are-section .about-image {
        height: 250px;
        max-width: 95%;
        margin: 15px auto 0;
    }

    /* Home Buyers Section Extra Small */
    .home-buyers-section {
        padding: 50px 0;
    }

    .section-center-header {
        margin-bottom: 30px;
        padding: 0 10px;
    }

    .center-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .center-icon-wrapper {
        margin-bottom: 15px;
    }

    .service-title {
        font-size: 1.5rem;
        line-height: 1.2;
    }

    .service-badge {
        padding: 5px 12px;
        font-size: 0.65rem;
        margin-bottom: 12px;
    }

    /* Services Grid Extra Small */
    .services-grid-layout {
        gap: 20px;
        padding: 0 10px;
    }

    .left-services .service-item,
    .right-services .service-item {
        padding: 15px 10px;
        gap: 10px;
    }

    .service-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        margin: 0 auto 12px auto;
    }

    .service-details h4 {
        font-size: 1rem;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .service-details p {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    /* Section Bottom Extra Small */
    .section-bottom {
        margin-top: 30px;
        padding: 0 10px;
    }

    .testimonial-box {
        padding: 15px 10px;
        margin-bottom: 20px;
    }

    .testimonial-text {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .testimonial-author {
        font-size: 0.75rem;
    }

    .service-cta .btn {
        padding: 10px 20px;
        font-size: 0.85rem;
        width: 100%;
        max-width: 250px;
    }
}
