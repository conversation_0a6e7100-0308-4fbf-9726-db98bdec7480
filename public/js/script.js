// Hestia <PERSON> - Main JavaScript File

document.addEventListener('DOMContentLoaded', function() {

    // Initialize all animations and interactions
    initScrollAnimations();
    initCounterAnimations();
    initSmoothScrolling();
    initFormValidation();
    initGalleryFiltering();

    // Scroll Animation Observer
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');

                    // Trigger counter animation for stat cards
                    if (entry.target.classList.contains('stat-card')) {
                        animateCounter(entry.target);
                    }

                    // Trigger counter animation for index stat cards
                    if (entry.target.classList.contains('index-stat-card')) {
                        animateIndexCounter(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observe all elements that need animation (excluding footer stats)
        const animateElements = document.querySelectorAll('.stat-card:not(.footer-stats .stat-card), .testimonial-card, .service-card, .feature-card, .blog-card, .index-stat-card, .gallery-item');
        animateElements.forEach(el => {
            observer.observe(el);
        });
    }

    // Counter Animation for Statistics
    function initCounterAnimations() {
        // Only target stat-number elements that are NOT in footer
        const counters = document.querySelectorAll('.stat-number:not(.footer-stats .stat-number)');

        counters.forEach(counter => {
            const originalText = counter.innerText;
            counter.setAttribute('data-target', originalText);

            // Only set to 0 if it contains numeric values and is not in footer
            const numericValue = parseInt(originalText.replace(/[^\d]/g, ''));
            if (!isNaN(numericValue) && numericValue > 0) {
                counter.innerText = '0';
            }
        });

        // Initialize index stat counters
        const indexCounters = document.querySelectorAll('.index-stat-number');
        indexCounters.forEach(counter => {
            const originalText = counter.innerText.trim();
            counter.setAttribute('data-target', originalText);
            counter.setAttribute('data-original', originalText);

            // Only set to 0 if it contains numeric values
            const numericValue = parseInt(originalText.replace(/[^\d]/g, ''));
            if (!isNaN(numericValue) && numericValue > 0) {
                counter.innerText = '0';
                counter.style.opacity = '1'; // Ensure visibility
            }
        });
    }

    function animateCounter(statCard) {
        const counter = statCard.querySelector('.stat-number');
        if (!counter) return;

        const originalText = counter.getAttribute('data-target') || counter.innerText;
        const numericValue = parseInt(originalText.replace(/[^\d]/g, ''));

        // Check if the stat contains numeric value
        if (!isNaN(numericValue) && numericValue > 0) {
            // Animate numeric values
            const duration = 2000; // 2 seconds
            const increment = numericValue / (duration / 16); // 60fps
            let current = 0;

            // Set initial value to 0 for animation
            counter.innerText = '0';

            const timer = setInterval(() => {
                current += increment;
                if (current >= numericValue) {
                    // Restore original text format (e.g., "98%", "100+", "15+")
                    counter.innerText = originalText;
                    clearInterval(timer);
                } else {
                    // Show counting animation with proper formatting
                    let displayValue = Math.floor(current);
                    if (originalText.includes('%')) {
                        counter.innerText = displayValue + '%';
                    } else if (originalText.includes('+')) {
                        counter.innerText = displayValue + '+';
                    } else {
                        counter.innerText = displayValue.toLocaleString();
                    }
                }
            }, 16);
        } else {
            // For non-numeric values like "Higher", "Long-Term", just show them with a fade-in effect
            counter.style.opacity = '0';
            counter.innerText = originalText;

            setTimeout(() => {
                counter.style.transition = 'opacity 0.8s ease';
                counter.style.opacity = '1';
            }, 300);
        }
    }

    // Counter Animation for Index Statistics
    function animateIndexCounter(indexStatCard) {
        const counter = indexStatCard.querySelector('.index-stat-number');
        if (!counter) return;

        // Prevent multiple animations on the same counter
        if (counter.hasAttribute('data-animated')) return;
        counter.setAttribute('data-animated', 'true');

        // Get the original text from data-original attribute
        const originalText = counter.getAttribute('data-original') || counter.getAttribute('data-target') || counter.innerText;
        const numericValue = parseInt(originalText.replace(/[^\d]/g, ''));

        // Check if the stat contains numeric value
        if (!isNaN(numericValue) && numericValue > 0) {
            // Start animation after a small delay for the card animation
            setTimeout(() => {
                // Animate numeric values
                const duration = 2000; // 2 seconds
                const steps = 60; // Number of animation steps
                const increment = numericValue / steps;
                let current = 0;
                let step = 0;

                // Set initial value to 0 for animation
                counter.innerText = '0';

                const timer = setInterval(() => {
                    step++;
                    current = Math.min(increment * step, numericValue);

                    if (step >= steps || current >= numericValue) {
                        // Animation complete - show final value
                        counter.innerText = originalText;
                        clearInterval(timer);
                    } else {
                        // Show counting animation with proper formatting
                        let displayValue = Math.floor(current);

                        // Handle different number formats based on original text
                        if (originalText.includes('+')) {
                            // For formats like "5000+" or "25+"
                            counter.innerText = displayValue.toLocaleString() + '+';
                        } else {
                            // For simple numbers like "15" or "500"
                            counter.innerText = displayValue.toLocaleString();
                        }
                    }
                }, duration / steps);
            }, 300); // Small delay to let card animation start first
        } else {
            // For non-numeric values, just show them with a fade-in effect
            counter.style.opacity = '0';
            counter.innerText = originalText;

            setTimeout(() => {
                counter.style.transition = 'opacity 1s ease';
                counter.style.opacity = '1';
            }, 400);
        }
    }

    // Make function available globally for testing
    window.animateIndexCounter = animateIndexCounter;

    // Smooth Scrolling for Anchor Links
    function initSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');

        links.forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href === '#') return;

                e.preventDefault();
                const target = document.querySelector(href);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Form Validation Enhancement
    function initFormValidation() {
        const forms = document.querySelectorAll('form');

        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');

            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('is-invalid')) {
                        validateField(this);
                    }
                });
            });
        });
    }

    function validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';

        // Remove existing validation classes
        field.classList.remove('is-valid', 'is-invalid');

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required';
        }

        // Email validation
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Please enter a valid email address';
            }
        }

        // Phone validation
        if (type === 'tel' && value) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/\s/g, ''))) {
                isValid = false;
                message = 'Please enter a valid phone number';
            }
        }

        // Apply validation result
        if (isValid && value) {
            field.classList.add('is-valid');
        } else if (!isValid) {
            field.classList.add('is-invalid');
            showFieldError(field, message);
        }

        return isValid;
    }

    function showFieldError(field, message) {
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    // Navbar Scroll Effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        }
    });

    // Gallery Filtering
    function initGalleryFiltering() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        const galleryItems = document.querySelectorAll('.gallery-item');

        if (filterBtns.length === 0 || galleryItems.length === 0) return;

        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');

                // Update active button
                filterBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Filter gallery items
                galleryItems.forEach(item => {
                    const category = item.getAttribute('data-category');

                    if (filter === 'all' || category === filter) {
                        item.style.display = 'block';
                        // Re-trigger animation
                        item.classList.remove('animate-in');
                        setTimeout(() => {
                            item.classList.add('animate-in');
                        }, 50);
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Update URL without page reload
                const url = new URL(window.location);
                if (filter === 'all') {
                    url.searchParams.delete('category');
                } else {
                    url.searchParams.set('category', filter);
                }
                window.history.pushState({}, '', url);
            });
        });

        // Set active filter based on URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const activeCategory = urlParams.get('category') || 'all';

        filterBtns.forEach(btn => {
            if (btn.getAttribute('data-filter') === activeCategory) {
                btn.click();
            }
        });
    }

    // Loading Animation
    window.addEventListener('load', function() {
        const loader = document.querySelector('.page-loader');
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => {
                loader.style.display = 'none';
            }, 300);
        }
    });

});

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Contact Form Submission (if needed)
function submitContactForm(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';

    // Here you would typically send the form data to your server
    // For now, we'll just simulate a successful submission
    setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;

        // Show success message
        showNotification('Message sent successfully!', 'success');
        form.reset();
    }, 2000);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}