// Home Page Contact Form Handler
document.addEventListener('DOMContentLoaded', function() {
    const homeContactForm = document.getElementById('homeContactForm');
    const newsletterForm = document.getElementById('newsletterForm');

    // Handle Contact Form
    if (homeContactForm) {
        handleContactForm();
    }

    // Handle Newsletter Form
    if (newsletterForm) {
        handleNewsletterForm();
    }

    function handleContactForm() {
    
    const submitBtn = homeContactForm.querySelector('button[type="submit"]');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnIcon = submitBtn.querySelector('.btn-icon i');

    // Form submission handler
    homeContactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = btnText.textContent;
        const originalIcon = btnIcon.className;
        
        btnText.textContent = 'Sending...';
        btnIcon.className = 'fas fa-spinner fa-spin';
        submitBtn.disabled = true;

        // Clear previous errors
        clearErrors();

        // Get form data
        const formData = new FormData(homeContactForm);

        // Submit form via AJAX
        fetch(homeContactForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showSuccessMessage(data.message);
                
                // Reset form
                homeContactForm.reset();
                
                // Reset floating labels
                resetFloatingLabels();
                
            } else {
                // Show validation errors
                if (data.errors) {
                    showValidationErrors(data.errors);
                } else {
                    showErrorMessage(data.message || 'An error occurred. Please try again.');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage('Sorry, there was an error submitting your form. Please try again or contact us directly.');
        })
        .finally(() => {
            // Reset button state
            btnText.textContent = originalText;
            btnIcon.className = originalIcon;
            submitBtn.disabled = false;
        });
    });

    // Clear all error messages
    function clearErrors() {
        const errorMessages = homeContactForm.querySelectorAll('.error-message');
        errorMessages.forEach(error => error.remove());
        
        const invalidInputs = homeContactForm.querySelectorAll('.is-invalid');
        invalidInputs.forEach(input => input.classList.remove('is-invalid'));
    }

    // Show validation errors
    function showValidationErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = homeContactForm.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');
                
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-danger mt-1';
                errorDiv.style.fontSize = '0.875rem';
                errorDiv.textContent = errors[fieldName][0];
                
                // Insert error message after the field's parent wrapper
                const wrapper = field.closest('.input-wrapper, .select-wrapper, .textarea-wrapper');
                if (wrapper) {
                    wrapper.appendChild(errorDiv);
                }
            }
        });
    }

    // Show success message
    function showSuccessMessage(message) {
        // Remove any existing alerts
        const existingAlerts = homeContactForm.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create success alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at the top of the form
        homeContactForm.insertBefore(alertDiv, homeContactForm.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
        
        // Scroll to the form to show the success message
        homeContactForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Show error message
    function showErrorMessage(message) {
        // Remove any existing alerts
        const existingAlerts = homeContactForm.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create error alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at the top of the form
        homeContactForm.insertBefore(alertDiv, homeContactForm.firstChild);
        
        // Scroll to the form to show the error message
        homeContactForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Reset floating labels
    function resetFloatingLabels() {
        const inputs = homeContactForm.querySelectorAll('.form-input, .form-textarea');
        inputs.forEach(input => {
            input.value = '';
            input.classList.remove('has-value');
        });
        
        const selects = homeContactForm.querySelectorAll('.form-select');
        selects.forEach(select => {
            select.selectedIndex = 0;
        });
    }

    // Handle floating labels
    const formInputs = homeContactForm.querySelectorAll('.form-input, .form-textarea');
    formInputs.forEach(input => {
        // Check on page load
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }

        // Check on input
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Check on focus/blur
        input.addEventListener('focus', function() {
            this.classList.add('has-value');
        });

        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.remove('has-value');
            }
        });
    });

    // Mobile number validation
    const mobileInput = homeContactForm.querySelector('input[name="mobile"]');
    if (mobileInput) {
        mobileInput.addEventListener('input', function() {
            // Remove non-numeric characters
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // Limit to 10 digits
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });
    }

    // Form field animations
    const inputWrappers = homeContactForm.querySelectorAll('.input-wrapper, .textarea-wrapper');
    inputWrappers.forEach(wrapper => {
        const input = wrapper.querySelector('.form-input, .form-textarea');
        const line = wrapper.querySelector('.input-line');
        
        if (input && line) {
            input.addEventListener('focus', function() {
                line.style.transform = 'scaleX(1)';
            });
            
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    line.style.transform = 'scaleX(0)';
                }
            });
        }
    });

    // Handle select field animations
    const selectWrappers = homeContactForm.querySelectorAll('.select-wrapper');
    selectWrappers.forEach(wrapper => {
        const select = wrapper.querySelector('.form-select');
        const line = wrapper.querySelector('.input-line');
        
        if (select && line) {
            select.addEventListener('focus', function() {
                line.style.transform = 'scaleX(1)';
            });
            
            select.addEventListener('blur', function() {
                if (this.value === '') {
                    line.style.transform = 'scaleX(0)';
                }
            });
            
            select.addEventListener('change', function() {
                if (this.value !== '') {
                    line.style.transform = 'scaleX(1)';
                } else {
                    line.style.transform = 'scaleX(0)';
                }
            });
        }
    });
    } // End of handleContactForm

    function handleNewsletterForm() {
        const submitBtn = newsletterForm.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnIcon = submitBtn.querySelector('.btn-icon i');
        const emailInput = newsletterForm.querySelector('input[name="email"]');

        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const originalText = btnText.textContent;
            const originalIcon = btnIcon.className;

            btnText.textContent = 'Subscribing...';
            btnIcon.className = 'fas fa-spinner fa-spin';
            submitBtn.disabled = true;

            // Clear previous messages
            clearNewsletterMessages();

            // Get form data
            const formData = new FormData(newsletterForm);

            // Submit form via AJAX
            fetch(newsletterForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNewsletterMessage(data.message, 'success');
                    emailInput.value = '';
                } else {
                    showNewsletterMessage(data.message || 'An error occurred. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNewsletterMessage('Sorry, there was an error. Please try again later.', 'error');
            })
            .finally(() => {
                // Reset button state
                btnText.textContent = originalText;
                btnIcon.className = originalIcon;
                submitBtn.disabled = false;
            });
        });

        function clearNewsletterMessages() {
            const existingMessages = newsletterForm.parentNode.querySelectorAll('.newsletter-message');
            existingMessages.forEach(msg => msg.remove());
        }

        function showNewsletterMessage(message, type) {
            clearNewsletterMessages();

            const messageDiv = document.createElement('div');
            messageDiv.className = `newsletter-message alert alert-${type === 'success' ? 'success' : 'danger'} mt-3`;
            messageDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            `;

            newsletterForm.parentNode.appendChild(messageDiv);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }
    } // End of handleNewsletterForm
});
