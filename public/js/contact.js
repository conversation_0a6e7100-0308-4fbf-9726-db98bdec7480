// Contact Form Handler
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    const submitBtn = contactForm.querySelector('button[type="submit"]');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnIcon = submitBtn.querySelector('.btn-icon i');

    // Form submission handler
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = btnText.textContent;
        const originalIcon = btnIcon.className;
        
        btnText.textContent = 'Sending...';
        btnIcon.className = 'fas fa-spinner fa-spin';
        submitBtn.disabled = true;

        // Clear previous errors
        clearErrors();

        // Get form data
        const formData = new FormData(contactForm);

        // Submit form via AJAX
        fetch(contactForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showSuccessMessage(data.message);
                
                // Reset form
                contactForm.reset();
                
                // Reset floating labels
                resetFloatingLabels();
                
            } else {
                // Show validation errors
                if (data.errors) {
                    showValidationErrors(data.errors);
                } else {
                    showErrorMessage(data.message || 'An error occurred. Please try again.');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage('Sorry, there was an error submitting your form. Please try again or contact us directly.');
        })
        .finally(() => {
            // Reset button state
            btnText.textContent = originalText;
            btnIcon.className = originalIcon;
            submitBtn.disabled = false;
        });
    });

    // Clear all error messages
    function clearErrors() {
        const errorMessages = contactForm.querySelectorAll('.error-message');
        errorMessages.forEach(error => error.remove());
        
        const invalidInputs = contactForm.querySelectorAll('.is-invalid');
        invalidInputs.forEach(input => input.classList.remove('is-invalid'));
    }

    // Show validation errors
    function showValidationErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = contactForm.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');
                
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-danger mt-1';
                errorDiv.style.fontSize = '0.875rem';
                errorDiv.textContent = errors[fieldName][0];
                
                // Insert error message after the field's parent wrapper
                const wrapper = field.closest('.input-wrapper, .select-wrapper, .textarea-wrapper');
                if (wrapper) {
                    wrapper.appendChild(errorDiv);
                }
            }
        });
    }

    // Show success message
    function showSuccessMessage(message) {
        // Remove any existing alerts
        const existingAlerts = contactForm.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create success alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at the top of the form
        contactForm.insertBefore(alertDiv, contactForm.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Show error message
    function showErrorMessage(message) {
        // Remove any existing alerts
        const existingAlerts = contactForm.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create error alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at the top of the form
        contactForm.insertBefore(alertDiv, contactForm.firstChild);
    }

    // Reset floating labels
    function resetFloatingLabels() {
        const inputs = contactForm.querySelectorAll('.form-input, .form-textarea');
        inputs.forEach(input => {
            input.value = '';
            input.classList.remove('has-value');
        });
        
        const selects = contactForm.querySelectorAll('.form-select');
        selects.forEach(select => {
            select.selectedIndex = 0;
        });
    }

    // Handle floating labels
    const formInputs = contactForm.querySelectorAll('.form-input, .form-textarea');
    formInputs.forEach(input => {
        // Check on page load
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }

        // Check on input
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Check on focus/blur
        input.addEventListener('focus', function() {
            this.classList.add('has-value');
        });

        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.remove('has-value');
            }
        });
    });

    // Mobile number validation
    const mobileInput = contactForm.querySelector('input[name="mobile"]');
    if (mobileInput) {
        mobileInput.addEventListener('input', function() {
            // Remove non-numeric characters
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // Limit to 10 digits
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });
    }

    // Form field animations
    const inputWrappers = contactForm.querySelectorAll('.input-wrapper, .textarea-wrapper');
    inputWrappers.forEach(wrapper => {
        const input = wrapper.querySelector('.form-input, .form-textarea');
        const line = wrapper.querySelector('.input-line');
        
        if (input && line) {
            input.addEventListener('focus', function() {
                line.style.transform = 'scaleX(1)';
            });
            
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    line.style.transform = 'scaleX(0)';
                }
            });
        }
    });
});
