// Projects Search & Filter JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initProjectsFilter();
});

function initProjectsFilter() {
    // Initialize search functionality
    initSearchFunctionality();
    
    // Initialize view toggle
    initViewToggle();
    
    // Initialize filter animations
    initFilterAnimations();
    
    // Initialize auto-submit for filters
    initAutoSubmit();
    
    // Initialize loading states
    initLoadingStates();
}

// Search Functionality
function initSearchFunctionality() {
    const searchInput = document.getElementById('searchInput');
    const searchForm = document.getElementById('searchForm');
    
    if (searchInput && searchForm) {
        // Auto-search with debounce
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    showLoadingOverlay();
                    searchForm.submit();
                }
            }, 500);
        });
        
        // Search on Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                showLoadingOverlay();
                searchForm.submit();
            }
        });
    }
}

// Clear Search Function
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
        showLoadingOverlay();
        document.getElementById('searchForm').submit();
    }
}

// View Toggle Functionality
function initViewToggle() {
    const viewButtons = document.querySelectorAll('.view-btn');
    const projectsGrid = document.getElementById('projectsGrid');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            viewButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Toggle view
            const view = this.dataset.view;
            if (view === 'list') {
                projectsGrid.classList.add('list-view');
                // Reorganize for list view
                reorganizeForListView();
            } else {
                projectsGrid.classList.remove('list-view');
                // Reorganize for grid view
                reorganizeForGridView();
            }
            
            // Save preference
            localStorage.setItem('projectsView', view);
        });
    });
    
    // Load saved preference
    const savedView = localStorage.getItem('projectsView');
    if (savedView === 'list') {
        document.querySelector('[data-view="list"]').click();
    }
}

// Reorganize for List View
function reorganizeForListView() {
    const projectItems = document.querySelectorAll('.project-item');
    const projectsRow = document.getElementById('projectsRow');
    
    projectItems.forEach(item => {
        item.className = 'col-12 mb-4 project-item';
        // Keep data attributes
        const dataAttrs = ['category', 'location', 'type', 'status', 'price', 'title'];
        dataAttrs.forEach(attr => {
            const value = item.dataset[attr];
            if (value) item.dataset[attr] = value;
        });
    });
}

// Reorganize for Grid View
function reorganizeForGridView() {
    const projectItems = document.querySelectorAll('.project-item');
    
    projectItems.forEach(item => {
        item.className = 'col-lg-4 col-md-6 mb-4 project-item';
        // Keep data attributes
        const dataAttrs = ['category', 'location', 'type', 'status', 'price', 'title'];
        dataAttrs.forEach(attr => {
            const value = item.dataset[attr];
            if (value) item.dataset[attr] = value;
        });
    });
}

// Filter Animations
function initFilterAnimations() {
    const projectItems = document.querySelectorAll('.project-item');
    
    // Animate project cards on load
    projectItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Auto-submit for filters
function initAutoSubmit() {
    const filterSelects = document.querySelectorAll('.filter-select, .sort-select');
    
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            showLoadingOverlay();
            // Small delay to show loading state
            setTimeout(() => {
                this.form.submit();
            }, 200);
        });
    });
}

// Loading States
function initLoadingStates() {
    // Create loading overlay if it doesn't exist
    if (!document.querySelector('.loading-overlay')) {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-spinner"></div>
        `;
        document.body.appendChild(loadingOverlay);
    }
}

// Show Loading Overlay
function showLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.add('show');
    }
}

// Hide Loading Overlay
function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// Advanced Search Functions
function searchProjects(query) {
    const projectItems = document.querySelectorAll('.project-item');
    const searchTerm = query.toLowerCase();
    let visibleCount = 0;
    
    projectItems.forEach(item => {
        const title = item.dataset.title || '';
        const category = item.dataset.category || '';
        const location = item.dataset.location || '';
        const type = item.dataset.type || '';
        
        const searchText = `${title} ${category} ${location} ${type}`.toLowerCase();
        
        if (searchText.includes(searchTerm) || searchTerm === '') {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // Update results count
    updateResultsCount(visibleCount);
    
    // Show no results message if needed
    toggleNoResultsMessage(visibleCount === 0);
}

// Update Results Count
function updateResultsCount(count) {
    const resultsCount = document.querySelector('.results-count');
    if (resultsCount) {
        resultsCount.textContent = `${count} Projects Found`;
    }
}

// Toggle No Results Message
function toggleNoResultsMessage(show) {
    let noResultsMsg = document.querySelector('.no-results-message');
    
    if (show && !noResultsMsg) {
        noResultsMsg = document.createElement('div');
        noResultsMsg.className = 'no-results-message text-center py-5';
        noResultsMsg.innerHTML = `
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h3 class="text-muted">No Projects Found</h3>
            <p class="text-muted">Try adjusting your search criteria or filters.</p>
            <button class="btn btn-primary" onclick="clearAllFilters()">
                <i class="fas fa-refresh me-2"></i>Clear All Filters
            </button>
        `;
        
        const projectsGrid = document.getElementById('projectsGrid');
        if (projectsGrid) {
            projectsGrid.appendChild(noResultsMsg);
        }
    } else if (!show && noResultsMsg) {
        noResultsMsg.remove();
    }
}

// Clear All Filters
function clearAllFilters() {
    // Clear search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
    }
    
    // Reset all select filters
    const filterSelects = document.querySelectorAll('.filter-select, .sort-select');
    filterSelects.forEach(select => {
        if (select.name !== 'sort_by') {
            select.value = 'all';
        } else {
            select.value = 'latest';
        }
    });
    
    // Submit form
    showLoadingOverlay();
    document.getElementById('searchForm').submit();
}

// Smooth Scroll to Results
function scrollToResults() {
    const projectsGrid = document.getElementById('projectsGrid');
    if (projectsGrid) {
        projectsGrid.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Initialize on page load
window.addEventListener('load', function() {
    hideLoadingOverlay();
    
    // Scroll to results if there are filters applied
    const hasFilters = new URLSearchParams(window.location.search).toString();
    if (hasFilters) {
        setTimeout(scrollToResults, 500);
    }
});

// Handle browser back/forward
window.addEventListener('popstate', function() {
    showLoadingOverlay();
    setTimeout(() => {
        location.reload();
    }, 200);
});

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global access
window.clearSearch = clearSearch;
window.clearAllFilters = clearAllFilters;
window.searchProjects = searchProjects;
