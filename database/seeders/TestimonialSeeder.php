<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Clear existing testimonials
        Testimonial::truncate();

        $testimonials = [
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'designation' => 'IT Professional',
                'company' => 'TCS',
                'testimonial_text' => '<PERSON><PERSON><PERSON> helped me navigate the Pune real estate market like a pro. I was impressed with their transparency and personalized service. They found me the perfect 3BHK apartment in Baner within my budget.',
                'rating' => 5,
                'is_active' => true,
                'order' => 1,
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'designation' => 'Investment Advisor',
                'company' => 'HDFC Securities',
                'testimonial_text' => 'As an investor, I appreciate the clarity and ROI-focused advice I received. Highly recommend their services! They helped me identify high-growth potential areas in Pune for my property investments.',
                'rating' => 5,
                'is_active' => true,
                'order' => 2,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'designation' => 'Business Owner',
                'company' => 'Sharma Enterprises',
                'testimonial_text' => 'Excellent service from start to finish. The team at Hestia Abodes made my commercial property purchase seamless. Their market knowledge and negotiation skills saved me both time and money.',
                'rating' => 5,
                'is_active' => true,
                'order' => 3,
            ],
            [
                'name' => 'Priya Joshi',
                'designation' => 'Software Engineer',
                'company' => 'Infosys',
                'testimonial_text' => 'Professional, reliable, and trustworthy. They understood my requirements perfectly and showed me only relevant properties. The entire documentation process was handled efficiently.',
                'rating' => 5,
                'is_active' => true,
                'order' => 4,
            ],
            [
                'name' => 'Vikram Patil',
                'designation' => 'Marketing Manager',
                'company' => 'Bajaj Auto',
                'testimonial_text' => 'Outstanding support throughout the property buying journey. Their team provided valuable insights about upcoming infrastructure developments that influenced my decision positively.',
                'rating' => 5,
                'is_active' => true,
                'order' => 5,
            ],
            [
                'name' => 'Neha Agarwal',
                'designation' => 'Doctor',
                'company' => 'Ruby Hall Clinic',
                'testimonial_text' => 'Hestia Abodes made my first home purchase stress-free. They explained every step clearly and ensured all legal aspects were properly handled. Truly professional service.',
                'rating' => 5,
                'is_active' => true,
                'order' => 6,
            ],
            [
                'name' => 'Amit Deshmukh',
                'designation' => 'Financial Analyst',
                'company' => 'Kotak Mahindra Bank',
                'testimonial_text' => 'Impressed with their market analysis and property valuation expertise. They helped me make an informed decision and negotiate the best price for my villa in Kharadi.',
                'rating' => 4,
                'is_active' => true,
                'order' => 7,
            ],
            [
                'name' => 'Kavita Singh',
                'designation' => 'HR Director',
                'company' => 'Wipro',
                'testimonial_text' => 'Exceptional customer service and deep knowledge of Pune real estate. They found me the perfect property that matched all my criteria within the specified timeline.',
                'rating' => 5,
                'is_active' => true,
                'order' => 8,
            ],
            [
                'name' => 'Rohit Kulkarni',
                'designation' => 'Entrepreneur',
                'company' => 'Tech Startup',
                'testimonial_text' => 'Great experience working with Hestia Abodes. Their team is knowledgeable, responsive, and genuinely cares about finding the right property for their clients.',
                'rating' => 5,
                'is_active' => true,
                'order' => 9,
            ],
            [
                'name' => 'Sunita Rao',
                'designation' => 'Teacher',
                'company' => 'Delhi Public School',
                'testimonial_text' => 'They made the complex process of buying a home simple and transparent. Excellent communication and follow-up throughout the entire process. Highly satisfied with their service.',
                'rating' => 5,
                'is_active' => true,
                'order' => 10,
            ],
            [
                'name' => 'Manish Jain',
                'designation' => 'Chartered Accountant',
                'company' => 'Self Employed',
                'testimonial_text' => 'Professional approach and honest advice. They helped me understand the legal and financial aspects clearly before making the investment. Trustworthy team.',
                'rating' => 4,
                'is_active' => true,
                'order' => 11,
            ],
            [
                'name' => 'Deepika Nair',
                'designation' => 'Product Manager',
                'company' => 'Microsoft',
                'testimonial_text' => 'Excellent service quality and attention to detail. They went above and beyond to ensure I found the perfect home in Pune. Their post-purchase support is also commendable.',
                'rating' => 5,
                'is_active' => true,
                'order' => 12,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create(array_merge($testimonial, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        $this->command->info('12 testimonials seeded successfully!');
    }
}
