<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogCategory;

class BlogCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Market Analysis',
                'description' => 'In-depth analysis of real estate market trends, pricing, and forecasts',
                'color' => '#007bff',
                'status' => true
            ],
            [
                'name' => 'Buying Guide',
                'description' => 'Comprehensive guides to help buyers make informed property decisions',
                'color' => '#28a745',
                'status' => true
            ],
            [
                'name' => 'Investment Tips',
                'description' => 'Expert advice on real estate investment strategies and opportunities',
                'color' => '#ffc107',
                'status' => true
            ],
            [
                'name' => 'Legal Guide',
                'description' => 'Legal aspects, documentation, and compliance in real estate transactions',
                'color' => '#dc3545',
                'status' => true
            ],
            [
                'name' => 'Property News',
                'description' => 'Latest news and updates from the real estate industry',
                'color' => '#6f42c1',
                'status' => true
            ],
            [
                'name' => 'Home Loans',
                'description' => 'Information about home loans, EMI calculations, and financing options',
                'color' => '#fd7e14',
                'status' => true
            ]
        ];

        foreach ($categories as $category) {
            BlogCategory::create($category);
        }
    }
}
