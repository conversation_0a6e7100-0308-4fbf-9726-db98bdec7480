<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProjectEnquiry;
use App\Models\Project;

class ProjectEnquirySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some projects to associate with enquiries
        $projects = Project::take(3)->get();

        if ($projects->isEmpty()) {
            $this->command->info('No projects found. Please seed projects first.');
            return;
        }

        $enquiries = [
            [
                'project_id' => $projects->first()->id,
                'project_name' => $projects->first()->title,
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543210',
                'interest' => 'site_visit',
                'message' => 'I am interested in visiting the site this weekend. Please let me know the available time slots.',
                'status' => 'new',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'project_id' => $projects->first()->id,
                'project_name' => $projects->first()->title,
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543211',
                'interest' => 'price_details',
                'message' => 'Could you please share the detailed pricing and payment plans for 2BHK units?',
                'status' => 'contacted',
                'contacted_at' => now()->subHours(6),
                'admin_notes' => 'Sent pricing details via email. Customer is interested in 2BHK on 5th floor.',
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subHours(6),
            ],
            [
                'project_id' => $projects->count() > 1 ? $projects->get(1)->id : $projects->first()->id,
                'project_name' => $projects->count() > 1 ? $projects->get(1)->title : $projects->first()->title,
                'name' => 'Amit Patel',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543212',
                'interest' => 'loan_assistance',
                'message' => 'I need help with home loan processing. Do you provide assistance with documentation?',
                'status' => 'interested',
                'contacted_at' => now()->subDays(1),
                'admin_notes' => 'Connected with our loan partner. Customer has good credit score.',
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(1),
            ],
            [
                'project_id' => $projects->count() > 1 ? $projects->get(1)->id : $projects->first()->id,
                'project_name' => $projects->count() > 1 ? $projects->get(1)->title : $projects->first()->title,
                'name' => 'Sneha Reddy',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543213',
                'interest' => 'floor_plans',
                'message' => 'Please share the floor plans for 3BHK units. Also, is car parking included?',
                'status' => 'converted',
                'contacted_at' => now()->subDays(5),
                'admin_notes' => 'Customer booked 3BHK unit on 8th floor. Token amount received.',
                'created_at' => now()->subWeek(),
                'updated_at' => now()->subDays(2),
            ],
            [
                'project_id' => $projects->count() > 2 ? $projects->get(2)->id : $projects->first()->id,
                'project_name' => $projects->count() > 2 ? $projects->get(2)->title : $projects->first()->title,
                'name' => 'Vikram Singh',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543214',
                'interest' => 'site_visit',
                'message' => 'I would like to visit the project with my family. What are the amenities available?',
                'status' => 'not_interested',
                'contacted_at' => now()->subDays(4),
                'admin_notes' => 'Customer visited but found the location too far from their workplace.',
                'created_at' => now()->subDays(6),
                'updated_at' => now()->subDays(3),
            ],
            [
                'project_id' => $projects->first()->id,
                'project_name' => $projects->first()->title,
                'name' => 'Anita Gupta',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543215',
                'interest' => 'price_details',
                'message' => null,
                'status' => 'new',
                'created_at' => now()->subHours(3),
                'updated_at' => now()->subHours(3),
            ],
        ];

        foreach ($enquiries as $enquiry) {
            ProjectEnquiry::create($enquiry);
        }

        $this->command->info('Project enquiries seeded successfully!');
    }
}
