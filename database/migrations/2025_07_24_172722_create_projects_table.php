<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->foreignId('project_category_id')->constrained()->onDelete('cascade');
            $table->string('location');
            $table->string('type');
            $table->string('status');
            $table->string('price_range');
            $table->json('configurations'); // Array of configurations like ['2 BHK', '3 BHK']
            $table->string('area_range');
            $table->text('description');
            $table->json('amenities'); // Array of amenities
            $table->json('images'); // Array of image paths
            $table->string('developer');
            $table->string('rera_number')->nullable();
            $table->string('possession');
            $table->boolean('featured')->default(false);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
