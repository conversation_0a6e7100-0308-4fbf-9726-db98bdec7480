<?php

/**
 * Laravel Application Entry Point
 * Handles both static files and Laravel routing
 */

// Get the request URI without query string
$uri = strtok($_SERVER['REQUEST_URI'], '?');

// Remove the base path
$basePath = dirname($_SERVER['SCRIPT_NAME']);
if ($basePath !== '/') {
    $uri = substr($uri, strlen($basePath));
}
$uri = '/' . ltrim($uri, '/');

// Define public directory
$publicDir = __DIR__ . '/public';

// Check if this is a request for a static file
if ($uri !== '/' && $uri !== '/index.php') {
    $filePath = $publicDir . $uri;

    if (file_exists($filePath) && is_file($filePath)) {
        // Get file extension
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        // Set content type
        $contentTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'html' => 'text/html',
            'txt' => 'text/plain',
            'json' => 'application/json',
            'pdf' => 'application/pdf'
        ];

        if (isset($contentTypes[$extension])) {
            header('Content-Type: ' . $contentTypes[$extension]);
        }

        // Output the file
        readfile($filePath);
        exit;
    }
}

// For Laravel requests, include the public index.php
require_once $publicDir . '/index.php';
