<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\SitemapController;

// Website Routes (Main pages)
Route::get('/', [WebsiteController::class, 'index'])->name('home');
Route::get('/our-services', [WebsiteController::class, 'services'])->name('services');
Route::get('/contact-us', [WebsiteController::class, 'contact'])->name('contact');
Route::get('/about-us', [WebsiteController::class, 'about'])->name('about');
Route::get('/how-we-work', [WebsiteController::class, 'howWeWork'])->name('how-we-work');
Route::get('/why-choose-us', [WebsiteController::class, 'whyChooseUs'])->name('why-choose-us');
Route::get('/projects', [WebsiteController::class, 'projects'])->name('projects');
Route::get('/projectsold', [WebsiteController::class, 'projectsold'])->name('projectsold');

Route::get('/project-details/{slug}', [WebsiteController::class, 'projectDetails'])->name('project-details');
Route::get('/project-detailss/{slug}', [WebsiteController::class, 'projectDetailss'])->name('project-detailss');

Route::get('/blog', [WebsiteController::class, 'blog'])->name('blog');
Route::get('/blog-details/{slug}', [WebsiteController::class, 'blogDetails'])->name('blog-details');
Route::get('/privacy-policy', [WebsiteController::class, 'privacyPolicy'])->name('privacy-policy');

// Sitemap routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap.xml');
Route::get('/sitemap', [SitemapController::class, 'humanReadable'])->name('sitemap');

// Contact form submission
Route::post('/contact', [App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');

// Project enquiry submission
Route::post('/project-enquiry', [App\Http\Controllers\ProjectEnquiryController::class, 'store'])->name('project-enquiry.store');

// Newsletter subscription
Route::post('/newsletter/subscribe', [App\Http\Controllers\NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
Route::get('/newsletter/unsubscribe/{email?}', [App\Http\Controllers\NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');
Route::get('/gallery', [WebsiteController::class, 'gallery'])->name('gallery');

// Career page
Route::get('/careers', [App\Http\Controllers\CareerController::class, 'index'])->name('careers');
Route::post('/careers', [App\Http\Controllers\CareerController::class, 'store'])->name('careers.store');

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Auth routes (accessible without authentication)
    Route::get('/login', [App\Http\Controllers\Admin\AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [App\Http\Controllers\Admin\AuthController::class, 'login']);

    // Protected admin routes
    Route::middleware('admin.auth')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
        Route::post('/logout', [App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('logout');

        // Profile routes
        Route::get('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('profile');
        Route::put('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('profile.update');

        // Password reset routes
        Route::get('/reset-password', [App\Http\Controllers\Admin\PasswordController::class, 'showResetForm'])->name('password.reset');
        Route::put('/reset-password', [App\Http\Controllers\Admin\PasswordController::class, 'reset'])->name('password.update');

        // Project Category CRUD
        Route::resource('project-categories', App\Http\Controllers\Admin\ProjectCategoryController::class);

        // Projects CRUD
        Route::resource('projects', App\Http\Controllers\Admin\ProjectController::class);

        // Gallery CRUD
        Route::resource('galleries', App\Http\Controllers\Admin\GalleryController::class);

        // Blog Category CRUD
        Route::resource('blog-categories', App\Http\Controllers\Admin\BlogCategoryController::class);

        // Blog CRUD
        Route::resource('blogs', App\Http\Controllers\Admin\BlogController::class);

        // Hero Slider CRUD
        Route::resource('hero-sliders', App\Http\Controllers\Admin\HeroSliderController::class);

        // Page Header CRUD
        Route::resource('page-headers', App\Http\Controllers\Admin\PageHeaderController::class);

        // Contact Submissions Management
        Route::resource('contact-submissions', App\Http\Controllers\Admin\ContactSubmissionController::class)->except(['create', 'store']);
        Route::post('contact-submissions/bulk-update', [App\Http\Controllers\Admin\ContactSubmissionController::class, 'bulkUpdate'])->name('contact-submissions.bulk-update');

        // Project Enquiries Management
        Route::resource('project-enquiries', App\Http\Controllers\Admin\ProjectEnquiryController::class)->except(['create', 'store']);

        // Career Applications Management
        Route::resource('career-applications', App\Http\Controllers\Admin\CareerApplicationController::class)->except(['create', 'store']);
        Route::post('career-applications/bulk-update', [App\Http\Controllers\Admin\CareerApplicationController::class, 'bulkUpdate'])->name('career-applications.bulk-update');
        Route::get('career-applications/{careerApplication}/download-resume', [App\Http\Controllers\Admin\CareerApplicationController::class, 'downloadResume'])->name('career-applications.download-resume');

    // Newsletter Subscriptions Management
    Route::resource('newsletter-subscriptions', App\Http\Controllers\Admin\NewsletterSubscriptionController::class);
    Route::post('newsletter-subscriptions/bulk-update', [App\Http\Controllers\Admin\NewsletterSubscriptionController::class, 'bulkUpdate'])->name('newsletter-subscriptions.bulk-update');
    Route::get('newsletter-subscriptions-export', [App\Http\Controllers\Admin\NewsletterSubscriptionController::class, 'export'])->name('newsletter-subscriptions.export');

    });
});